function [X_optimal, convergence_info] = Sparse_LowRank_Regularization(X_input, signal, params)
% Sparse_LowRank_Regularization - 稀疏-低秩耦合正则化模块
%
% 核心思想：
% 1. 稀疏性：散射点在时-相位高维网格上呈K-稀疏
% 2. 低秩：跨距离帧形成Hankel矩阵，秩≈目标散射点数
% 3. 联合代价：min λ||X||₁ + (1-λ)||X||_* s.t. ||Y - F_PCFT(X)||₂² ≤ η
%
% 输入:
%   X_input - 输入频谱 (PCFT变换结果)
%   signal  - 原始信号 (用于数据保真度)
%   params  - 算法参数
%
% 输出:
%   X_optimal        - 优化后的频谱
%   convergence_info - 收敛信息

%% 参数初始化
if nargin < 3
    params = get_default_sparse_lr_params();
end

N = length(X_input);
fprintf('稀疏-低秩耦合正则化：数据维度 %d\n', N);

%% 算法选择
switch params.algorithm
    case 'admm'
        [X_optimal, convergence_info] = sparse_lr_admm_solver(X_input, signal, params);
    case 'condat_vu'
        [X_optimal, convergence_info] = sparse_lr_condat_vu_solver(X_input, signal, params);
    case 'alternating'
        [X_optimal, convergence_info] = sparse_lr_alternating_solver(X_input, signal, params);
    otherwise
        error('不支持的算法: %s', params.algorithm);
end

fprintf('稀疏-低秩优化完成，最终误差: %.2e\n', convergence_info.final_error);
end

%% ADMM求解器
function [X_optimal, convergence_info] = sparse_lr_admm_solver(X_input, signal, params)
% ADMM求解稀疏-低秩耦合问题

N = length(X_input);
lambda_sparse = params.lambda_sparse;
lambda_lowrank = params.lambda_lowrank;
max_iter = params.max_iterations;
tolerance = params.tolerance;
rho = params.rho;

% 初始化变量
X = X_input;                    % 主变量
Z_sparse = X_input;             % 稀疏辅助变量
Y_lowrank = X_input;            % 低秩辅助变量
U_sparse = zeros(size(X));      % 对偶变量1
U_lowrank = zeros(size(X));     % 对偶变量2

% Hankel矩阵参数
hankel_size = min(params.hankel_size, floor(N/2));

% 收敛历史
convergence_history = zeros(max_iter, 1);
objective_history = zeros(max_iter, 1);

fprintf('ADMM稀疏-低秩求解器启动...\n');

for iter = 1:max_iter
    X_prev = X;
    
    %% 子问题1：更新X (数据保真度 + ADMM增广项)
    % X = argmin ||X - X_input||² + ρ/2||X - Z_sparse + U_sparse||² + ρ/2||X - Y_lowrank + U_lowrank||²
    numerator = X_input + rho * (Z_sparse - U_sparse) + rho * (Y_lowrank - U_lowrank);
    denominator = 1 + 2 * rho;
    X = numerator / denominator;
    
    %% 子问题2：更新Z_sparse (稀疏约束)
    % Z_sparse = argmin λ_sparse||Z||₁ + ρ/2||X - Z + U_sparse||²
    Z_sparse = soft_threshold_complex(X + U_sparse, lambda_sparse / rho);
    
    %% 子问题3：更新Y_lowrank (低秩约束)
    % Y_lowrank = argmin λ_lowrank||Hankel(Y)||_* + ρ/2||X - Y + U_lowrank||²
    Y_lowrank = update_lowrank_hankel_constraint(X + U_lowrank, lambda_lowrank / rho, hankel_size);
    
    %% 对偶变量更新
    U_sparse = U_sparse + (X - Z_sparse);
    U_lowrank = U_lowrank + (X - Y_lowrank);
    
    %% 计算目标函数值
    data_fidelity = 0.5 * norm(X - X_input)^2;
    sparse_penalty = lambda_sparse * norm(Z_sparse, 1);
    lowrank_penalty = lambda_lowrank * nuclear_norm_hankel(Y_lowrank, hankel_size);
    objective_val = data_fidelity + sparse_penalty + lowrank_penalty;
    
    %% 收敛检查
    primal_residual_sparse = norm(X - Z_sparse);
    primal_residual_lowrank = norm(X - Y_lowrank);
    total_primal_residual = primal_residual_sparse + primal_residual_lowrank;
    
    convergence_history(iter) = total_primal_residual;
    objective_history(iter) = objective_val;
    
    if total_primal_residual < tolerance
        fprintf('  ADMM在第 %d 次迭代收敛\n', iter);
        break;
    end
    
    % 自适应调整rho
    if iter > 10 && mod(iter, 10) == 0
        if primal_residual_sparse > 10 * primal_residual_lowrank
            rho = rho * 2;
        elseif primal_residual_lowrank > 10 * primal_residual_sparse
            rho = rho / 2;
        end
    end
    
    % 进度显示
    if mod(iter, 50) == 0
        fprintf('  迭代 %d: 目标函数 = %.6f, 残差 = %.2e\n', iter, objective_val, total_primal_residual);
    end
end

X_optimal = X;

% 收敛信息
convergence_info.converged = (total_primal_residual < tolerance);
convergence_info.iterations = iter;
convergence_info.final_error = total_primal_residual;
convergence_info.convergence_history = convergence_history(1:iter);
convergence_info.objective_history = objective_history(1:iter);
end

%% Condat-Vũ原始-对偶迭代求解器
function [X_optimal, convergence_info] = sparse_lr_condat_vu_solver(X_input, signal, params)
% Condat-Vũ原始-对偶迭代方法

N = length(X_input);
lambda_sparse = params.lambda_sparse;
lambda_lowrank = params.lambda_lowrank;
max_iter = params.max_iterations;
tolerance = params.tolerance;

% 算法参数
tau = params.tau;     % 原始步长
sigma = params.sigma; % 对偶步长
theta = params.theta; % 外推参数

% 初始化
x = X_input;
x_bar = x;
y_sparse = zeros(size(x));      % 稀疏对偶变量
y_lowrank = zeros(size(x));     % 低秩对偶变量

% Hankel矩阵参数
hankel_size = min(params.hankel_size, floor(N/2));

% 收敛历史
convergence_history = zeros(max_iter, 1);
objective_history = zeros(max_iter, 1);

fprintf('Condat-Vũ原始-对偶求解器启动...\n');

for iter = 1:max_iter
    x_prev = x;
    
    %% 对偶变量更新
    % 稀疏对偶更新
    y_sparse_temp = y_sparse + sigma * x_bar;
    y_sparse = y_sparse_temp - sigma * prox_l1_dual(y_sparse_temp / sigma, lambda_sparse / sigma);
    
    % 低秩对偶更新
    y_lowrank_temp = y_lowrank + sigma * hankel_operator(x_bar, hankel_size);
    y_lowrank = y_lowrank_temp - sigma * prox_nuclear_dual(y_lowrank_temp / sigma, lambda_lowrank / sigma, hankel_size);
    
    %% 原始变量更新
    grad_data = x - X_input; % 数据保真度梯度
    grad_sparse = y_sparse;  % 稀疏项梯度
    grad_lowrank = hankel_adjoint_operator(y_lowrank, hankel_size, N); % 低秩项梯度
    
    x_new = x - tau * (grad_data + grad_sparse + grad_lowrank);
    
    %% 外推更新
    x_bar = x_new + theta * (x_new - x);
    
    %% 计算目标函数值
    data_fidelity = 0.5 * norm(x_new - X_input)^2;
    sparse_penalty = lambda_sparse * norm(x_new, 1);
    lowrank_penalty = lambda_lowrank * nuclear_norm_hankel(x_new, hankel_size);
    objective_val = data_fidelity + sparse_penalty + lowrank_penalty;
    
    %% 收敛检查
    param_change = norm(x_new - x) / (norm(x) + eps);
    convergence_history(iter) = param_change;
    objective_history(iter) = objective_val;
    
    if param_change < tolerance
        fprintf('  Condat-Vũ在第 %d 次迭代收敛\n', iter);
        break;
    end
    
    % 更新
    x = x_new;
    
    % 进度显示
    if mod(iter, 50) == 0
        fprintf('  迭代 %d: 目标函数 = %.6f, 变化 = %.2e\n', iter, objective_val, param_change);
    end
end

X_optimal = x;

% 收敛信息
convergence_info.converged = (param_change < tolerance);
convergence_info.iterations = iter;
convergence_info.final_error = param_change;
convergence_info.convergence_history = convergence_history(1:iter);
convergence_info.objective_history = objective_history(1:iter);
end

%% 交替最小化求解器
function [X_optimal, convergence_info] = sparse_lr_alternating_solver(X_input, signal, params)
% 交替最小化求解稀疏-低秩问题

N = length(X_input);
lambda_sparse = params.lambda_sparse;
lambda_lowrank = params.lambda_lowrank;
max_iter = params.max_iterations;
tolerance = params.tolerance;

% 初始化
X = X_input;
hankel_size = min(params.hankel_size, floor(N/2));

% 收敛历史
convergence_history = zeros(max_iter, 1);
objective_history = zeros(max_iter, 1);

fprintf('交替最小化求解器启动...\n');

for iter = 1:max_iter
    X_prev = X;
    
    %% 子问题1：固定低秩结构，优化稀疏性
    % min ||X||₁ + (1/2λ_sparse)||X - X_input||²
    X_sparse = soft_threshold_complex(X_input, lambda_sparse);
    
    %% 子问题2：固定稀疏结构，优化低秩性
    % min ||Hankel(X)||_* + (1/2λ_lowrank)||X - X_sparse||²
    X_lowrank = solve_lowrank_subproblem(X_sparse, lambda_lowrank, hankel_size);
    
    %% 加权组合
    weight = params.sparse_weight; % 稀疏权重
    X = weight * X_sparse + (1 - weight) * X_lowrank;
    
    %% 计算目标函数值
    data_fidelity = 0.5 * norm(X - X_input)^2;
    sparse_penalty = lambda_sparse * norm(X, 1);
    lowrank_penalty = lambda_lowrank * nuclear_norm_hankel(X, hankel_size);
    objective_val = data_fidelity + sparse_penalty + lowrank_penalty;
    
    %% 收敛检查
    param_change = norm(X - X_prev) / (norm(X_prev) + eps);
    convergence_history(iter) = param_change;
    objective_history(iter) = objective_val;
    
    if param_change < tolerance
        fprintf('  交替最小化在第 %d 次迭代收敛\n', iter);
        break;
    end
    
    % 进度显示
    if mod(iter, 20) == 0
        fprintf('  迭代 %d: 目标函数 = %.6f, 变化 = %.2e\n', iter, objective_val, param_change);
    end
end

X_optimal = X;

% 收敛信息
convergence_info.converged = (param_change < tolerance);
convergence_info.iterations = iter;
convergence_info.final_error = param_change;
convergence_info.convergence_history = convergence_history(1:iter);
convergence_info.objective_history = objective_history(1:iter);
end

%% 辅助函数：复数软阈值
function z = soft_threshold_complex(x, threshold)
% 复数域软阈值算子
magnitude = abs(x);
phase = angle(x);
shrunk_magnitude = max(magnitude - threshold, 0);
z = shrunk_magnitude .* exp(1j * phase);
end

%% 辅助函数：低秩Hankel约束更新
function Y_lowrank = update_lowrank_hankel_constraint(X_plus_U, lambda_lr_rho, hankel_size)
% 更新低秩Hankel约束

N = length(X_plus_U);
if hankel_size > N/2
    hankel_size = floor(N/2);
end

% 构造Hankel矩阵
H = construct_hankel_matrix(X_plus_U, hankel_size);

% SVD分解
[U, S, V] = svd(H);

% 软阈值处理奇异值
S_thresh = diag(max(diag(S) - lambda_lr_rho, 0));

% 重构低秩Hankel矩阵
H_lowrank = U * S_thresh * V';

% 从Hankel矩阵恢复信号
Y_lowrank = recover_signal_from_hankel(H_lowrank, N);
end

%% 辅助函数：构造Hankel矩阵
function H = construct_hankel_matrix(signal, hankel_size)
% 从信号构造Hankel矩阵
N = length(signal);
rows = hankel_size;
cols = N - hankel_size + 1;
H = zeros(rows, cols, 'like', signal(1));

for i = 1:rows
    for j = 1:cols
        H(i, j) = signal(i + j - 1);
    end
end
end

%% 辅助函数：从Hankel矩阵恢复信号
function signal_recovered = recover_signal_from_hankel(H, N)
% 从Hankel矩阵恢复信号 (反对角线平均)
signal_recovered = zeros(N, 1, 'like', H(1,1));
[rows, cols] = size(H);

for n = 1:N
    count = 0;
    sum_val = 0;
    
    for i = 1:rows
        j = n - i + 1;
        if j >= 1 && j <= cols
            sum_val = sum_val + H(i, j);
            count = count + 1;
        end
    end
    
    if count > 0
        signal_recovered(n) = sum_val / count;
    end
end
end

%% 辅助函数：Hankel矩阵核范数
function nuclear_norm_val = nuclear_norm_hankel(signal, hankel_size)
% 计算信号对应Hankel矩阵的核范数
H = construct_hankel_matrix(signal, hankel_size);
singular_values = svd(H);
nuclear_norm_val = sum(singular_values);
end

%% 辅助函数：Hankel算子
function Hx = hankel_operator(x, hankel_size)
% Hankel算子：x -> Hankel(x)
H = construct_hankel_matrix(x, hankel_size);
Hx = H(:); % 向量化
end

%% 辅助函数：Hankel伴随算子
function x = hankel_adjoint_operator(Hx_vec, hankel_size, N)
% Hankel伴随算子
rows = hankel_size;
cols = N - hankel_size + 1;
H = reshape(Hx_vec, rows, cols);
x = recover_signal_from_hankel(H, N);
end

%% 辅助函数：L1对偶近似算子
function z = prox_l1_dual(y, lambda)
% L1范数对偶近似算子
z = y ./ max(1, abs(y) / lambda);
end

%% 辅助函数：核范数对偶近似算子
function z = prox_nuclear_dual(y_vec, lambda, hankel_size)
% 核范数对偶近似算子
rows = hankel_size;
cols = length(y_vec) / rows;
Y = reshape(y_vec, rows, cols);

[U, S, V] = svd(Y);
S_new = diag(min(diag(S), lambda));
Z = U * S_new * V';

z = Z(:);
end

%% 辅助函数：低秩子问题求解
function X_lowrank = solve_lowrank_subproblem(X_input, lambda, hankel_size)
% 求解低秩子问题
X_lowrank = update_lowrank_hankel_constraint(X_input, lambda, hankel_size);
end

%% 默认参数
function params = get_default_sparse_lr_params()
% 稀疏-低秩正则化默认参数

% 算法选择
params.algorithm = 'admm'; % 'admm', 'condat_vu', 'alternating'

% 正则化参数
params.lambda_sparse = 0.1;    % 稀疏正则化权重
params.lambda_lowrank = 0.05;  % 低秩正则化权重

% 优化参数
params.max_iterations = 100;   % 最大迭代次数
params.tolerance = 1e-4;       % 收敛容差

% ADMM参数
params.rho = 1.0;              % ADMM惩罚参数

% Condat-Vũ参数
params.tau = 0.01;             % 原始步长
params.sigma = 0.01;           % 对偶步长
params.theta = 1.0;            % 外推参数

% 交替最小化参数
params.sparse_weight = 0.6;    % 稀疏权重

% Hankel矩阵参数
params.hankel_size = 32;       % Hankel矩阵大小
end
