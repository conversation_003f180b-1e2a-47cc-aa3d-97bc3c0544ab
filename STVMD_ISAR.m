%-------------------------------------------------------------------------%
%--------   ISAR Imaging based on Short-Time Variational Mode Decomposition   -------%
%--------   专为三维转动目标设计，解决散焦问题 - 优化版本                    -------%
%-------------------------------------------------------------------------%

function [ISAR_image_enhanced, s_compensated] = STVMD_ISAR(radar_data, params)
% STVMD_ISAR - 基于短时变分模态分解的ISAR成像算法 (优化版本)
%
% 该算法结合了短时傅里叶变换和变分模态分解的优点，专为处理非平稳信号设计，
% 能有效解决三维转动目标ISAR成像中的散焦问题。
%
% 优化特性:
% - 向量化操作减少循环开销
% - 自适应参数调整提高收敛性
% - 改进的数值稳定性
% - 针对仿真和实测数据的不同处理策略
%
% 输入:
%   radar_data - 距离压缩后的回波数据 (距离单元 x 方位单元)
%   params - 算法参数结构体:
%     .K - 模态数量 (默认: 3)
%     .alpha - 平衡参数 (默认: 2000)
%     .tau - 拉格朗日乘子更新步长 (默认: 0.1)
%     .tol - 收敛容限 (默认: 1e-7)
%     .window_sizes - 多尺度窗口大小数组 (默认: [32, 64])
%     .overlap - 窗口重叠率 (默认: 0.75)
%     .dynamic - 是否使用动态中心频率 (默认: true)
%     .max_iter - 最大迭代次数 (默认: 200)
%     .global_iterations - 全局迭代次数 (默认: 2)
%     .display_progress - 是否显示处理进度 (默认: true)
%     .display_intermediate - 是否显示中间结果 (默认: false)
%     .data_type - 数据类型 ('simulation' 或 'real', 默认: 'auto')
%     .adaptive_stop - 是否使用自适应停止准则 (默认: true)
%     .phase_smooth_order - 相位平滑多项式阶数 (默认: 4)
%
% 输出:
%   ISAR_image_enhanced - 增强型STVMD处理后的ISAR图像
%   s_compensated - 相位补偿后的信号

% 设置默认参数
if nargin < 2
    params = struct();
end

% 优化的默认参数设置
default_params = struct(...
    'K', 3, ...
    'alpha', 2000, ...
    'tau', 0.1, ...
    'tol', 1e-7, ...
    'window_sizes', [32, 64], ...  % 减少窗口数量提高效率
    'overlap', 0.75, ...           % 增加重叠率提高精度
    'dynamic', true, ...
    'max_iter', 200, ...           % 减少最大迭代次数
    'global_iterations', 2, ...    % 减少全局迭代次数
    'display_progress', true, ...
    'display_intermediate', false, ...
    'data_type', 'auto', ...       % 自动检测数据类型
    'adaptive_stop', true, ...     % 自适应停止准则
    'phase_smooth_order', 4 ...    % 相位平滑阶数
);

% 合并用户参数和默认参数
param_names = fieldnames(default_params);
for i = 1:length(param_names)
    if ~isfield(params, param_names{i})
        params.(param_names{i}) = default_params.(param_names{i});
    end
end

% 获取数据尺寸
[N_r, N_tm] = size(radar_data);

% 自动检测数据类型
if strcmp(params.data_type, 'auto')
    % 基于信号特性自动判断数据类型
    signal_energy = sum(abs(radar_data(:)).^2);
    signal_variance = var(abs(radar_data(:)));
    snr_estimate = 10*log10(signal_energy / (signal_variance + eps));

    if snr_estimate > 20 && signal_variance < 0.1
        params.data_type = 'simulation';
        if params.display_progress
            fprintf('检测到仿真数据 (SNR: %.1f dB)\n', snr_estimate);
        end
    else
        params.data_type = 'real';
        if params.display_progress
            fprintf('检测到实测数据 (SNR: %.1f dB)\n', snr_estimate);
        end
    end
end

% 根据数据类型调整参数
if strcmp(params.data_type, 'simulation')
    % 仿真数据参数优化
    params.alpha = params.alpha * 0.5;        % 降低平衡参数
    params.tol = params.tol * 10;             % 放宽收敛条件
    params.global_iterations = 1;             % 减少全局迭代
    initial_phase_comp = false;               % 仿真数据通常不需要初始相位补偿
else
    % 实测数据参数优化
    params.alpha = params.alpha * 1.5;        % 增加平衡参数
    params.tol = params.tol * 0.1;            % 严格收敛条件
    initial_phase_comp = true;                % 实测数据需要初始相位补偿
end

% 创建时间和频率轴
tm_norm = (0:N_tm-1)/N_tm;
tm_indices = 0:N_tm-1;
omega_axis_norm = (0:N_tm-1) / N_tm;

% 初始化存储
s_compensated = zeros(N_r, N_tm, 'like', 1j);
s_temp = radar_data;

% --- 自适应初始相位补偿 ---
if initial_phase_comp
    % 估计主要运动参数
    [~, max_energy_idx] = max(sum(abs(radar_data).^2, 2));
    ref_signal = radar_data(max_energy_idx, :);

    % 使用多项式拟合估计相位趋势
    phase_unwrapped = unwrap(angle(ref_signal));
    p_coeff = polyfit(tm_indices, phase_unwrapped, 3);

    % 自适应相位补偿参数
    linear_term = p_coeff(3) / (2*pi);
    quadratic_term = 2 * p_coeff(2) / (2*pi);
    cubic_term = 6 * p_coeff(1) / (2*pi);

    % 应用初始相位补偿
    ones_r = ones(N_r, 1);
    phase_comp_terms = 2*pi*(linear_term*tm_norm + (1/2)*quadratic_term*tm_norm.^2 + (1/6)*cubic_term*tm_norm.^3);
    s_phase_comp = exp(1j * ones_r * phase_comp_terms);
    s_temp = s_temp .* s_phase_comp;

    if params.display_progress
        fprintf('应用自适应初始相位补偿 (线性: %.1f, 二次: %.1f, 三次: %.1f)\n', ...
                linear_term, quadratic_term, cubic_term);
    end
end

if params.display_progress
    fprintf('开始STVMD ISAR处理 (共 %d 个距离单元, 数据类型: %s)...\n', N_r, params.data_type);
end

% --- 优化的全局迭代策略 ---
% 预计算一些常用量以提高效率
energy_threshold = 1e-10;
range_energies = sum(abs(s_temp).^2, 2);
active_ranges = find(range_energies > energy_threshold);
N_active = length(active_ranges);

if params.display_progress
    fprintf('活跃距离单元数量: %d / %d\n', N_active, N_r);
end

% 初始化质量指标
previous_entropy = inf;
entropy_history = zeros(params.global_iterations, 1);

for global_iter = 1:params.global_iterations
    if params.display_progress
        fprintf('全局迭代: %d / %d\n', global_iter, params.global_iterations);
    end

    % 计算当前图像质量指标
    temp_image = fftshift(fft(s_temp, [], 2), 2);
    current_entropy = calculate_image_entropy(abs(temp_image));
    entropy_history(global_iter) = current_entropy;

    % 计算对比度和聚焦度
    current_contrast = calculate_contrast(abs(temp_image));
    current_focus = calculate_focus_measure(abs(temp_image));

    if params.display_progress
        fprintf('图像熵: %.4f, 对比度: %.4f, 聚焦度: %.4f\n', ...
                current_entropy, current_contrast, current_focus);
    end

    % 自适应停止准则
    if params.adaptive_stop && global_iter > 1
        entropy_improvement = previous_entropy - current_entropy;
        if entropy_improvement < 0.01 || current_entropy > previous_entropy * 1.1
            if params.display_progress
                fprintf('图像质量改进不明显或恶化，提前停止迭代\n');
            end
            break;
        end
    end

    % 显示中间结果
    if params.display_intermediate
        figure('name', sprintf('迭代 %d - 中间结果', global_iter));
        max_val = max(abs(temp_image(:)));
        if max_val == 0, max_val = 1; end
        temp_image_db = 20*log10(abs(temp_image) / max_val);

        imagesc(temp_image_db);
        caxis([-40, 0]);
        colormap('jet');
        colorbar;
        xlabel('方位单元 (Azimuth Cell / Doppler)');
        ylabel('距离单元 (Range Cell)');
        title(sprintf('迭代 %d - 中间结果 (dB)', global_iter));
        axis xy;
    end

    % --- 优化的距离单元处理 ---
    % 只处理活跃的距离单元以提高效率
    for idx = 1:N_active
        r_idx = active_ranges(idx);

        if params.display_progress && mod(idx, max(1, round(N_active/10))) == 0
            fprintf('处理距离单元: %d / %d (索引: %d)\n', idx, N_active, r_idx);
        end

        % 获取当前距离单元的信号
        signal_r_tm = s_temp(r_idx, :);
        signal_energy = range_energies(r_idx);

        % --- 优化的模态数量确定 ---
        % 使用预计算的FFT
        S_r_omega = fft(signal_r_tm);
        abs_spectrum = abs(S_r_omega);

        % 改进的频谱平滑和峰值检测
        if N_tm >= 10
            window_size = min(7, floor(N_tm/20));
            if window_size > 1
                smoothed_spectrum = conv(abs_spectrum, ones(1, window_size)/window_size, 'same');
            else
                smoothed_spectrum = abs_spectrum;
            end
        else
            smoothed_spectrum = abs_spectrum;
        end

        % 自适应峰值检测阈值
        spectrum_max = max(smoothed_spectrum);
        if spectrum_max > 0
            peak_threshold = 0.15 * spectrum_max;  % 提高阈值以减少噪声峰值
            [~, peak_indices] = findpeaks(smoothed_spectrum, 'MinPeakHeight', peak_threshold, ...
                                         'MinPeakDistance', max(1, floor(N_tm/20)));
        else
            peak_indices = [];
        end

        % 根据数据类型和峰值数量自适应确定模态数K
        if strcmp(params.data_type, 'simulation')
            K = min(max(length(peak_indices), 2), params.K);
        else
            K = min(max(length(peak_indices), 1), params.K);
        end

        % 改进的自适应参数调整
        normalized_energy = signal_energy / (mean(range_energies(active_ranges)) + eps);

        % 自适应alpha参数
        if strcmp(params.data_type, 'simulation')
            alpha_stvmd = params.alpha * (0.8 + 0.4 * tanh(normalized_energy - 1));
        else
            alpha_stvmd = params.alpha * (1 + 0.3 * exp(-normalized_energy));
        end

        % 自适应tau参数
        tau_stvmd = params.tau * (0.8 + 0.4 / (1 + normalized_energy));

        % --- 优化的多尺度STVMD分解 ---
        num_scales = length(params.window_sizes);
        u_k_tm_multiscale = cell(num_scales, 1);
        omega_k_multiscale = cell(num_scales, 1);
        scale_qualities = zeros(num_scales, 1);

        % 并行处理多个尺度（如果可能）
        for scale_idx = 1:num_scales
            win_size = min(params.window_sizes(scale_idx), N_tm);

            % 跳过过小的窗口
            if win_size < 8
                continue;
            end

            % 应用优化的STVMD分解
            [u_k_tm, omega_k, quality] = stvmd_decompose_optimized(signal_r_tm, K, alpha_stvmd, tau_stvmd, ...
                                                                  win_size, params.overlap, params.dynamic, ...
                                                                  params.max_iter, params.tol);

            % 存储分解结果和质量指标
            u_k_tm_multiscale{scale_idx} = u_k_tm;
            omega_k_multiscale{scale_idx} = omega_k;
            scale_qualities(scale_idx) = quality;
        end

        % --- 改进的多尺度融合相位误差估计 ---
        valid_scales = find(~cellfun(@isempty, u_k_tm_multiscale));
        if isempty(valid_scales)
            % 如果所有尺度都失败，使用简单的相位补偿
            s_compensated(r_idx, :) = signal_r_tm;
            continue;
        end

        % 初始化融合权重和相位误差
        scale_weights = zeros(length(valid_scales), 1);
        phase_errors_multiscale = zeros(length(valid_scales), N_tm);

        % 向量化计算每个尺度的权重和相位误差
        for i = 1:length(valid_scales)
            scale_idx = valid_scales(i);
            u_k_tm_decomposed = u_k_tm_multiscale{scale_idx};
            omega_k_current = omega_k_multiscale{scale_idx};

            if isempty(u_k_tm_decomposed)
                continue;
            end

            % 向量化计算模式能量和相干性
            K_current = size(u_k_tm_decomposed, 1);
            mode_energies = sum(abs(u_k_tm_decomposed).^2, 2);

            % 改进的相干性计算
            mode_coherence = zeros(K_current, 1);
            for k_idx = 1:K_current
                phase_seq = unwrap(angle(u_k_tm_decomposed(k_idx, :)));
                if length(phase_seq) > 1
                    phase_diff = diff(phase_seq);
                    phase_variance = var(phase_diff);
                    mode_coherence(k_idx) = 1 / (phase_variance + eps);
                else
                    mode_coherence(k_idx) = 1;
                end
            end

            % 结合能量、相干性和质量指标选择最佳模式
            combined_score = mode_energies .* mode_coherence * scale_qualities(scale_idx);
            [best_score, best_mode_idx] = max(combined_score);

            % 计算当前尺度的权重
            scale_weights(i) = best_score;

            % 改进的相位误差计算
            u_dominant_tm = u_k_tm_decomposed(best_mode_idx, :);
            f_center_dominant_norm = omega_k_current(best_mode_idx);

            % 使用更稳定的相位误差估计
            phase_error = angle(u_dominant_tm) - 2 * pi * f_center_dominant_norm * tm_indices;
            phase_error = unwrap(phase_error);

            % 自适应多项式拟合阶数
            poly_order = min(params.phase_smooth_order, max(2, floor(N_tm/15)));
            if length(tm_indices) > poly_order
                p = polyfit(tm_indices, phase_error, poly_order);
                phase_error_smooth = polyval(p, tm_indices);
            else
                phase_error_smooth = phase_error;
            end

            % 移除均值并应用额外的平滑
            phase_error_smooth = phase_error_smooth - mean(phase_error_smooth);

            % 对于仿真数据，应用更强的平滑
            if strcmp(params.data_type, 'simulation') && length(phase_error_smooth) > 5
                phase_error_smooth = smooth(phase_error_smooth, min(5, floor(length(phase_error_smooth)/10)))';
            end

            % 存储当前尺度的相位误差
            phase_errors_multiscale(i, :) = phase_error_smooth;
        end

        % 归一化权重
        sum_weights = sum(scale_weights);
        if sum_weights > 0
            scale_weights = scale_weights / sum_weights;
        else
            scale_weights = ones(length(valid_scales), 1) / length(valid_scales);
        end

        % 加权融合多尺度相位误差
        fused_phase_error = zeros(1, N_tm);
        for i = 1:length(valid_scales)
            fused_phase_error = fused_phase_error + scale_weights(i) * phase_errors_multiscale(i, :);
        end

        % --- 改进的相位补偿 ---
        % 应用数值稳定的相位补偿
        phase_comp_factor = exp(-1j * fused_phase_error);

        % 检查相位补偿的有效性
        if any(~isfinite(phase_comp_factor))
            % 如果相位补偿因子无效，使用原始信号
            s_compensated(r_idx, :) = signal_r_tm;
        else
            s_compensated(r_idx, :) = signal_r_tm .* phase_comp_factor;
        end
    end

    % 复制未处理的距离单元
    inactive_ranges = setdiff(1:N_r, active_ranges);
    s_compensated(inactive_ranges, :) = s_temp(inactive_ranges, :);

    % 更新临时信号用于下一次迭代
    s_temp = s_compensated;

    % 更新活跃距离单元（能量可能发生变化）
    range_energies = sum(abs(s_temp).^2, 2);
    active_ranges = find(range_energies > energy_threshold);
    N_active = length(active_ranges);

    % 更新previous_entropy用于下次比较
    previous_entropy = current_entropy;
end

if params.display_progress
    fprintf('STVMD ISAR处理完成。\n');
    fprintf('最终图像熵: %.4f\n', entropy_history(min(global_iter, end)));
end

% --- 方位向FFT成像 ---
% 修复镜像对称问题，确保结果与传统FFT成像的目标形状一致

% 先计算传统FFT图像作为参考
traditional_image = fftshift(fft(radar_data, [], 2), 2);
% 提取传统图像中的主要轮廓特征
traditional_features = abs(traditional_image) > 0.1*max(abs(traditional_image(:)));

% 创建两种可能的结果，一种是原始补偿信号，一种是相位共轭后的补偿信号
s_compensated_original = s_compensated;
s_compensated_conjugate = conj(s_compensated);

% 计算两种可能结果的FFT图像
image_original = fftshift(fft(s_compensated_original, [], 2), 2);
image_conjugate = fftshift(fft(s_compensated_conjugate, [], 2), 2);

% 提取两种结果的主要轮廓特征
features_original = abs(image_original) > 0.1*max(abs(image_original(:)));
features_conjugate = abs(image_conjugate) > 0.1*max(abs(image_conjugate(:)));

% 计算与传统图像的结构相似度
similarity_original = sum(sum(features_original & traditional_features)) / sum(traditional_features(:));
similarity_conjugate = sum(sum(features_conjugate & traditional_features)) / sum(traditional_features(:));

% 选择与传统图像更相似的结果
if similarity_conjugate > similarity_original
    if params.display_progress
        fprintf('检测到错误的镜像对称，应用相位共轭校正使结果与传统FFT成像一致...\n');
    end
    ISAR_image_enhanced = image_conjugate;
    s_compensated = s_compensated_conjugate;
else
    ISAR_image_enhanced = image_original;
end

% 额外安全检查：分析目标主要位置的能量分布是否与传统图像一致
% 计算传统图像和增强图像的质心
[row_idx, col_idx] = find(abs(traditional_image) > 0.3*max(abs(traditional_image(:))));
if ~isempty(row_idx) && ~isempty(col_idx)
    traditional_centroid = [mean(row_idx), mean(col_idx)];
    
    [row_idx, col_idx] = find(abs(ISAR_image_enhanced) > 0.3*max(abs(ISAR_image_enhanced(:))));
    if ~isempty(row_idx) && ~isempty(col_idx)
        enhanced_centroid = [mean(row_idx), mean(col_idx)];
        
        % 如果质心位置差异太大，可能表明仍有镜像问题
        if abs(traditional_centroid(2) - enhanced_centroid(2)) > N_tm/4
            if params.display_progress
                fprintf('检测到质心位置不一致，应用额外校正...\n');
            end
            % 反转最终结果
            ISAR_image_enhanced = conj(ISAR_image_enhanced);
        end
    end
end

end

% --- 辅助函数：计算图像熵 ---
function entropy = calculate_image_entropy(image_data)
% 改进的图像熵计算
image_data = abs(image_data);
total_energy = sum(image_data(:));
if total_energy == 0
    entropy = 0;
    return;
end
normalized_data = image_data / total_energy;
% 避免log(0)的问题
valid_data = normalized_data(normalized_data > eps);
if isempty(valid_data)
    entropy = 0;
else
    entropy = -sum(valid_data .* log2(valid_data));
end
end

% --- 辅助函数：计算对比度 ---
function contrast_val = calculate_contrast(image_data)
% 计算图像对比度（标准差与均值的比值）
image_data = abs(image_data);
mean_val = mean(image_data(:));
if mean_val == 0
    contrast_val = 0;
else
    contrast_val = std(image_data(:)) / mean_val;
end
end

% --- 辅助函数：计算聚焦度 ---
function focus_val = calculate_focus_measure(image_data)
% 计算图像聚焦度（基于梯度的方法）
image_data = abs(image_data);
[Gx, Gy] = gradient(image_data);
focus_val = mean(sqrt(Gx(:).^2 + Gy(:).^2));
end

% --- 辅助函数：优化的STVMD分解 ---
function [u_k_tm, omega_k, quality] = stvmd_decompose_optimized(signal, K, alpha, tau, window_length, overlap, dynamic, max_iter, tol)
% 优化的STVMD分解算法
%
% 输入:
%   signal - 待分解的复信号
%   K - 模态数量
%   alpha - 平衡参数
%   tau - 拉格朗日乘子更新步长
%   window_length - 窗口长度
%   overlap - 窗口重叠率
%   dynamic - 是否使用动态中心频率
%   max_iter - 最大迭代次数
%   tol - 收敛容限
%
% 输出:
%   u_k_tm - 分解后的模态函数 (K x signal_length)
%   omega_k - 各模态的中心频率 (K x 1)
%   quality - 分解质量指标

% 初始化参数
signal_length = length(signal);
signal = signal(:).'; % 确保为行向量

% 检查输入有效性
if signal_length < window_length
    window_length = signal_length;
end

% 优化的窗口分割策略
step = max(1, floor(window_length * (1 - overlap)));
num_segments = max(1, floor((signal_length - window_length) / step) + 1);

% 预分配内存
segments = zeros(num_segments, window_length);

% 向量化的窗口分割
for i = 1:num_segments
    start_idx = (i-1) * step + 1;
    end_idx = min(start_idx + window_length - 1, signal_length);
    actual_length = end_idx - start_idx + 1;

    if actual_length == window_length
        segments(i, :) = signal(start_idx:end_idx);
    else
        % 处理边界情况 - 使用零填充
        segments(i, 1:actual_length) = signal(start_idx:end_idx);
        segments(i, (actual_length+1):end) = 0;
    end
end

% 应用优化的窗函数
window_func = hamming(window_length)';
windowed_segments = bsxfun(@times, segments, window_func);

% 优化的FFT计算
n_fft = 2^nextpow2(max(window_length, 16)); % 确保最小FFT长度
f_hat_s = fft(windowed_segments, n_fft, 2);

% 预分配内存
u_hat_s = zeros(num_segments, n_fft, K, 'like', 1j);
omega_s = zeros(K, num_segments);

% 改进的初始化策略
freq_axis = (0:n_fft-1) / n_fft;
freq_axis(freq_axis > 0.5) = freq_axis(freq_axis > 0.5) - 1;

% 基于信号频谱特性初始化中心频率
signal_spectrum = mean(abs(f_hat_s).^2, 1);
[~, peak_locs] = findpeaks(signal_spectrum, 'NPeaks', K, 'SortStr', 'descend');
if length(peak_locs) >= K
    for k = 1:K
        omega_s(k, :) = freq_axis(peak_locs(k));
    end
else
    % 回退到均匀分布
    for k = 1:K
        omega_s(k, :) = (0.4 / K) * (k - 1) - 0.2;
    end
end

% 预分配拉格朗日乘子
lambda_hat = zeros(num_segments, n_fft, 'like', 1j);

% 优化的迭代VMD
convergence_history = zeros(max_iter, 1);
for iter = 1:max_iter
    u_hat_prev = u_hat_s;
    omega_prev = omega_s;

    % 向量化的模态更新
    for k = 1:K
        % 计算除当前模态外的所有模态之和
        sum_uk = sum(u_hat_s, 3) - u_hat_s(:, :, k);

        % 向量化的频域滤波
        for t = 1:num_segments
            % 构建维纳滤波器
            freq_diff_sq = (freq_axis - omega_s(k, t)).^2;
            filter = 1 ./ (1 + alpha * freq_diff_sq);

            % 更新模态
            numerator = f_hat_s(t, :) - sum_uk(t, :) + lambda_hat(t, :) / 2;
            u_hat_s(t, :, k) = numerator .* filter;
        end

        % 更新中心频率
        if dynamic
            for t = 1:num_segments
                power_spectrum = abs(u_hat_s(t, :, k)).^2;
                total_power = sum(power_spectrum);
                if total_power > eps
                    omega_s(k, t) = sum(freq_axis .* power_spectrum) / total_power;
                    % 限制频率范围
                    omega_s(k, t) = max(-0.5, min(0.5, omega_s(k, t)));
                end
            end
        else
            % 非动态模式
            total_power_spectrum = sum(abs(u_hat_s(:, :, k)).^2, 1);
            total_power = sum(total_power_spectrum);
            if total_power > eps
                new_omega = sum(freq_axis .* total_power_spectrum) / total_power;
                omega_s(k, :) = max(-0.5, min(0.5, new_omega));
            end
        end
    end

    % 更新拉格朗日乘子
    residual = sum(u_hat_s, 3) - f_hat_s;
    lambda_hat = lambda_hat + tau * residual;

    % 检查收敛性
    u_diff = norm(u_hat_s(:) - u_hat_prev(:))^2 / (norm(u_hat_prev(:))^2 + eps);
    omega_diff = norm(omega_s(:) - omega_prev(:))^2 / (norm(omega_prev(:))^2 + eps);

    convergence_history(iter) = u_diff + omega_diff;

    if (u_diff < tol) && (omega_diff < tol)
        break;
    end
end

% 重建时域IMFs
u_k_tm = zeros(K, signal_length, 'like', 1j);
normalization = zeros(1, signal_length);

% 优化的重叠相加
for k = 1:K
    for i = 1:num_segments
        start_idx = (i-1) * step + 1;
        end_idx = min(start_idx + window_length - 1, signal_length);
        segment_length = end_idx - start_idx + 1;

        % IFFT并取实际长度
        temp = ifft(u_hat_s(i, :, k), n_fft, 2);
        segment_data = temp(1:segment_length);

        % 应用窗函数并累加
        window_segment = window_func(1:segment_length);
        u_k_tm(k, start_idx:end_idx) = u_k_tm(k, start_idx:end_idx) + ...
                                       segment_data .* window_segment;

        % 累积归一化权重
        if k == 1
            normalization(start_idx:end_idx) = normalization(start_idx:end_idx) + window_segment;
        end
    end
end

% 应用归一化
normalization(normalization < eps) = 1;
for k = 1:K
    u_k_tm(k, :) = u_k_tm(k, :) ./ normalization;
end

% 计算分解质量指标
omega_k = mean(omega_s, 2);
reconstruction_error = norm(signal - sum(u_k_tm, 1)) / norm(signal);
mode_orthogonality = 0;
for i = 1:K
    for j = i+1:K
        mode_orthogonality = mode_orthogonality + abs(sum(conj(u_k_tm(i,:)) .* u_k_tm(j,:)));
    end
end
mode_orthogonality = mode_orthogonality / (K*(K-1)/2) / signal_length;

% 综合质量指标
quality = 1 / (1 + reconstruction_error + mode_orthogonality);

end
