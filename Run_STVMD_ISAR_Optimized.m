%-------------------------------------------------------------------------%
%--------        优化版本：运行基于短时变分模态分解的ISAR成像算法        -------%
%--------        专为三维转动目标设计，解决散焦问题                    -------%
%--------        性能优化和成像质量改进版本                            -------%
%-------------------------------------------------------------------------%
clc; clear all; close all;

%% 数据加载和预处理
fprintf('=== 优化版本 STVMD ISAR 成像算法 ===\n');

% 加载数据 - 支持多种数据源
data_source = 'mig'; % 'simulation', 'real', 'mig', 'point'

switch data_source
    case 'simulation'
        load simulation_ship_s_r_tm2.mat
        radar_data = s_r_tm2;
        fprintf('加载仿真数据: simulation_ship_s_r_tm2.mat\n');
    case 'real'
        load shipx2.mat;
        radar_data = shipx2;
        fprintf('加载实测数据: shipx2.mat\n');
    case 'mig'
        load mig2555.mat
        radar_data = X; % 假设变量名为X
        fprintf('加载MIG数据: MIG255.MAT\n');
    case 'point'
        load pointx2.mat
        radar_data = pointx2;
        fprintf('加载点目标数据: pointx2.mat\n');
    otherwise
        error('未知的数据源类型');
end

% 显示数据基本信息
[N_r, N_tm] = size(radar_data);
fprintf('数据尺寸: %d x %d (距离单元 x 方位单元)\n', N_r, N_tm);
fprintf('数据类型: %s\n', class(radar_data));
fprintf('数据范围: [%.2e, %.2e]\n', min(abs(radar_data(:))), max(abs(radar_data(:))));

%% 传统FFT成像结果（基准对比）
fprintf('\n--- 计算传统FFT成像结果 ---\n');
tic;
Y_original = fftshift(fft(radar_data, [], 2), 2);
fft_time = toc;
fprintf('传统FFT处理时间: %.3f 秒\n', fft_time);

max_val_orig = max(abs(Y_original(:)));
G1_original = 20*log10(abs(Y_original)./max_val_orig);

% 计算传统FFT图像质量指标
entropy_fft = EntropyImage(abs(Y_original) + eps);
contrast_fft = contrast(abs(Y_original));
fprintf('传统FFT - 图像熵: %.4f, 对比度: %.4f\n', entropy_fft, contrast_fft);

%% 优化的STVMD ISAR算法参数设置
fprintf('\n--- 设置优化的STVMD参数 ---\n');

% 根据数据类型自动调整参数
if strcmp(data_source, 'simulation')
    % 仿真数据优化参数
    params = struct();
    params.K = 3;                      % 模态数量
    params.alpha = 1000;               % 降低平衡参数
    params.tau = 0.05;                 % 减小步长
    params.tol = 1e-6;                 % 放宽收敛容限
    params.window_sizes = [32, 64];    % 减少窗口数量
    params.overlap = 0.75;             % 高重叠率
    params.dynamic = true;             % 动态中心频率
    params.max_iter = 150;             % 减少迭代次数
    params.global_iterations = 1;      % 单次全局迭代
    params.data_type = 'simulation';   % 明确指定数据类型
    params.adaptive_stop = true;       % 自适应停止
    params.phase_smooth_order = 3;     % 较低的相位平滑阶数
    fprintf('使用仿真数据优化参数\n');
else
    % 实测数据优化参数
    params = struct();
    params.K = 4;                      % 增加模态数量
    params.alpha = 3000;               % 增加平衡参数
    params.tau = 0.1;                  % 标准步长
    params.tol = 1e-7;                 % 严格收敛容限
    params.window_sizes = [32, 64];    % 标准窗口
    params.overlap = 0.75;             % 高重叠率
    params.dynamic = true;             % 动态中心频率
    params.max_iter = 200;             % 标准迭代次数
    params.global_iterations = 5;      % 两次全局迭代
    params.data_type = 'real';         % 明确指定数据类型
    params.adaptive_stop = true;       % 自适应停止
    params.phase_smooth_order = 4;     % 标准相位平滑阶数
    fprintf('使用实测数据优化参数\n');
end

params.display_progress = true;       % 显示处理进度
params.display_intermediate = false;  % 不显示中间结果以节省时间

% 显示参数设置
fprintf('参数设置:\n');
fprintf('  模态数量 K: %d\n', params.K);
fprintf('  平衡参数 alpha: %d\n', params.alpha);
fprintf('  窗口大小: [%s]\n', num2str(params.window_sizes));
fprintf('  重叠率: %.2f\n', params.overlap);
fprintf('  全局迭代次数: %d\n', params.global_iterations);
fprintf('  最大迭代次数: %d\n', params.max_iter);

%% 运行优化的STVMD ISAR成像算法
fprintf('\n--- 开始STVMD ISAR处理 ---\n');
tic;
[ISAR_image_stvmd, s_compensated] = STVMD_ISAR(radar_data, params);
processing_time = toc;

fprintf('STVMD处理完成！\n');
fprintf('总处理时间: %.2f 秒\n', processing_time);
fprintf('相对于FFT的时间比: %.1fx\n', processing_time / fft_time);

%% 计算和显示图像质量指标
fprintf('\n--- 图像质量评估 ---\n');

% STVMD结果质量指标
max_val_stvmd = max(abs(ISAR_image_stvmd(:)));
if max_val_stvmd == 0, max_val_stvmd = 1; end
ISAR_image_stvmd_db = 20*log10(abs(ISAR_image_stvmd) / max_val_stvmd);

entropy_stvmd = EntropyImage(abs(ISAR_image_stvmd) + eps);
contrast_stvmd = contrast(abs(ISAR_image_stvmd));

% 计算改进指标
entropy_improvement = (entropy_fft - entropy_stvmd) / entropy_fft * 100;
contrast_improvement = (contrast_stvmd - contrast_fft) / contrast_fft * 100;

fprintf('图像质量对比:\n');
fprintf('  传统FFT  - 熵: %.4f, 对比度: %.4f\n', entropy_fft, contrast_fft);
fprintf('  STVMD    - 熵: %.4f, 对比度: %.4f\n', entropy_stvmd, contrast_stvmd);
fprintf('  改进     - 熵: %.1f%%, 对比度: %.1f%%\n', entropy_improvement, contrast_improvement);

%% 显示成像结果
fprintf('\n--- 显示成像结果 ---\n');

% 传统FFT成像结果
figure('name','传统FFT成像结果', 'Position', [100, 500, 600, 400]);
imagesc(G1_original);
caxis([-30,0]);
grid on; axis xy; colorbar;
xlabel('方位单元 (Azimuth Cell / Doppler)');
ylabel('距离单元 (Range Cell)');
colormap jet;
title(sprintf('传统FFT成像结果 (熵: %.3f, 对比度: %.3f)', entropy_fft, contrast_fft));

% STVMD成像结果
figure('name', 'STVMD ISAR成像结果', 'Position', [750, 500, 600, 400]);
imagesc(ISAR_image_stvmd_db);
caxis([-30, 0]);
grid on; axis xy; colorbar;
xlabel('方位单元 (Azimuth Cell / Doppler)');
ylabel('距离单元 (Range Cell)');
colormap jet;
title(sprintf('STVMD成像结果 (熵: %.3f, 对比度: %.3f)', entropy_stvmd, contrast_stvmd));

%% 信号分析和可视化
fprintf('\n--- 信号分析 ---\n');

% 选择能量最大的距离单元进行分析
[~, max_energy_range_bin] = max(sum(abs(radar_data).^2, 2));
fprintf('选择距离单元 %d 进行信号分析\n', max_energy_range_bin);

% 原始信号与补偿后信号对比
figure('name', '信号对比分析', 'Position', [100, 50, 1200, 400]);

% 原始信号幅度
subplot(2,4,1);
plot(abs(radar_data(max_energy_range_bin, :)), 'b-', 'LineWidth', 1.5);
title('原始信号幅度'); xlabel('方位采样点'); ylabel('幅度'); grid on;

% 原始信号相位
subplot(2,4,2);
plot(unwrap(angle(radar_data(max_energy_range_bin, :))), 'b-', 'LineWidth', 1.5);
title('原始信号相位'); xlabel('方位采样点'); ylabel('相位 (rad)'); grid on;

% 补偿后信号幅度
subplot(2,4,3);
plot(abs(s_compensated(max_energy_range_bin, :)), 'r-', 'LineWidth', 1.5);
title('补偿后信号幅度'); xlabel('方位采样点'); ylabel('幅度'); grid on;

% 补偿后信号相位
subplot(2,4,4);
plot(unwrap(angle(s_compensated(max_energy_range_bin, :))), 'r-', 'LineWidth', 1.5);
title('补偿后信号相位'); xlabel('方位采样点'); ylabel('相位 (rad)'); grid on;

% 相位差分析
subplot(2,4,5);
phase_diff = unwrap(angle(s_compensated(max_energy_range_bin, :))) - ...
             unwrap(angle(radar_data(max_energy_range_bin, :)));
plot(phase_diff, 'g-', 'LineWidth', 1.5);
title('相位补偿量'); xlabel('方位采样点'); ylabel('相位差 (rad)'); grid on;

% 频谱对比
subplot(2,4,6);
freq_orig = abs(fft(radar_data(max_energy_range_bin, :)));
freq_comp = abs(fft(s_compensated(max_energy_range_bin, :)));
freq_axis = (0:length(freq_orig)-1) / length(freq_orig);
plot(freq_axis, freq_orig, 'b-', freq_axis, freq_comp, 'r-', 'LineWidth', 1.5);
title('频谱对比'); xlabel('归一化频率'); ylabel('幅度'); 
legend('原始', '补偿后', 'Location', 'best'); grid on;

% 时频图对比
subplot(2,4,7);
window_size = min(32, floor(N_tm/4));
[S, F, T] = spectrogram(radar_data(max_energy_range_bin, :), hamming(window_size), ...
                       floor(window_size*0.75), 256, 1, 'yaxis');
imagesc(T, F, 20*log10(abs(S)/max(abs(S(:)))));
axis xy; colormap jet; caxis([-40, 0]);
title('原始信号时频图'); xlabel('时间'); ylabel('归一化频率');

subplot(2,4,8);
[S_comp, F_comp, T_comp] = spectrogram(s_compensated(max_energy_range_bin, :), ...
                                       hamming(window_size), floor(window_size*0.75), 256, 1, 'yaxis');
imagesc(T_comp, F_comp, 20*log10(abs(S_comp)/max(abs(S_comp(:)))));
axis xy; colormap jet; caxis([-40, 0]);
title('补偿后信号时频图'); xlabel('时间'); ylabel('归一化频率');

%% 保存结果
fprintf('\n--- 保存结果 ---\n');
save_filename = sprintf('STVMD_ISAR_Results_%s.mat', data_source);
save(save_filename, 'ISAR_image_stvmd', 's_compensated', 'params', ...
     'processing_time', 'entropy_stvmd', 'contrast_stvmd', ...
     'entropy_improvement', 'contrast_improvement');
fprintf('结果已保存到: %s\n', save_filename);

%% 性能总结
fprintf('\n=== 处理总结 ===\n');
fprintf('数据源: %s\n', data_source);
fprintf('数据尺寸: %d x %d\n', N_r, N_tm);
fprintf('处理时间: %.2f 秒\n', processing_time);
fprintf('图像熵改进: %.1f%%\n', entropy_improvement);
fprintf('对比度改进: %.1f%%\n', contrast_improvement);
fprintf('算法效率: %.1f 距离单元/秒\n', N_r / processing_time);

if entropy_improvement > 5 && contrast_improvement > 10
    fprintf('✓ 成像质量显著改善\n');
elseif entropy_improvement > 0 && contrast_improvement > 0
    fprintf('○ 成像质量有所改善\n');
else
    fprintf('✗ 成像质量改善不明显，建议调整参数\n');
end

fprintf('=== 处理完成 ===\n');
