clc
close all
clear all
load data2

%% compensate the RVP
% frequency vector
if (mod(N, 2)~=0)
	f = (-(N-1)/2:((N-1)/2))*fs/N;
else
	f = (-N/2:N/2-1)*fs/N;
end 
df = f(2)-f(1);
r = -f*c/(2*K);
x_r = zeros(N, M);
for i = 1: M
	x_r(:, i) = fftshift(fft(raw(:, i)));
	% compensate the RVP
	x_r(:, i) = x_r(:, i).*exp(-1j*pi*f.*f/K).';	%'
end
x_t = zeros(N, M);
for i = 1: M
	x_t(:, i) = ifft(ifftshift(x_r(:, i)));
end

%% time domain presentation 
figure;imagesc(abs(x_t));
% imagesc_data(x_t, [0, M/PRF], [ts(1), ts(N)], 'slow time(s)', 'fast time(s)', 'time domain', 0);
% imagesc_data(x_r, [0, M/PRF], [r(1), r(N)], 'slow time(s)', 'range(m)', 'slow-time distance', 0);
% imagesc_data(x_t, [0, M/PRF], [ts(1), ts(N)], 'slow time(s)', 'fast time(s)', 'time domain', 0);
% imagesc_data(x_r, [0, M/PRF], [r(1), r(N)], 'slow time(s)', 'range(m)', 'slow-time distance', 0);
%% aligning the envelop	%%%%%%%%%%%%%%%%%
factor = 8;
NN = factor*N;
x_r_en = zeros(NN, M);
x_t_aligned = zeros(N, M);
x_r_aligned = zeros(N, M);
x_r_en(:, 1) = abs(fftshift(fft(x_t(:, 1), NN)));
x_t_aligned(:, 1) = x_t(:, 1);

% offset(i), i+1th pulse right shift
offset = zeros(1,M-1);      % save the offset between two pulses
h=waitbar(0,'aligning the envelop');
for i = 1:M-1
    x_r_en(:, i+1) = abs(fftshift(fft(x_t(:, i+1), NN)));
    correlation = fftshift(ifft( fft(x_r_en(:, i), 2*NN-1) .* conj(fft(x_r_en(:, i+1), 2*NN-1))));
    [~, offset(i)] = max(abs(correlation));
    if (i == 4)
        figure
        plot(-NN+1:NN-1, abs(correlation));
    end
    offset(i) = offset(i)-NN;
    if (offset(i) >= NN/2)
        offset(i) = offset(i) - NN;
    else if (offset(i) < -NN/2)
            offset(i) = offset(i) + NN;
        end
    end
    x_t_aligned(:, i+1) = x_t(:, i+1) .* exp(1j*2*pi*df/factor*offset(i)*ts).';
    x_r_en(:, i+1) = abs(fftshift(fft(x_t_aligned(:, i+1), NN)));
    waitbar(i/(M-1));
end    
close(h);
for i = 1:M
	x_r_aligned(:, i) = fftshift(fft(x_t_aligned(:, i)));
end
%imagesc_data(x_r_aligned, [0, M/PRF], [r(1), r(N)], 'slow time(s)', 'range(m)', 'aligned slow-time distance', 0);%'

%% compensate the phase %%%%%%%%%%%%%%%%
% select the strongest scatter as the reference point
[~, pos] = max(mean(abs(x_r_aligned')));        %'
x_r_ok = zeros(N, M);
x_t_ok = zeros(N, M);
x_r_ok(:, 1) = x_r_aligned(:, 1);
c_phase = zeros(1, M-1);
% for i = 1:M-1
%     c_phase(i) = x_r_aligned(pos, i+1)*conj(x_r_aligned(pos, i));
%     c_phase(i) = c_phase(i) / abs(c_phase(i));
% end
% for i = 2:M
%     for k = 1:i-1
%         x_r_ok(:, i) = x_r_aligned(:, i)*conj(c_phase(k));
%     end
% end
for i = 2:M
    c_phase(i-1) = x_r_aligned(pos, i)*conj(x_r_aligned(pos, 1));
    c_phase(i-1) = c_phase(i-1) / abs(c_phase(i-1));
end
for i = 2:M
    x_r_ok(:, i) = x_r_aligned(:, i)*conj(c_phase(i-1));
end

for i = 1:M
    x_t_ok(:, i) = ifft(ifftshift(x_r_ok(:, i)));
end

xita = pi/2 - angle(bulk_pos) - fai0;
ftemp = 2*(ts*K+fc)/c;    
dfu = 2*fc/c*(xita(2)-xita(1));
dfv = ftemp(2)-ftemp(1);

% ISAR_1 = ifftshift(ifft2(x_t_aligned, 4*N, 8*M));
% imagesc_data(ISAR_1, [-1/2/dfu, 1/2/dfu],  ...
% 	[-1/2/dfv, 1/2/dfv], 'cross(s)', 'range(m)', 'x-t-aligned', 0);
% 
% ISAR_2 = ifftshift(ifft2(x_t_ok, 4*N, 8*M));
% imagesc_data(ISAR_2, [-1/2/dfu, 1/2/dfu],  ...
% 	[-1/2/dfv, 1/2/dfv], 'cross(s)', 'range(m)', 'x-t-ok', 0);
% ISAR_1 = ifftshift(ifft2(x_t_aligned));
% imagesc_data(ISAR_1, [-1/2/dfu, 1/2/dfu],  ...
% 	[-1/2/dfv, 1/2/dfv], 'cross(s)', 'range(m)', 'x-t-aligned', 0);
% 
% ISAR_2 = ifftshift(ifft2(x_t_ok));
% imagesc_data(ISAR_2, [-1/2/dfu, 1/2/dfu],  ...
% 	[-1/2/dfv, 1/2/dfv], 'cross(s)', 'range(m)', 'x-t-ok', 0);

% ISAR_3 = ifftshift(ifft2(x_t, 4*N, 8*M));
% imagesc_data(ISAR_3, [-1/2/dfu, 1/2/dfu],  ...
% 	[-1/2/dfv, 1/2/dfv], 'cross(s)', 'range(m)', 'x-t', 0);

% %% N-MEA
% hrrp = x_r_aligned;
% [N,M] = size(hrrp);
% P = 6;
% % hrrp = hrrp.*(ones(N,1)*gausswin(M,5).');
% fai_temp = zeros(1,M);
% fai_new = ones(1,M);
% fai_old = fai_temp;
% % for m = 2:M
% %     fai_temp(m) = angle(sum(hrrp(:,m).*conj(hrrp(:,m-1))));
% %     fai_new(m) = -sum(fai_temp(1:m));
% % end
% % fai_new = ones(1,M);
%  %fai_new=angle(a);
%  %fai_new=fai0_dct;
% count=0;
% miu = 1e-3;
% e=[];
% fai1=[];
% dk = [];
% timee = [];
% %hrrp = awgn(hrrp,-10); 
% tic
% while   count<100%(max(abs(exp(j*fai_new)-exp(j*fai_old)))>miu)
%     %count<P%(max(abs(exp(j*fai_new)-exp(j*fai_old))) > miu) % && count<50%
% 
%     count=count+1;
%     fai_old = fai_new;
%     im = fty(hrrp.*(ones(N,1)*exp(j.*fai_old)));
%     dE = delt_E(im,hrrp,fai_old);
%     dE_2 = delt_E2(im,hrrp,fai_old);
%     fai_new = angle(exp(j*(fai_old-dE./(dE_2+eps))));
%     e=[e EntropyImage(im)];
% %     fai1=[fai1 fai_new'];
% %     dk1 = dE./(dE_2+eps);
% %     dk = [dk dk1'];
% 
%     %timee = [timee toc];
% 
% end
%  figure;plot(e,'LineWidth',2);
% % figure;imagesc(abs(im));
% toc
% 
% ret_data = hrrp.*(ones(N,1)*exp(j.*fai_new));
% fai = -fai_new;
% x2 = ret_data;
% figure('name','校正后初相'); imagesc(angle(x2));
% grid on;axis xy;
% %x3 = Keystone(x2,B,fc);
% Y=RDimaging(x2); %直接FFT
% G2=20*log10(abs(Y)./max(abs(Y(:))));
% figure('name','ISAR成像结果dB');
% imagesc(G2);caxis([-15,0]);
% grid on;axis xy;colorbar;ftresize(13);%axis equal;
% xlabel('azimuth');ylabel('range (m)');

%  
%--------------------DN最小熵------------------
% function [ret_data fai]=PhaseComp_MinEntropy_NI(hrrp,miu,P)
% 牛顿迭代
% hrrp: 对齐后包络，快时间*慢时间
% miu: 迭代终止条件
% P：迭代步数
x1=x_r_aligned;
x2=PhaseComp_MidTr(x1);  
 hrrp=x2;
[N,M] = size(hrrp);
P = 6;
% hrrp = hrrp.*(ones(N,1)*gausswin(M,5).');
fai_temp = zeros(1,M);

fai_old = fai_temp;
% for m = 2:M
%     fai_temp(m) = angle(sum(hrrp(:,m).*conj(hrrp(:,m-1))));
%     fai_new(m) = -sum(fai_temp(1:m));
% end
fai_new = ones(1,M);

p =[];
beta0=0;
h0= 0.1;
%hrrp = awgn(hrrp,-5);
%timee = [];tic
count=0;
miu = 1e-3;
e=[];
c= [];
fai1=[];
dk = [];
timee = [];
while   count<200
    %count<P%(max(abs(exp(j*fai_new)-exp(j*fai_old))) > miu) % && count<50%

    count=count+1;
    fai_old = fai_new;
    im = fty(hrrp.*(ones(N,1)*exp(j.*fai_old)));
    dE = delt_E(im,hrrp,fai_old);
    dE_2 = delt_E2(im,hrrp,fai_old);
    fai_new = angle(exp(j*(fai_old-dE./(dE_2+eps))));
    e=[e EntropyImage(im)];
    c=[c contrast(im)];
  %  fai1=[fai1 fai_new'];
   dk1 = dE./(dE_2+eps);
    dk = [dk dk1'];

    %timee = [timee toc];
end
%toc
figure;plot(e,'LineWidth',2);
% figure;imagesc(abs(im));
ret_data = hrrp.*(ones(N,1)*exp(j.*fai_new));
fai = -fai_new;
x2 = ret_data;

Y=RDimaging(x2); %直接FFT
G2=20*log10(abs(Y)./max(abs(Y(:))));
% xx2=linspace(-disrough_a*4,disrough_a*4,1024);
% yy2=linspace(-disrough_r*4,disrough_r*4,1024);
figure('name','ISAR成像结果dB');
imagesc([-1/2/dfu, 1/2/dfu],[-1/2/dfv, 1/2/dfv],G2);caxis([-30,0]);
% imagesc_data(x2, [-1/2/dfu, 1/2/dfu],  ...
% 	[-1/2/dfv, 1/2/dfv], 'cross(s)', 'range(m)', 'x-t-ok', 0);
grid on;axis xy;colorbar;ftresize(13);%axis equal;
xlabel('azimuth');ylabel('range (m)');

%% ground truth
figure
u = x*cos(fai0)-y*sin(fai0);
v = x*sin(fai0)+y*cos(fai0);
plot(u, v, 'ko');
xlabel('cross(m)');
ylabel('range(m)');
title('ground truth');


