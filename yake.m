
%tic;
clear all
close all
clc
load 'data';
s1=fft(s);%fft对矩阵按列进行傅里叶变换
for i=1:256;
   s1(:,i)=fftshift(s1(:,i));%把直流分量移到图像中
end
 
figure(1);

contour(abs(s1));
title('雅克42对准前的数据');



x1=RangeAlign_Corr(s,8,30);                 %以两包络相关系数最大为包络对齐标准（数据，插值，窗）ok
% x1=RangeAlign_MinEnt(data,1,30,30);        %最小熵包络对齐（数据，插值，对齐窗，搜索窗）ok
% x1=RangeAlign_MinEnt_Refine(data,1,30,30); %改进的最小熵法（数据，插值，对齐窗，搜索窗）ok
% x1=RangeAlign_All(data,1,30,30,2);         %基于模1、模2-距离最小准则（数据，插值，对齐窗,搜索窗,1/2）ok
% x1=linej(data,8);                              %相关精对齐方法,(数据，插值)ok
figure('name','包络对齐');
contour(abs(x1));
title('一维距离像——包络对齐后');



[x2,e]=PhaseComp_MidTr(x1);  
miu = 1e-3;
P=150;
x4=Comp_MinEntropy(x1,1e-3);

[ret_data fai]=PhaseComp_MinEntropy_NI1(x2,miu,P);
% 
 x3 = ret_data;


 %x2=PhaseComp_MidTr(x1);         %中心跟踪方法,ok
% x2=PhaseComp_SingP(x1);         %单特显点,ok
 %x2=CompensateByMultiProNo(x1,30);  %多特显点(初相差估计)(x1,特显点),ok
% x2=wmsax(x1,0.2);               %加权多特显点方法(x1,门限)
% x2=Compensate_corr(x1);              %互相关相位对准(即相位精补偿),ok
 %x2=Comp_MinEntropy(x1,0.5);    %基于最小熵原则的相位精补偿,ok,但是方位位置不对
 %x2=IcsaAutofocus(x1,2000,3);     %高精度迭代相干积累自聚焦函数,ok
 %x2=PhaseComp_PGA(x1,10);           %PGA法(x1,迭代次数),ok,但是方位位置不对
Im=EntropyImage(x2);
% figure,imagesc(abs(Y));axis xy;
Y=RDimaging(x3); %直接FFT
G=20*log10(abs(Y)./max(abs(Y(:))));
figure('name','ISAR成像结果dB');
imagesc(G);caxis([-30,0]);
grid on;axis xy;colorbar;%axis equal;
% axis([-0.6 0.6 y(select_row(1)) y(select_row(end))]);%set(gca,'xtick',[-0.2 0 0.2]);
xlabel('azimuth');ylabel('range (m)');
%