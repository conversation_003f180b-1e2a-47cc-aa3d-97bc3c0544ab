% 加载仿真数据
load data_airplane;
disp('加载仿真数据完成');
c=3e8;
%% 补偿残余视频相位（RVP）
% 频率向量
if mod(N, 2) ~= 0
    f = (-(N-1)/2:((N-1)/2)) * fs / N;
else
    f = (-N/2:N/2-1) * fs / N;
end
df = f(2) - f(1);
r = -f * c / (2 * K);
x_r = zeros(N, M);
for i = 1:M
    x_r(:, i) = fftshift(fft(raw(:, i)));
    % 补偿 RVP
    x_r(:, i) = x_r(:, i) .* exp(-1j * pi * f .* f / K).';
end
x_t = zeros(N, M);
for i = 1:M
    x_t(:, i) = ifft(ifftshift(x_r(:, i)));
end
disp('RVP 补偿完成');

%% 距离对齐（Range Alignment）
factor = 8;
NN = factor * N;
x_r_en = zeros(NN, M);
x_t_aligned = zeros(N, M);
x_r_aligned = zeros(N, M);
x_r_en(:, 1) = abs(fftshift(fft(x_t(:, 1), NN)));
x_t_aligned(:, 1) = x_t(:, 1);

% 偏移量记录
offset = zeros(1, M-1);
h = waitbar(0, '对齐包络中...');
for i = 1:M-1
    x_r_en(:, i+1) = abs(fftshift(fft(x_t(:, i+1), NN)));
    correlation = fftshift(ifft(fft(x_r_en(:, i), 2*NN-1) .* conj(fft(x_r_en(:, i+1), 2*NN-1))));
    [~, offset(i)] = max(abs(correlation));
    offset(i) = offset(i) - NN;
    if offset(i) >= NN/2
        offset(i) = offset(i) - NN;
    elseif offset(i) < -NN/2
        offset(i) = offset(i) + NN;
    end
    x_t_aligned(:, i+1) = x_t(:, i+1) .* exp(1j * 2 * pi * df / factor * offset(i) * ts).';
    x_r_en(:, i+1) = abs(fftshift(fft(x_t_aligned(:, i+1), NN)));
    waitbar(i/(M-1));
end
close(h);
for i = 1:M
    x_r_aligned(:, i) = fftshift(fft(x_t_aligned(:, i)));
end
disp('距离对齐完成');

%% 基于 FRFT 的相位补偿
% 参数设置
%% 基于 FRFT 的相位补偿
% 参数设置
K = 5; % FRFT 基函数数量
% MODIFIED LINE BELOW:
alpha_p_range = 0.01 : 0.02 : 1.99; % FRFT 阶数参数 p 范围 (alpha = p * pi/2)
                                  % This avoids p=0 (alpha=0) and p=2 (alpha=pi)
alpha_range = alpha_p_range * pi / 2; % 实际阶数范围
energy_concentration = zeros(length(alpha_range), 1);

% 选择最强距离单元的慢时间信号
[~, pos] = max(mean(abs(x_r_aligned')));
signal_ref = x_r_aligned(pos, :);

% 阶数搜索：计算不同 alpha 下的能量集中度
disp('Starting FRFT order search...'); % Add for progress
h_frft_search = waitbar(0, 'FRFT order search...');
for idx = 1:length(alpha_range)
    alpha = alpha_range(idx);
    % 调用 FRFT 函数（确保 frft.m is robust for this alpha range)
    try
        frft_signal = frft(signal_ref, alpha);
        if any(isnan(frft_signal)) || any(isinf(frft_signal))
            disp(['Warning: frft returned NaN/Inf for alpha_p = ', num2str(alpha_p_range(idx))]);
            energy_concentration(idx) = -Inf; % Penalize problematic alphas
        else
            energy_concentration(idx) = max(abs(frft_signal)); % 峰值幅度作为集中度
        end
    catch ME
        disp(['Error in frft for alpha_p = ', num2str(alpha_p_range(idx)), ': ', ME.message]);
        energy_concentration(idx) = -Inf; % Penalize problematic alphas
    end
    waitbar(idx/length(alpha_range), h_frft_search);
end
close(h_frft_search);
disp('FRFT 阶数搜索完成');

% Check if energy_concentration has valid peaks
if all(isinf(energy_concentration))
    error('FRFT order search failed to find valid peaks. Check frft.m or alpha_range.');
end

% 选择前 K 个最优阶数
[~, sorted_idx] = sort(energy_concentration, 'descend');
alpha_candidates = alpha_range(sorted_idx(1:K));
disp('Selected alpha_candidates (in radians):');
disp(alpha_candidates);

% 构建 FRFT 基函数矩阵
m = 0:M-1;
% OPTIONAL: Use centered m for Psi if your FRFT definition implies it
% m_centered = m - (M-1)/2; 
Psi = zeros(M, K);
for k_idx = 1:K % Changed loop variable to avoid conflict with global K if any
    alpha_k = alpha_candidates(k_idx);
    % Check for cot(alpha_k) issues again, though modified alpha_range should prevent Inf
    if abs(sin(alpha_k)) < 1e-9 % Should not happen with modified alpha_range
        disp(['Warning: alpha_k = ', num2str(alpha_k), ' is too close to 0 or pi for cot. Setting Psi column to 1.']);
        Psi(:, k_idx) = 1;
    elseif abs(cos(alpha_k)) < 1e-9 % alpha_k is pi/2 or 3pi/2. cot is 0.
        Psi(:, k_idx) = 1; % exp(0) = 1
    else
        % Using m (0 to M-1) for now. If issues persist, try m_centered.
        Psi(:, k_idx) = exp(1j * (pi/2) * cot(alpha_k) * m.^2).'; % FRFT 相位基函数
    end
end

% Check Psi for NaN/Inf
if any(isnan(Psi(:))) || any(isinf(Psi(:)))
    error('Psi matrix contains NaN or Inf values. Problem with alpha_candidates or cot calculation.');
end
% 系数优化：使用简单遗传算法（简化版，实际可使用 MATLAB 的 ga 函数）
P = 50; % 种群大小
max_iter = 100; % 最大迭代次数
a_population = randn(P, K) * 0.1; % 初始种群
best_fitness = -Inf;
best_a = zeros(1, K);

h = waitbar(0, '优化 FRFT 系数中...');
for iter = 1:max_iter
    fitness = zeros(P, 1);
    for p = 1:P
        a = a_population(p, :);
        phi_error = Psi * a'; % 维度为 (M, 1)
        % 广播 phi_error 到 (N, M) 维度
        phi_error_mat = repmat(phi_error', [N, 1]);
        x_r_comp = x_r_aligned .* exp(-1j * phi_error_mat);
        x_t_comp = ifft(ifftshift(x_r_comp, 1), [], 1);
        ISAR_img = ifftshift(ifft2(x_t_comp));
        entropy = -sum(abs(ISAR_img(:)).^2 .* log(abs(ISAR_img(:)).^2 + eps)) / sum(abs(ISAR_img(:)).^2);
        fitness(p) = 1 / (entropy + eps); % 适应度：熵的倒数
    end
    
    % 选择最优个体
    [max_fit, idx] = max(fitness);
    if max_fit > best_fitness
        best_fitness = max_fit;
        best_a = a_population(idx, :);
    end
    
    % 选择（轮盘赌）
    prob = fitness / sum(fitness);
    selected_idx = randsample(1:P, P, true, prob);
    new_population = a_population(selected_idx, :);
    
    % 交叉（简单平均）
    for p = 1:2:P-1
        if rand < 0.8 % 交叉概率
            new_population(p, :) = 0.5 * (new_population(p, :) + new_population(p+1, :));
            new_population(p+1, :) = new_population(p, :);
        end
    end
    
    % 变异
    mutation_rate = 0.1;
    mutation_mask = rand(P, K) < mutation_rate;
    new_population(mutation_mask) = new_population(mutation_mask) + randn(sum(mutation_mask(:)), 1) * 0.05;
    
    a_population = new_population;
    waitbar(iter/max_iter);
end
close(h);
disp('FRFT 系数优化完成');

% 使用最优系数进行相位补偿
phi_error_opt = Psi * best_a'; % 维度为 (M, 1)
phi_error_opt_mat = repmat(phi_error_opt', [N, 1]); % 广播到 (N, M)
x_r_comp_opt = x_r_aligned .* exp(-1j * phi_error_opt_mat);
x_t_comp_opt = ifft(ifftshift(x_r_comp_opt, 1), [], 1);

%% 最终成像
% 计算 ISAR 图像
ISAR_img = ifftshift(ifft2(x_t_comp_opt));
G = 20 * log10(abs(ISAR_img) / max(abs(ISAR_img(:))));

% 显示成像结果
figure('Name', 'ISAR 成像结果 (FRFT 补偿)');
imagesc(G); caxis([-30, 0]);
grid on; axis xy; colorbar;
xlabel('方位向 (多普勒维)'); ylabel('距离向 (m)');
title('ISAR 成像结果 (基于 FRFT 的相位补偿)');
disp('成像完成');

%% 显示地面真实分布（Ground Truth）
figure('Name', '地面真实分布');
u = x * cos(fai0) - y * sin(fai0);
v = x * sin(fai0) + y * cos(fai0);
plot(u, v, 'ko');
xlabel('方位向 (m)'); ylabel('距离向 (m)');
title('地面真实分布 (飞机散射点)');
grid on;