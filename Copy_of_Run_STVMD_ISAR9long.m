%-------------------------------------------------------------------------%
%--------        运行基于短时变分模态分解的ISAR成像算法                -------%
%--------        专为三维转动目标设计，解决散焦问题                    -------%
%-------------------------------------------------------------------------%
clc; clear all; close all;

% 加载原始数据
%load shipx2.mat;
 load simulation_ship_s_r_tm2.mat
%load MIG255.MAT
%load pointx2.mat
%shipx2 = ifft(X);
shipx2=s_r_tm2;
% 显示原始数据的基本信息
[N_r, N_tm] = size(shipx2);
fprintf('原始数据尺寸: %d x %d (距离单元 x 方位单元)\n', N_r, N_tm);

% 显示传统FFT成像结果（用于比较）
Y_original = fftshift(fft(shipx2, [], 2), 2);
max_val_orig = max(abs(Y_original(:)));
G1_original = 20*log10(abs(Y_original)./max_val_orig);

figure('name','传统FFT成像结果');
imagesc(G1_original);
caxis([-30,0]);
grid on;
axis xy;
colorbar;
xlabel('方位单元 (Azimuth Cell / Doppler)');
ylabel('距离单元 (Range Cell)');
colormap jet;
title('传统FFT成像结果 (dB)');

% 设置STVMD ISAR算法参数
params = struct();
params.K = 5;                      % 最大模态数
params.alpha = 2000;               % 平衡参数
params.tau = 0.1;                  % 拉格朗日乘子更新步长
params.tol = 1e-7;                 % 收敛容限
params.window_sizes = [16, 32, 64]; % 多尺度窗口大小
params.overlap = 0.75;             % 窗口重叠率（增加重叠以提高时间分辨率）
params.dynamic = true;             % 使用动态中心频率
params.max_iter = 300;             % 最大迭代次数
params.global_iterations = 3;      % 全局迭代次数
params.display_progress = true;    % 显示处理进度
params.display_intermediate = true; % 显示中间结果

% 运行STVMD ISAR成像算法
fprintf('开始运行STVMD ISAR成像算法...\n');
tic;
[ISAR_image_stvmd, s_compensated] = STVMD_ISARlong(shipx2, params);
processing_time = toc;
fprintf('处理时间: %.2f 秒\n', processing_time);

% 显示STVMD ISAR成像结果
figure('name', 'STVMD ISAR成像结果 (dB)');
max_val = max(abs(ISAR_image_stvmd(:)));
if max_val == 0, max_val = 1; end
ISAR_image_stvmd_db = 20*log10(abs(ISAR_image_stvmd) / max_val);

imagesc(ISAR_image_stvmd_db);
caxis([-30, 0]);
grid on;
axis xy;
colorbar;
xlabel('方位单元 (Azimuth Cell / Doppler)');
ylabel('距离单元 (Range Cell)');
colormap jet;
title('STVMD ISAR成像结果 (dB)');

% 保存结果
save('STVMD_ISAR_Results.mat', 'ISAR_image_stvmd', 's_compensated');

% 显示时频分析结果（选择一个有代表性的距离单元）
% 寻找能量最大的距离单元
[~, max_energy_range_bin] = max(sum(abs(shipx2).^2, 2));
fprintf('选择距离单元 %d 进行时频分析可视化\n', max_energy_range_bin);

% 原始信号的时频分析
figure('name', '原始信号与补偿后信号的时频分析');

% 原始信号
subplot(2,2,1);
plot(abs(shipx2(max_energy_range_bin, :)));
title('原始信号幅度');
xlabel('方位采样点');
ylabel('幅度');
grid on;

subplot(2,2,2);
plot(unwrap(angle(shipx2(max_energy_range_bin, :))));
title('原始信号相位');
xlabel('方位采样点');
ylabel('相位 (rad)');
grid on;

% 补偿后信号
subplot(2,2,3);
plot(abs(s_compensated(max_energy_range_bin, :)));
title('补偿后信号幅度');
xlabel('方位采样点');
ylabel('幅度');
grid on;

subplot(2,2,4);
plot(unwrap(angle(s_compensated(max_energy_range_bin, :))));
title('补偿后信号相位');
xlabel('方位采样点');
ylabel('相位 (rad)');
grid on;

% 显示原始信号和补偿后信号的STFT时频图
figure('name', '时频分析');

% 原始信号STFT
subplot(2,1,1);
window_size = 32;
overlap_points = floor(window_size * 0.75);
nfft = 256;
[S, F, T] = spectrogram(shipx2(max_energy_range_bin, :), hamming(window_size), overlap_points, nfft, 1, 'yaxis');
imagesc(T, F, 20*log10(abs(S)/max(abs(S(:)))));
axis xy;
colormap jet;
colorbar;
caxis([-40, 0]);
title('原始信号时频图');
xlabel('时间');
ylabel('归一化频率');

% 补偿后信号STFT
subplot(2,1,2);
[S_comp, F_comp, T_comp] = spectrogram(s_compensated(max_energy_range_bin, :), hamming(window_size), overlap_points, nfft, 1, 'yaxis');
imagesc(T_comp, F_comp, 20*log10(abs(S_comp)/max(abs(S_comp(:)))));
axis xy;
colormap jet;
colorbar;
caxis([-40, 0]);
title('补偿后信号时频图');
xlabel('时间');
ylabel('归一化频率');

fprintf('处理完成。结果已保存到 STVMD_ISAR_Results.mat\n');
EntropyImage(ISAR_image_stvmd+eps)
contrast(ISAR_image_stvmd)
EntropyImage(G1_original+eps)
contrast(G1_original)
