%% Test_AS_PCFT_Core - AS-PCFT核心功能测试
%
% 本脚本测试AS-PCFT算法的核心组件：
% 1. 多阶PCFT核函数
% 2. 原子范数最小化
% 3. 稀疏-低秣耦合正则化

clear; close all; clc;

fprintf('=== AS-PCFT核心功能测试 ===\n\n');

%% 测试1：多阶PCFT核函数
fprintf('测试1: 多阶PCFT核函数...\n');

% 生成测试信号
N = 128;
tm_norm = (0:N-1) / N;

% 创建包含多阶相位的测试信号
f0 = 0.1;
alpha = 20;
beta = 100;
gamma = 50;

phase_true = 2*pi * (f0*tm_norm + 0.5*alpha*tm_norm.^2 + (1/6)*beta*tm_norm.^3 + (1/24)*gamma*tm_norm.^4);
test_signal = exp(1j * phase_true) + 0.1*(randn(1,N) + 1j*randn(1,N));

% 测试不同阶数的PCFT
orders_to_test = [0, 1, 2, 3, 4];
phase_params_test = [f0, alpha, beta, gamma, 0];

fprintf('  测试不同阶数的PCFT变换:\n');
for order = orders_to_test
    try
        [X_pcft, comp_time] = Multi_Order_PCFT_Core(test_signal, phase_params_test(1:order+1), tm_norm, order);
        peak_ratio = max(abs(X_pcft)) / mean(abs(X_pcft));
        fprintf('    阶数 %d: 峰值比 = %.2f, 计算时间 = %.4f秒\n', order, peak_ratio, comp_time);
    catch ME
        fprintf('    阶数 %d: 错误 - %s\n', order, ME.message);
    end
end

%% 测试2：原子范数最小化
fprintf('\n测试2: 原子范数最小化...\n');

% 创建多原子测试信号
K_atoms = 3;
N_test = 64;
tm_test = (0:N_test-1) / N_test;
multi_atom_signal = zeros(1, N_test, 'like', 1j);

% 添加多个原子
atom_params = [
    0.1, 10, 50, 0;    % 原子1
    -0.2, -15, 80, 0;  % 原子2
    0.05, 25, -60, 0   % 原子3
];

for k = 1:K_atoms
    f_k = atom_params(k, 1);
    alpha_k = atom_params(k, 2);
    beta_k = atom_params(k, 3);

    phase_k = 2*pi * (f_k*tm_test + 0.5*alpha_k*tm_test.^2 + (1/6)*beta_k*tm_test.^3);
    multi_atom_signal = multi_atom_signal + exp(1j * phase_k);
end

% 添加噪声
multi_atom_signal = multi_atom_signal + 0.2*(randn(1,N_test) + 1j*randn(1,N_test));

% 测试原子范数最小化
try
    % 直接定义原子范数参数
    atomic_params = struct();
    atomic_params.island_threshold_ratio = 0.1;
    atomic_params.island_merge_distance = 3;
    atomic_params.max_atoms = 10;
    atomic_params.local_window_size = 32;
    atomic_params.init_noise_level = 0.01;
    atomic_params.max_iterations = 20; % 减少迭代次数用于测试
    atomic_params.tolerance = 1e-6;
    atomic_params.lambda_atomic = 0.01;
    atomic_params.step_size_amplitude = 0.01;
    atomic_params.step_size_phase = 0.001;
    atomic_params.alpha_min = -100; atomic_params.alpha_max = 100;
    atomic_params.beta_min = -1000; atomic_params.beta_max = 1000;
    atomic_params.gamma_min = -5000; atomic_params.gamma_max = 5000;

    [A_optimal, a_optimal, convergence_info] = Atomic_Norm_Minimization(multi_atom_signal, atomic_params);

    fprintf('  原子范数最小化结果:\n');
    fprintf('    检测到原子数: %d\n', size(a_optimal, 1));
    fprintf('    收敛状态: %s\n', convergence_info.converged , '收敛' : '未收敛');
    fprintf('    迭代次数: %d\n', convergence_info.iterations);
    fprintf('    最终误差: %.2e\n', convergence_info.final_error);

catch ME
    fprintf('  原子范数最小化错误: %s\n', ME.message);
end

%% 测试3：稀疏-低秩耦合正则化
fprintf('\n测试3: 稀疏-低秩耦合正则化...\n');

% 创建稀疏+低秩测试信号
N_sparse = 64;
sparse_signal = zeros(1, N_sparse, 'like', 1j);

% 添加稀疏成分
sparse_indices = [10, 25, 40, 55];
sparse_signal(sparse_indices) = [2, 1.5, 1.8, 1.2] .* exp(1j*2*pi*rand(1,4));

% 添加低秩成分（通过Hankel结构）
low_rank_component = sin(2*pi*0.1*(0:N_sparse-1)) + 0.5*sin(2*pi*0.3*(0:N_sparse-1));
sparse_signal = sparse_signal + low_rank_component;

% 添加噪声
noisy_signal = sparse_signal + 0.3*(randn(1,N_sparse) + 1j*randn(1,N_sparse));

% 测试稀疏-低秩正则化
try
    % 直接定义稀疏-低秩参数
    sparse_lr_params = struct();
    sparse_lr_params.algorithm = 'admm';
    sparse_lr_params.lambda_sparse = 0.1;
    sparse_lr_params.lambda_lowrank = 0.05;
    sparse_lr_params.max_iterations = 20; % 减少迭代次数用于测试
    sparse_lr_params.tolerance = 1e-4;
    sparse_lr_params.rho = 1.0;
    sparse_lr_params.tau = 0.01;
    sparse_lr_params.sigma = 0.01;
    sparse_lr_params.theta = 1.0;
    sparse_lr_params.sparse_weight = 0.6;
    sparse_lr_params.hankel_size = 32;

    [X_optimal, convergence_info] = Sparse_LowRank_Regularization(noisy_signal, sparse_signal, sparse_lr_params);

    fprintf('  稀疏-低秩正则化结果:\n');
    fprintf('    收敛状态: %s\n', convergence_info.converged ? '收敛' : '未收敛');
    fprintf('    迭代次数: %d\n', convergence_info.iterations);
    fprintf('    最终误差: %.2e\n', convergence_info.final_error);

    % 计算重构误差
    reconstruction_error = norm(X_optimal - sparse_signal) / norm(sparse_signal);
    fprintf('    重构误差: %.2e\n', reconstruction_error);

catch ME
    fprintf('  稀疏-低秩正则化错误: %s\n', ME.message);
end

%% 测试4：AS-PCFT完整流程
fprintf('\n测试4: AS-PCFT完整流程...\n');

% 生成简单的ISAR仿真数据
N_range = 32;
N_azimuth = 64;
echo_test = zeros(N_range, N_azimuth, 'like', 1j);

% 添加几个散射点
scatter_points = [
    8, 0.05, 15, 80;    % [range_idx, freq, alpha, beta]
    16, -0.1, -20, 120;
    24, 0.08, 10, -60
];

tm_azimuth = (0:N_azimuth-1) / 1000; % 假设PRF=1000Hz

for i = 1:size(scatter_points, 1)
    r_idx = scatter_points(i, 1);
    f_i = scatter_points(i, 2);
    alpha_i = scatter_points(i, 3);
    beta_i = scatter_points(i, 4);

    phase_i = 2*pi * (f_i*tm_azimuth + 0.5*alpha_i*tm_azimuth.^2 + (1/6)*beta_i*tm_azimuth.^3);
    echo_test(r_idx, :) = exp(1j * phase_i);
end

% 添加噪声
echo_test = echo_test + 0.2*(randn(N_range, N_azimuth) + 1j*randn(N_range, N_azimuth));

% 测试AS-PCFT完整算法
try
    % 直接定义AS-PCFT参数
    as_pcft_params = struct();
    as_pcft_params.radar.PRF = 1000;
    as_pcft_params.preprocessing.remove_dc = true;
    as_pcft_params.preprocessing.range_alignment = false;
    as_pcft_params.preprocessing.apply_window = true;
    as_pcft_params.processing.block_size = 16;
    as_pcft_params.energy_threshold = 1e-8;
    as_pcft_params.coarse.alpha_min = -50;
    as_pcft_params.coarse.alpha_max = 50;
    as_pcft_params.coarse.alpha_step = 5;
    as_pcft_params.coarse.beta_min = -500;
    as_pcft_params.coarse.beta_max = 500;
    as_pcft_params.coarse.beta_step = 50;
    as_pcft_params.atomic.max_iter = 10;  % 减少迭代次数
    as_pcft_params.atomic.tolerance = 1e-6;
    as_pcft_params.atomic.refinement_factor = 10;
    as_pcft_params.sparse_lr.lambda_sparse = 0.1;
    as_pcft_params.sparse_lr.lambda_lowrank = 0.05;
    as_pcft_params.sparse_lr.max_iter = 10;
    as_pcft_params.sparse_lr.tolerance = 1e-4;
    as_pcft_params.postprocessing.contrast_enhancement = true;

    fprintf('  运行AS-PCFT完整算法...\n');
    tic;
    [ISAR_image, processing_info] = AS_PCFT_ISAR_Framework(echo_test, as_pcft_params);
    total_time = toc;

    fprintf('  AS-PCFT完整流程结果:\n');
    fprintf('    处理时间: %.2f秒\n', total_time);
    fprintf('    输出图像维度: %d × %d\n', size(ISAR_image, 1), size(ISAR_image, 2));
    fprintf('    平均重构误差: %.2e\n', processing_info.mean_error);

    % 计算图像质量指标
    image_magnitude = abs(ISAR_image);
    contrast = std(image_magnitude(:)) / mean(image_magnitude(:));
    peak_ratio = max(image_magnitude(:)) / mean(image_magnitude(:));

    fprintf('    图像对比度: %.2f\n', contrast);
    fprintf('    峰值比: %.2f\n', peak_ratio);

catch ME
    fprintf('  AS-PCFT完整流程错误: %s\n', ME.message);
    fprintf('  错误堆栈:\n');
    for i = 1:length(ME.stack)
        fprintf('    %s (行 %d)\n', ME.stack(i).name, ME.stack(i).line);
    end
end

%% 性能对比测试
fprintf('\n测试5: 性能对比...\n');

% 简单的FFT基线
tic;
ISAR_fft = fftshift(fft(echo_test, [], 2), 2);
fft_time = toc;

% 计算FFT结果的质量指标
fft_magnitude = abs(ISAR_fft);
fft_contrast = std(fft_magnitude(:)) / mean(fft_magnitude(:));
fft_peak_ratio = max(fft_magnitude(:)) / mean(fft_magnitude(:));

fprintf('  FFT基线结果:\n');
fprintf('    处理时间: %.4f秒\n', fft_time);
fprintf('    图像对比度: %.2f\n', fft_contrast);
fprintf('    峰值比: %.2f\n', fft_peak_ratio);

if exist('ISAR_image', 'var') && exist('total_time', 'var')
    fprintf('\n  性能提升:\n');
    fprintf('    对比度提升: %.1f%%\n', (contrast - fft_contrast) / fft_contrast * 100);
    fprintf('    峰值比提升: %.1f%%\n', (peak_ratio - fft_peak_ratio) / fft_peak_ratio * 100);
    fprintf('    计算时间比: %.1fx\n', total_time / fft_time);
end

fprintf('\n=== AS-PCFT核心功能测试完成 ===\n');

%% 可视化结果（如果有图像输出）
if exist('ISAR_image', 'var')
    figure('Position', [100, 100, 1000, 400]);

    subplot(1, 2, 1);
    imagesc(20*log10(abs(ISAR_fft) + eps));
    colormap(gray); colorbar;
    title('FFT基线结果');
    xlabel('多普勒单元'); ylabel('距离单元');

    subplot(1, 2, 2);
    imagesc(20*log10(abs(ISAR_image) + eps));
    colormap(gray); colorbar;
    title('AS-PCFT结果');
    xlabel('多普勒单元'); ylabel('距离单元');

    sgtitle('AS-PCFT vs FFT性能对比');

    % 保存图像
    saveas(gcf, 'AS_PCFT_Test_Results.png');
    fprintf('\n对比图像已保存为: AS_PCFT_Test_Results.png\n');
end
