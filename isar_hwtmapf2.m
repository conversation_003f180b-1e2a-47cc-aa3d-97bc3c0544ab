function z = isar_hwtmapf2(xf, w, len)
    k = length(w);
    half_k = floor(k / 2);
    z = zeros(len, len);

    % ADMM参数设置
    max_iter = 100;
    rho = 1.0;
    lambda = 0.1;

    % 预计算FFT矩阵 - 加速方法1
    F = dftmtx(len)/sqrt(len);

    % 并行处理 - 加速方法2
    for ii = 1:len
        % 获取当前时间窗口的信号
        start_idx = max(1, ii - half_k);
        end_idx = min(len, ii + half_k - 1);
        window_length = end_idx - start_idx + 1;
        y = xf(start_idx:end_idx).';

        % 快速构建基矩阵 - 加速方法3
        H = construct_fast_hwt(F, w, start_idx, end_idx, len);

        % 预计算和缓存不变量 - 加速方法4
        HtH = H'*H;
        Hty = H'*y;
        L = chol(HtH + rho*eye(len), 'lower'); % Cholesky分解 - 加速方法5

        % 初始化ADMM变量
        x = zeros(len, 1);
        z_var = zeros(len, 1);
        u = zeros(len, 1);

        % ADMM迭代 - 使用快速求解
        for iter = 1:max_iter
            % 使用Cholesky分解求解
            b = Hty + rho*(z_var - u);
            x = L' \ (L \ b);

            % 向量化的软阈值 - 加速方法6
            z_var = sign(x + u) .* max(abs(x + u) - lambda/rho, 0);

            % 更新u
            u = u + (x - z_var);

            % 快速收敛检查 - 加速方法7
            if mod(iter, 5) == 0 && norm(x - z_var) < 1e-4
                break;
            end
        end

        z(:,ii) = abs(x).^2;
    end
end

% 快速构建谐波小波基矩阵
function H = construct_fast_hwt(F, w, start_idx, end_idx, len)
    window_length = end_idx - start_idx + 1;
    H = zeros(window_length, len);

    % 使用FFT矩阵构建基矩阵
    for m = 1:window_length
        t = start_idx + m - 2;
        H(m,:) = w(m) * F(t+1,:);
    end
end

% 预计算DFT矩阵
function F = dftmtx(n)
    F = zeros(n,n);
    for k = 0:n-1
        for j = 0:n-1
            F(k+1,j+1) = exp(-2*pi*1i*k*j/n);
        end
    end
    F = F/sqrt(n);
end


% 
% % 
% % % ISARbeifen.m
% % % Enhanced ISAR Imaging with ADMM Optimization for Harmonic Basis Construction
% % 
% % % Modified isar_hwtmapf2 Function
% % 使用ADMM算法优化谐波基构建
% function z = isar_hwtmapf2(xf, w, len)
%     k = length(w);
%     half_k = floor(k / 2);
%     z = zeros(len, len);
% 
%     % ADMM Parameters
%     max_iter = 100;
%     rho = 1.0;
%     lambda = 0.1;
% 
%     % Precompute FFT matrix for harmonic basis
%     F = dftmtx(len) / sqrt(len);
% 
%     for ii = 1:len
%         % Define window indices
%         start_idx = max(1, ii - half_k);
%         end_idx = min(len, ii + half_k - 1);
%         window_length = end_idx - start_idx + 1;
% 
%         % Extract windowed signal
%         y = xf(start_idx:end_idx).';
% 
%         % Pad y with zeros if window_length < len
%         if window_length < len
%             y_padded = [y; zeros(len - window_length, 1)];
%         else
%             y_padded = y;
%         end
% 
%         % Construct harmonic wavelet transform basis
%         H = construct_harmonic_basis(F, w, start_idx, end_idx, len, window_length);
% 
%         % Precompute H'H and H'y for ADMM
%         HtH = H' * H;          % [len x len] * [len x len] = [len x len]
%         Hty = H' * y_padded;    % [len x len] * [len x 1] = [len x 1]
% 
%         % Cholesky decomposition for efficient solves
%         L = chol(HtH + rho * eye(len), 'lower');
% 
%         % Initialize ADMM variables
%         x = zeros(len, 1);
%         z_var = zeros(len, 1);
%         u = zeros(len, 1);
% 
%         % ADMM Iterations
%         for iter = 1:max_iter
%             % Update x by solving (H'H + rho I)x = H'y + rho(z - u)
%             b = Hty + rho * (z_var - u);
%             x = L \ (L' \ b);
% 
%             % Update z_var with soft thresholding (sparsity promotion)
%             z_var = soft_threshold(x + u, lambda / rho);
% 
%             % Update dual variable u
%             u = u + (x - z_var);
% 
%             % Convergence check every 5 iterations
%             if mod(iter, 5) == 0 && norm(x - z_var) < 1e-4
%                 break;
%             end
%         end
% 
%         % Reconstruct signal and store power
%         signal_recon = H * x;
%         z(:, ii) = abs(signal_recon).^2;
%     end
% end
% 
% % Helper function to construct harmonic wavelet transform basis with adaptive weights
% function H = construct_harmonic_basis(F, w, start_idx, end_idx, len, window_length)
%     H = zeros(len, len); %% Ensuring H is always [len x len]
% 
%     for m = 1:window_length
%         t = start_idx + m - 2;
%         % Adaptive weight based on symmetric modulation
%         adaptive_weight = w(m) * (1 + 0.5 * cos(2 * pi * t / len));
%         H(:, m) = adaptive_weight * F(t + 1, :).';
%     end
% 
%     % Apply sparse constraint (e.g., limiting to top 10% non-zero elements)
%     sparsity_level = 0.1; % 10% non-zero
%     threshold = prctile(abs(H(:)), 100 * (1 - sparsity_level));
%     mask = abs(H) > threshold;
%     H = H .* mask;
% end
% 
% % Soft thresholding operator for sparsity
% function z = soft_threshold(x, thresh)
%     z = sign(x) .* max(abs(x) - thresh, 0);
% end
% 
% % Optimized DFT matrix construction
% function F = dftmtx(n)
%     F = exp(-2*pi*1i*(0:n-1)'*(0:n-1)/n) / sqrt(n);
% end
% 



%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%     3.2789
% function z = isar_hwtmapf2(xf, w, len)
%     k = length(w);
%     half_k = floor(k / 2);
%     z = zeros(len, len);
% 
%     % ADMM参数设置
%     max_iter = 150;
%     initial_rho = 2.0;
%     lambda = 0.15;
%     mu = 1.5;
%     eps_abs = 1e-4;
%     eps_rel = 1e-4;
% 
%     % 预计算FFT矩阵
%     F = dftmtx(len)/sqrt(len);
% 
%     % 并行处理
%     parfor ii = 1:len
%         % 局部变量初始化
%         rho = initial_rho;
% 
%         % 获取当前时间窗口的信号
%         start_idx = max(1, ii - half_k);
%         end_idx = min(len, ii + half_k - 1);
%         window_length = end_idx - start_idx + 1;
%         y = xf(start_idx:end_idx);  % 获取窗口信号
%         y = reshape(y, [], 1);      % 确保y是列向量
% 
%         % 构建基矩阵
%         H = zeros(window_length, len);
% 
%         % 构建改进的谐波小波基矩阵
%         for m = 1:window_length
%             t = start_idx + m - 2;
%             if t < 0
%                 t = 0;
%             end
%             t = mod(t, len) + 1;  % 确保索引在有效范围内
% 
%             % 计算自适应权重
%             time_weight = exp(-0.5*(m - window_length/2)^2/(window_length/4)^2);
%             dist_weight = exp(-0.5*(t - ii)^2/len^2);
% 
%             % 确保权重索引在有效范围内
%             w_idx = min(m, length(w));
%             adaptive_weight = w(w_idx) * time_weight * dist_weight;
% 
%             % 应用权重到基函数
%             H(m,:) = adaptive_weight * F(t,:);
%         end
% 
%         % 预计算
%         HtH = H'*H;
%         Hty = H'*y;
%         L = chol(HtH + rho*eye(len), 'lower');
% 
%         % ADMM变量初始化
%         x = zeros(len, 1);
%         z_var = zeros(len, 1);
%         u = zeros(len, 1);
% 
%         % ADMM迭代
%         for iter = 1:max_iter
%             % 更新x
%             b = Hty + rho*(z_var - u);
%             x = L' \ (L \ b);
% 
%             % 保存上一次迭代结果
%             z_old = z_var;
% 
%             % 软阈值处理
%             z_var = sign(x + u) .* max(abs(x + u) - lambda/rho * (0.95^iter), 0);
% 
%             % 更新u
%             u = u + (x - z_var);
% 
%             % 自适应更新rho
%             r_norm = norm(x - z_var);
%             s_norm = norm(-rho*(z_var - z_old));
% 
%             if r_norm > mu*s_norm
%                 rho = rho*1.1;
%                 u = u/1.1;
%             elseif s_norm > mu*r_norm
%                 rho = rho/1.1;
%                 u = u*1.1;
%             end
% 
%             % 定期更新Cholesky分解
%             if mod(iter, 10) == 0
%                 L = chol(HtH + rho*eye(len), 'lower');
%             end
% 
%             % 收敛检查
%             eps_pri = sqrt(len)*eps_abs + eps_rel*max(norm(x), norm(z_var));
%             eps_dual = sqrt(len)*eps_abs + eps_rel*norm(rho*u);
% 
%             if r_norm < eps_pri && s_norm < eps_dual
%                 break;
%             end
%         end
% 
%         % 计算重建信号的功率谱
%         recon_signal = H * x;
%         temp_fft = fft(recon_signal, len);
%         z(:,ii) = abs(temp_fft(1:len)).^2;
%     end
% end
% 
% % 优化的DFT矩阵构建函数
% function F = dftmtx(n)
%     k = (0:n-1)';
%     j = 0:n-1;
%     F = exp(-2*pi*1i*(k*j)/n) / sqrt(n);
% end



% %%%%%%%%%%%%%%%%%%%%%%%%%% 3.3121
% function z = isar_hwtmapf2(xf, w, len)
%     k = length(w);
%     half_k = floor(k / 2);
%     z = zeros(len, len);
% 
%     % ADMM参数优化
%     max_iter = 100;
%     rho = 1.2;      % 适度调整rho
%     lambda = 0.05;  % 降低lambda以保留更多细节
% 
%     % 预计算FFT矩阵
%     F = dftmtx(len)/sqrt(len);
% 
%     % 并行处理
%     parfor ii = 1:len
%         % 获取当前时间窗口的信号
%         start_idx = max(1, ii - half_k);
%         end_idx = min(len, ii + half_k - 1);
%         window_length = end_idx - start_idx + 1;
%         y = xf(start_idx:end_idx).';
% 
%         % 构建优化的基矩阵
%         H = construct_simplified_hwt(F, w, start_idx, end_idx, len, ii);
% 
%         % 预计算和缓存不变量
%         HtH = H'*H;
%         Hty = H'*y;
%         L = chol(HtH + rho*eye(len), 'lower');
% 
%         % ADMM迭代
%         [x, ~] = solve_admm(H, y, L, rho, lambda, max_iter);
% 
%         % 信号重建和功率谱计算
%         z(:,ii) = abs(fft(H * x, len)).^2;
%     end
% end
% 
% function H = construct_simplified_hwt(F, w, start_idx, end_idx, len, current_pos)
%     window_length = end_idx - start_idx + 1;
%     H = zeros(window_length, len);
%     center_idx = (start_idx + end_idx) / 2;
% 
%     % 简化的窗口参数
%     sigma_t = window_length/3;  % 调整时间窗口宽度
% 
%     for m = 1:window_length
%         t = start_idx + m - 2;
% 
%         % 简化的时间权重
%         time_weight = exp(-0.5 * ((m - window_length/2) / sigma_t)^2);
% 
%         % 确保索引有效
%         t_idx = mod(t, len) + 1;
%         t_idx = max(1, min(t_idx, len));
% 
%         % 应用权重
%         m_idx = min(m, length(w));
%         H(m,:) = w(m_idx) * time_weight * F(t_idx,:);
%     end
% 
%     % 简化的正则化
%     row_norms = sqrt(sum(abs(H).^2, 2)) + eps;
%     H = H ./ row_norms;
% end
% 
% function [x, converged] = solve_admm(H, y, L, rho, lambda, max_iter)
%     len = size(H, 2);
%     x = zeros(len, 1);
%     z_var = zeros(len, 1);
%     u = zeros(len, 1);
% 
%     Hty = H'*y;
%     converged = false;
% 
%     for iter = 1:max_iter
%         % 更新x
%         b = Hty + rho*(z_var - u);
%         x = L' \ (L \ b);
% 
%         % 更新z
%         z_old = z_var;
%         z_var = sign(x + u) .* max(abs(x + u) - lambda/rho, 0);
% 
%         % 更新u
%         u = u + (x - z_var);
% 
%         % 收敛检查
%         if mod(iter, 5) == 0
%             if norm(x - z_var) < 1e-4
%                 converged = true;
%                 break;
%             end
%         end
%     end
% end
% 
% function F = dftmtx(n)
%     k = (0:n-1)';
%     j = 0:n-1;
%     F = exp(-2*pi*1i*(k*j)/n) / sqrt(n);
% end
% 优化的谐波小波基矩阵构建函数




% %%%%%%%%%%%%LADMM   可用版本2.8
% function z = isar_hwtmapf2(xf, w, len)
%     % isar_hwtmapf2 - Harmonic Wavelet Transform using Linearized ADMM (LADMM)
%     %
%     % Implements the Harmonic Wavelet Transform (HWT) using the Linearized
%     % Alternating Direction Method of Multipliers (LADMM) for optimization.
%     %
%     % Inputs:
%     %   xf    - Input signal (1D array)
%     %   w     - Window function (1D array)
%     %   len   - Length of the signal
%     %
%     % Outputs:
%     %   z     - Power spectrum matrix after transformation
% 
%     % Initialize parameters
%     k = length(w);
%     half_k = floor(k / 2);
%     z = zeros(len, len);
% 
%     % LADMM Parameters
%     max_iter = 100;   % Maximum number of iterations
%     rho = 1.0;        % ADMM penalty parameter
%     lambda = 0.1;     % Regularization parameter for sparsity
%     tau = 1.0;        % Step size for the x-update
% 
%     % Precompute FFT matrix for efficiency
%     F = dftmtx(len) / sqrt(len);
% 
%     % Parallel processing for each time window
%     parfor ii = 1:len
%         % Define window indices
%         start_idx = max(1, ii - half_k);
%         end_idx = min(len, ii + half_k - 1);
%         window_length = end_idx - start_idx + 1;
% 
%         % Extract current window signal and reshape
%         y = xf(start_idx:end_idx).';
% 
%         % Construct the harmonic wavelet basis matrix
%         H = construct_fast_hwt(F, w, start_idx, end_idx, len);
% 
%         % Precompute H' * H and H' * y
%         HtH = H' * H;
%         Hty = H' * y;
% 
%         % Initialize LADMM variables
%         x = zeros(len, 1);
%         z_var = zeros(len, 1);
%         u = zeros(len, 1);
% 
%         % LADMM Iterations
%         for iter = 1:max_iter
%             % Compute the gradient of f(x) = 0.5 * ||Hx - y||^2
%             grad = H' * (H * x - y);
% 
%             % x-update: Linearized gradient step
%             x = x - tau * (grad + rho * (x - z_var + u));
% 
%             % z-update: Soft thresholding for sparsity
%             z_new = sign(x + u) .* max(abs(x + u) - lambda / rho, 0);
% 
%             % u-update: Dual variable update
%             u = u + (x - z_new);
% 
%             % Update z_var for the next iteration
%             z_var = z_new;
% 
%             % Convergence check every 5 iterations
%             if mod(iter, 5) == 0 && norm(x - z_var) < 1e-4
%                 break;
%             end
%         end
% 
%         % Store the power spectrum
%         z(:, ii) = abs(x).^2;
%     end
% end
% 
% % Fast Harmonic Wavelet Basis Construction
% function H = construct_fast_hwt(F, w, start_idx, end_idx, len)
%     % construct_fast_hwt - Quickly constructs the harmonic wavelet basis matrix
%     %
%     % Inputs:
%     %   F         - Precomputed FFT matrix
%     %   w         - Window function
%     %   start_idx - Start index of the current window
%     %   end_idx   - End index of the current window
%     %   len       - Length of the signal
%     %
%     % Outputs:
%     %   H         - Harmonic wavelet basis matrix
% 
%     window_length = end_idx - start_idx + 1;
%     H = zeros(window_length, len);
% 
%     for m = 1:window_length
%         t = start_idx + m - 2;
%         t_adj = min(t + 1, len);  % Ensure the index does not exceed 'len'
%         H(m, :) = w(m) * F(t_adj, :);
%     end
% end
% 
% % Precompute DFT Matrix
% function F = dftmtx(n)
%     % dftmtx - Constructs the DFT matrix
%     %
%     % Input:
%     %   n - Size of the DFT matrix
%     %
%     % Output:
%     %   F - DFT matrix normalized by sqrt(n)
% 
%     F = zeros(n, n);
%     for k = 0:n-1
%         for j = 0:n-1
%             F(k+1, j+1) = exp(-2*pi*1i*k*j/n);
%         end
%     end
%     F = F / sqrt(n);
% end

% %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%升降采样ladmm版本2.9
% function z = isar_hwtmapf2(xf, w, len)
%     % 初始化参数
%     k = length(w);
%     half_k = floor(k / 2);
%     z = zeros(len, len);
% 
%     % 增强的LADMM参数
%     max_iter = 150;      % 增加迭代次数以提高精度
%     rho = 1.5;          % 调整惩罚参数
%     lambda = 0.08;      % 微调正则化参数
%     tau = 1.2;          % 优化步长
%     epsilon = 1e-6;     % 收敛阈值
% 
%     % 创新点1: 自适应窗口权重
%     w = adaptive_window(w, xf);
% %     % 创新点2: 引入频率过采样因子
%     oversample_factor = 2;
%     len_oversampled = len * oversample_factor;
%     F = dftmtx(len_oversampled) / sqrt(len_oversampled);
% 
%     % 创新点3: 添加噪声抑制
%     xf = denoise_signal(xf);
% 
%     % 并行处理
%     parfor ii = 1:len
%         % 获取时间窗口
%         start_idx = max(1, ii - half_k);
%         end_idx = min(len, ii + half_k - 1);
%         y = xf(start_idx:end_idx).';
% 
%         % 创新点4: 自适应基矩阵构建
%         H = construct_adaptive_hwt(F, w, start_idx, end_idx, len, len_oversampled);
% 
%         % 预计算
%         HtH = H' * H;
%         Hty = H' * y;
% 
%         % LADMM变量初始化
%         x = zeros(len_oversampled, 1);
%         z_var = zeros(len_oversampled, 1);
%         u = zeros(len_oversampled, 1);
% 
%         % 创新点5: 引入动态步长调整
%         tau_k = tau;
% 
%         % LADMM迭代
%         for iter = 1:max_iter
%             x_prev = x;
% 
%             % 梯度计算与更新
%             grad = H' * (H * x - y);
%             x = x - tau_k * (grad + rho * (x - z_var + u));
% 
%             % 创新点6: 增强的软阈值处理
%             z_new = enhanced_soft_threshold(x + u, lambda/rho);
% 
%             % 更新双重变量
%             u = u + (x - z_new);
% 
%             % 动态步长调整
%             if mod(iter, 10) == 0
%                 tau_k = update_step_size(tau_k, x, x_prev);
%             end
% 
%             % 更新z_var
%             z_var = z_new;
% 
%             % 收敛检查
%             if mod(iter, 5) == 0 && norm(x - x_prev) < epsilon
%                 break;
%             end
%         end
% 
%         % 创新点7: 后处理增强
%         %x = post_process(x);
% 
%         % 降采样回原始维度并存储结果
%         x_downsampled = downsample_result(x, oversample_factor);
%         z(:,ii) = abs(x_downsampled).^2;
%     end
% 
% end
% 
% % 自适应窗口权重计算
% function w_adapted = adaptive_window(w, xf)
%     % 基于信号能量分布调整窗口权重
%     signal_energy = abs(xf).^2;
%     energy_normalized = signal_energy / max(signal_energy);
%     w_adapted = w .* (1 + 0.2 * energy_normalized(1:length(w)));
%     w_adapted = w_adapted / max(w_adapted);
% end
% 
% % 信号降噪
% function xf_denoised = denoise_signal(xf)
%     % 小波阈值降噪
%     [c,l] = wavedec(xf, 3, 'db4');
%     thr = median(abs(c))/0.6745 * sqrt(2*log(length(xf)));
%     c_denoised = wthresh(c, 's', thr);
%     xf_denoised = waverec(c_denoised, l, 'db4');
% end
% 
% % 自适应基矩阵构建
% function H = construct_adaptive_hwt(F, w, start_idx, end_idx, len, len_oversampled)
%     window_length = end_idx - start_idx + 1;
%     H = zeros(window_length, len_oversampled);
% 
%     for m = 1:window_length
%         t = start_idx + m - 2;
%         t_adj = min(t + 1, len_oversampled);
%         % 添加频率调制因子
%         freq_mod = exp(-1i * pi * (t/len_oversampled)^2);
%         H(m,:) = w(m) * F(t_adj,:) * freq_mod;
%     end
% end
% 
% % 增强的软阈值处理
% function z = enhanced_soft_threshold(x, threshold)
%     % 非线性软阈值
%     magnitude = abs(x);
%     phase = angle(x);
%     z = sign(x) .* max(0, magnitude - threshold .* (1 - exp(-magnitude/threshold)));
%     z = z .* exp(1i * phase);
% end
% 
% % 动态步长更新
% function tau_new = update_step_size(tau, x, x_prev)
%     rel_change = norm(x - x_prev) / norm(x_prev);
%     if rel_change > 0.1
%         tau_new = 0.8 * tau;  % 减小步长
%     elseif rel_change < 0.01
%         tau_new = 1.2 * tau;  % 增加步长
%     else
%         tau_new = tau;
%     end
%     % 限制步长范围
%     tau_new = min(max(tau_new, 0.5), 2.0);
% end
% 

% % 降采样处理
% function x_down = downsample_result(x, factor)
%     x_down = x(1:factor:end);
% end
% 
% 
% %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%熵1.5844
% function z = isar_hwtmapf2(xf, w, len)
%     % 初始化参数
%     k = length(w);
%     half_k = floor(k / 2);
%     z = zeros(len, len);
% 
%     % 增强的LADMM参数
%     max_iter = 88;      % 增加迭代次数以提高精度
%     rho = 1.5;           % 调整惩罚参数
%     lambda = 0.08;       % 微调正则化参数
%     tau = 1.2;           % 优化步长
%     epsilon = 1e-6;      % 收敛阈值
% 
%     % 创新点1: 自适应窗口权重
%     w = adaptive_window(w, xf);
% 
%     % 创新点2: 添加噪声抑制
%     xf = denoise_signal(xf);
% 
%     % 基础频率矩阵
%     F = dftmtx(len) / sqrt(len);
% 
%     % 并行处理
%     for ii = 1:len
%         % 获取时间窗口
%         start_idx = max(1, ii - half_k);
%         end_idx = min(len, ii + half_k - 1);
%         y = xf(start_idx:end_idx).';
% 
%         % 创新点4: 自适应基矩阵构建
%         H = construct_adaptive_hwt(F, w, start_idx, end_idx, len);
% 
%         % 预计算
%         HtH = H' * H;
%         Hty = H' * y;
% 
%         % LADMM变量初始化
%         x = zeros(len, 1);
%         z_var = zeros(len, 1);
%         u = zeros(len, 1);
% 
%         % 创新点5: 引入动态步长调整
%         tau_k = tau;
% 
%         % LADMM迭代
%         for iter = 1:max_iter
%             x_prev = x;
% 
%             % 梯度计算与更新
%             grad = H' * (H * x - y);
%             x = x - tau_k * (grad + rho * (x - z_var + u));
% 
%             % 创新点6: 增强的软阈值处理
%             z_new = enhanced_soft_threshold(x + u, lambda/rho);
% 
%             % 更新双重变量
%             u = u + (x - z_new);
% 
%             % 动态步长调整
%             if mod(iter, 10) == 0
%                 tau_k = update_step_size(tau_k, x, x_prev);
%             end
% 
%             % 更新z_var
%             z_var = z_new;
% 
%             % 收敛检查
%             if mod(iter, 5) == 0 && norm(x - x_prev) < epsilon
%                 break;
%             end
%         end
% 
%         % 存储结果
%         z(:,ii) = abs(x).^2;
%     end
% 
% end
% 
% % 自适应窗口权重计算
% function w_adapted = adaptive_window(w, xf)
%     % 基于信号能量分布调整窗口权重
%     signal_energy = abs(xf).^2;
%     energy_normalized = signal_energy / max(signal_energy);
%     w_adapted = w .* (1 + 0.2 * energy_normalized(1:length(w)));
%     w_adapted = w_adapted / max(w_adapted);
% end
% 
% % 信号降噪
% function xf_denoised = denoise_signal(xf)
%     % 小波阈值降噪
%     [c,l] = wavedec(xf, 3, 'db4');
%     thr = median(abs(c))/0.6745 * sqrt(2*log(length(xf)));
%     c_denoised = wthresh(c, 's', thr);
%     xf_denoised = waverec(c_denoised, l, 'db4');
% end
% 
% % 自适应基矩阵构建
% function H = construct_adaptive_hwt(F, w, start_idx, end_idx, len)
%     window_length = end_idx - start_idx + 1;
%     H = zeros(window_length, len);
% 
%     for m = 1:window_length
%         t = start_idx + m - 2;
%         t_adj = min(max(t + 1, 1), len);  % 确保索引在有效范围内
%         % 添加频率调制因子
%         freq_mod = exp(-1i * pi * (t_adj/len)^2);
%         H(m,:) = w(m) * F(t_adj,:) * freq_mod;
%     end
% end
% 
% % 增强的软阈值处理
% function z = enhanced_soft_threshold(x, threshold)
%     % 非线性软阈值
%     magnitude = abs(x);
%     phase = angle(x);
%     z = sign(x) .* max(0, magnitude - threshold .* (1 - exp(-magnitude/threshold)));
%     z = z .* exp(1i * phase);
% end
% 
% % 动态步长更新
% function tau_new = update_step_size(tau, x, x_prev)
%     rel_change = norm(x - x_prev) / (norm(x_prev) + eps);
%     if rel_change > 0.1
%         tau_new = 0.8 * tau;  % 减小步长
%     elseif rel_change < 0.01
%         tau_new = 1.2 * tau;  % 增加步长
%     else
%         tau_new = tau;
%     end
%     % 限制步长范围
%     tau_new = min(max(tau_new, 0.5), 2.0);
% end






%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%92ci 


function z = isar_hwtmapf2(xf, w, len)
    % 初始化参数
    k = length(w);
    half_k = floor(k / 2);
    z = zeros(len, len);

    % 增强的LADMM参数
    max_iter =6;      % 增加迭代次数以提高精度  92  62 93    901数据 44  yak 44  2.4  9011 12  yakkk 5 24
    rho = 1.5;           % 调整惩罚参数
    lambda = 0.08;       % 微调正则化参数
    tau = 1.2;           % 优化步长
    epsilon = 1e-6;      % 收敛阈值

    % 创新点1: 自适应窗口权重
    w = adaptive_window(w, xf);

    %  添加噪声抑制
    xf = denoise_signal(xf);

    % 基础频率矩阵
    F = dftmtx(len) / sqrt(len);

    % 预计算频率调制因子
    freq_mod_array = exp(-1i * pi * ((0:len-1)' / len).^2);

    % 并行处理
    for ii = 1:len
        % 获取时间窗口
        start_idx = max(1, ii - half_k);
        end_idx = min(len, ii + half_k - 1);
        y = xf(start_idx:end_idx).';
        window_length = end_idx - start_idx + 1;

        % 创新点4: 自适应基矩阵构建
        H = construct_adaptive_hwt(F, w, start_idx, end_idx, len, freq_mod_array, window_length);

        % 预计算 H' * H
        HtH = H' * H;

        % 预计算 H' * y
        Hty = H' * y;

        % LADMM变量初始化
        x = zeros(len, 1);
        z_var = zeros(len, 1);
        u = zeros(len, 1);

        % 创新点5: 引入动态步长调整
        tau_k = tau;

        % LADMM迭代
        for iter = 1:max_iter
            x_prev = x;

            % 梯度计算与更新
            grad = H' * (H * x - y);  % Efficient calculation
            x = x - tau_k * (grad + rho * (x - z_var + u));


            % 创新点6: 增强的软阈值处理
            z_new = enhanced_soft_threshold(x + u, lambda / rho);

            % 更新双重变量
            u = u + (x - z_new);

            % 动态步长调整
            if mod(iter, 10) == 0
                tau_k = update_step_size(tau_k, x, x_prev);
            end

            % 更新z_var
            z_var = z_new;

            % 收敛检查
            if mod(iter, 5) == 0 && norm(x - x_prev) < epsilon
                break;
            end
        end

        % 存储结果
        z(:,ii) = abs(x).^2;
    end

end

% 自适应窗口权重计算
function w_adapted = adaptive_window(w, xf)
    % 基于信号能量分布调整窗口权重
    signal_energy = abs(xf).^2;
    energy_normalized = signal_energy / max(signal_energy);
    w_adapted = w .* (1 + 0.2 * energy_normalized(1:length(w)));
    w_adapted = w_adapted / max(w_adapted);
end

% 信号降噪
function xf_denoised = denoise_signal(xf)
    % 小波阈值降噪
    [c, l] = wavedec(xf, 3, 'db4');
    thr = median(abs(c)) / 0.6745 * sqrt(2 * log(length(xf)));
    c_denoised = wthresh(c, 's', thr);
    xf_denoised = waverec(c_denoised, l, 'db4');
end

% 自适应基矩阵构建
function H = construct_adaptive_hwt(F, w, start_idx, end_idx, len, freq_mod_array, window_length)
    % 预分配矩阵空间
    H = zeros(window_length, len);

    % 利用向量化加快频率调制因子应用
    indices = (start_idx:end_idx) - 1;
    freq_mod = freq_mod_array(indices + 1);  % +1 for MATLAB 1-based indexing
    H = (w(1:window_length).' .* freq_mod) .* F(indices + 1, :);
end

% 增强的软阈值处理
function z = enhanced_soft_threshold(x, threshold)
    % 非线性软阈值
    magnitude = abs(x);
    phase = angle(x);
    shrink = threshold * (1 - exp(-magnitude / threshold));
    z = sign(x) .* max(0, magnitude - shrink);
    z = z .* exp(1i * phase);
end

% 动态步长更新
function tau_new = update_step_size(tau, x, x_prev)
    rel_change = norm(x - x_prev) / (norm(x_prev) + eps);
    if rel_change > 0.1
        tau_new = 0.8 * tau;  % 减小步长
    elseif rel_change < 0.01
        tau_new = 1.2 * tau;  % 增加步长
    else
        tau_new = tau;
    end
    % 限制步长范围
    tau_new = min(max(tau_new, 0.5), 2.0);
end
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%if xieboxiaobo
% function z = isar_hwtmapf2(xf, w, len, waveletType)
% % isar_hwtmapf2 - Enhanced Harmonic Wavelet Time-Frequency Mapping Function
% %
% % This function performs time-frequency mapping using an enhanced Linearized
% % Alternating Direction Method of Multipliers (LADMM) approach with adaptive
% % windowing and denoising techniques.
% %
% % Parameters:
% %   xf         - Input signal in the time domain (complex or real)
% %   w          - Window function
% %   len        - Length of the signal
% %   waveletType - Type of wavelet to use ('dft' or 'morlet')
% %
% % Returns:
% %   z          - Time-Frequency energy representation
% 
%     % 设置默认波形
%     if nargin < 4
%         waveletType = 'dft';
%     end
% 
%     % 初始化参数
%     k = length(w);
%     half_k = floor(k / 2);
%     z = zeros(len, len);
% 
%     % 增强的LADMM参数
%     max_iter = 39;      % 增加迭代次数以提高精度
%     rho = 1.5;          % 调整惩罚参数
%     lambda = 0.08;      % 微调正则化参数
%     tau = 1.2;          % 优化步长
%     epsilon = 1e-6;     % 收敛阈值
% 
%     % 创新点1: 自适应窗口权重
%     w = adaptive_window(w, xf);
% 
%     % 添加噪声抑制
%     xf = denoise_signal(xf);
% 
%     % 基础频率矩阵或Morlet小波基
%     if strcmpi(waveletType, 'morlet')
%         % 使用Morlet小波
%         [cwtCoeffs, frequencies] = cwt(real(xf), 'morl', 'FrequencyLimits', [0, len/2]);
%         F = abs(cwtCoeffs).^2;
%     else
%         % 使用DFT矩阵
%         F = dftmtx(len) / sqrt(len);
%     end
% 
%     % 基础频率矩阵
%     F = dftmtx(len) / sqrt(len);
% 
%     % 预计算频率调制因子
%     freq_mod_array = exp(-1i * pi * ((0:len-1)' / len).^2);
% 
%     % 并行处理
%     parfor ii = 1:len
%         % 获取时间窗口
%         start_idx = max(1, ii - half_k);
%         end_idx = min(len, ii + half_k - 1);
%         y = xf(start_idx:end_idx).';
%         window_length = end_idx - start_idx + 1;
% 
%         % 创新点4: 自适应基矩阵构建
%         H = construct_adaptive_hwt(F, w, start_idx, end_idx, len, freq_mod_array, window_length, waveletType);
% 
%         % 预计算 H' * H
%         HtH = H' * H;
% 
%         % 预计算 H' * y
%         Hty = H' * y;
% 
%         % LADMM变量初始化
%         x_val = zeros(len, 1);
%         z_var = zeros(len, 1);
%         u = zeros(len, 1);
% 
%         % 创新点5: 引入动态步长调整
%         tau_k = tau;
% 
%         % LADMM迭代
%         for iter = 1:max_iter
%             x_prev = x_val;
% 
%             % 梯度计算与更新
%             grad = Hty + HtH * x_val;  % Efficient calculation
%             x_val = x_val - tau_k * (grad + rho * (x_val - z_var + u));
% 
%             % 创新点6: 增强的软阈值处理
%             z_new = enhanced_soft_threshold(x_val + u, lambda / rho);
% 
%             % 更新双重变量
%             u = u + (x_val - z_new);
% 
%             % 动态步长调整
%             if mod(iter, 10) == 0
%                 tau_k = update_step_size(tau_k, x_val, x_prev);
%             end
% 
%             % 更新z_var
%             z_var = z_new;
% 
%             % 收敛检查
%             if mod(iter, 5) == 0 && norm(x_val - x_prev) < epsilon
%                 break;
%             end
%         end
% 
%         % 存储结果
%         z(:,ii) = abs(x_val).^2;
%     end
% 
%     % 如果使用Morlet小波，累加能量
%     if strcmpi(waveletType, 'morlet')
%         z = sum(z, 1);
%         z = repmat(z, len, 1);  % 构建矩阵
%     end
% end
% 
% % 自适应窗口权重计算
% function w_adapted = adaptive_window(w, xf)
%     % 基于信号能量分布调整窗口权重
%     signal_energy = abs(xf).^2;
%     energy_normalized = signal_energy / max(signal_energy);
%     w_adapted = w .* (1 + 0.2 * energy_normalized(1:length(w)));
%     w_adapted = w_adapted / max(w_adapted);
% end
% 
% % 信号降噪
% function xf_denoised = denoise_signal(xf)
%     % 小波阈值降噪
%     [c, l] = wavedec(xf, 3, 'db4');
%     thr = median(abs(c)) / 0.6745 * sqrt(2 * log(length(xf)));
%     c_denoised = wthresh(c, 's', thr);
%     xf_denoised = waverec(c_denoised, l, 'db4');
% end
% 
% % 自适应基矩阵构建
% function H = construct_adaptive_hwt(F, w, start_idx, end_idx, len, freq_mod_array, window_length, waveletType)
%     % construct_adaptive_hwt - Constructs the adaptive transformation matrix
%     %
%     % Parameters:
%     %   F              - DFT matrix
%     %   w              - Window function
%     %   start_idx      - Start index of the time window
%     %   end_idx        - End index of the time window
%     %   len            - Length of the signal
%     %   freq_mod_array - Frequency modulation array
%     %   window_length  - Length of the current window
%     %   waveletType    - Type of wavelet to use ('dft' or 'morlet')
%     %
%     % Returns:
%     %   H - Adaptive transformation matrix
% 
%     % 预分配矩阵空间
%     H = zeros(window_length, len);
% 
%     if strcmpi(waveletType, 'morlet')
%         % 使用Morlet小波基
%         for m = 1:window_length
%             % 生成Morlet小波在当前时间点
%             t = (m - half(window_length)) / window_length;
%             sigma = 0.5;
%             morlet = exp(-t^2 / (2*sigma^2)) .* exp(1i*2*pi*0.25*t);
%             H(m, :) = morlet .* F(m, :) .* freq_mod_array(m + start_idx - 1);
%         end
%     else
%         % 使用DFT基
%         indices = (start_idx:end_idx) - 1;
%         freq_mod = freq_mod_array(indices + 1);  % Adjusting indices for MATLAB's 1-based indexing
%         for m = 1:window_length
%             H(m, :) = F(m, :) .* freq_mod(m) .* w(m);
%         end
%     end
% end
% 
% % 增强的软阈值处理
% function z = enhanced_soft_threshold(x, threshold)
%     % enhanced_soft_threshold - Applies enhanced soft thresholding to promote sparsity
%     %
%     % Parameters:
%     %   x         - Input signal
%     %   threshold - Threshold value
%     %
%     % Returns:
%     %   z         - Thresholded signal
% 
%     magnitude = abs(x);
%     phase = angle(x);
%     shrink = threshold * (1 - exp(-magnitude / threshold));
%     z = sign(x) .* max(0, magnitude - shrink);
%     z = z .* exp(1i * phase);
% end
% 
% % 动态步长更新
% function tau_new = update_step_size(tau, x, x_prev)
%     % update_step_size - Dynamically updates the step size for LADMM
%     %
%     % Parameters:
%     %   tau     - Current step size
%     %   x       - Current estimate
%     %   x_prev  - Previous estimate
%     %
%     % Returns:
%     %   tau_new - Updated step size
% 
%     rel_change = norm(x - x_prev) / (norm(x_prev) + eps);
%     if rel_change > 0.1
%         tau_new = 0.8 * tau;  % 减小步长
%     elseif rel_change < 0.01
%         tau_new = 1.2 * tau;  % 增加步长
%     else
%         tau_new = tau;
%     end
%     % 限制步长范围
%     tau_new = min(max(tau_new, 0.5), 2.0);
% end

