function rngpro = RangeAlignment(X)

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%
% range alignment using time-delay estimation method
% via cross-correlation with a reference range profile
%
% Input: 
%     Complex data: X
%     
% Output:
%     Range profile: rngpro
%
% Ref: Haywood, B., Evens, R.,"Motion compensation for ISAR imaging," 
%      Proc. of ASSPA, Adelaide, Australia, pp. 113-117, 1989.
%
% By V.C. Chen
%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

j= sqrt(-1);
[m,n] = size(X);
rngpro = fft(X,[],2);            % range profiles
frngpro = ifft(abs(rngpro),[],2); % magnitude in freq. domain
cfrngpro = conj(frngpro(m/2,:)); % conjugate of the reference range profile

figure
[np,nr] = size(frngpro);
 for l=1:5:np
   temp =0.3*abs(frngpro(l,:));
   plot(temp+l,'-b','LineWidth',2)
   axis([1 nr 1 np]);
   hold on
 end
grid minor 
title('Frequency-Range profiles before alignment')
xlabel('Range cells')
ylabel('Frequency')
drawnow

figure
plot(abs(frngpro(m/2,:)),'-b','LineWidth',2);
axis([1 128 0 150])
title('Conjugate of the Reference Range Profile in Freq.');
xlabel('Range cells')
ylabel('Magnitude')
grid minor
drawnow

% cross-correlation in freq. domain
for k = 1:m
  xcorr_rngpro(k,:) = ifft(frngpro(k,:).*cfrngpro);
end

figure
[np,nr] = size(xcorr_rngpro);
 for l=1:5:np
   temp =0.1*abs(xcorr_rngpro(l,:));
   plot(temp+l,'-b','LineWidth',2)
   axis([1 nr 1 np]);
   hold on
 end
grid minor 
title('Cross-correlation function of range cells');
xlabel('Range cells')
ylabel('Pulses')
drawnow

% find maximum delay
[lagmax,nlag] = max(xcorr_rngpro,[],2);
figure
subplot(2,1,1)
plot(abs(lagmax),'-b','LineWidth',2)
xlabel('Pulses')
ylabel('Magnitude')
title('Distribution of maximum range drift')
axis tight
grid minor
drawnow
subplot(2,1,2)
plot(nlag,'-b','LineWidth',2)
xlabel('Pulses')
ylabel('Number of range cell drift')
title('Range drift of maxima')
axis([1 256 0 150])
grid minor
drawnow

% range cell shift relative to reference
r_delay = rem(nlag-1+n/2,n)-n/2;          
unwrap_r_delay = (n/2)/pi * unwrap(r_delay/(n/2)*pi); % unwrap 
figure
subplot(2,1,1)
plot(r_delay,'-b','LineWidth',2)
xlabel('Pulses')
ylabel('Number of range cell shift')
title('Range cell drift relative to the reference range profile')
axis tight
grid minor
drawnow
subplot(2,1,2)
plot(unwrap_r_delay,'-b','LineWidth',2)
xlabel('Pulses')
ylabel('Number of range cell drift')
title('Unwrapped range cell drift relative to the reference range profile')
axis tight
grid minor
drawnow

% phase compensation
x = [1:m]';
p = polyfit(x,unwrap_r_delay,2); % polynomial fit
sr_delay = polyval(p,x); 
t_delay = sr_delay/n;
W = 2*pi*[0:n-1];   
f_delay = t_delay*W;
P = exp(j*(f_delay)); % phase
Y = X.*P;  % phase correction
rngpro = fft(Y,[],2);

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%5555555555%%%%