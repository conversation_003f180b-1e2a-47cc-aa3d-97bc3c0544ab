# STVMD-Based ISAR Imaging for Ship Targets

This project implements Short-Time Variational Mode Decomposition (STVMD) for Inverse Synthetic Aperture Radar (ISAR) imaging of ship targets. STVMD extends traditional Variational Mode Decomposition (VMD) by incorporating Short-Time Fourier Transform (STFT) to better handle non-stationary signals, making it particularly suitable for ISAR imaging of ships with complex motions.

## Background

### Inverse Synthetic Aperture Radar (ISAR)
ISAR is a radar technique used to generate a two-dimensional high-resolution image of a target. Unlike conventional SAR, ISAR uses the target's own motion to synthesize a large aperture. The quality of ISAR images is highly dependent on the motion of the target.

### Variational Mode Decomposition (VMD)
VMD is an adaptive, non-recursive, modal decomposition method that decomposes a signal into an ensemble of band-limited intrinsic mode functions (IMFs). VMD overcomes limitations of Empirical Mode Decomposition (EMD) such as endpoint effects and mode mixing.

### Short-Time Variational Mode Decomposition (STVMD)
STVMD extends VMD by incorporating time-frequency analysis, allowing it to better handle non-stationary signals with time-varying frequency components. STVMD comes in two variants:
1. **Non-dynamic STVMD**: Uses fixed central frequencies across time windows
2. **Dynamic STVMD**: Allows central frequencies to vary with time, making it more suitable for highly non-stationary signals

## Implementation

The implementation includes:

1. **ISAR_STVMD.m**: Main function that applies STVMD to ISAR radar data
2. **test_ISAR_STVMD.m**: Test script demonstrating how to use the STVMD-based ISAR imaging

### Mathematical Formulation

STVMD decomposes a signal into K intrinsic mode functions (IMFs) by solving the following optimization problem:

```
min_{uk, ωk} ∑_k ||∂t[(δ(t) + j/πt) * uk(t)]e^(-jωkt)||^2
s.t. ∑_k uk = f
```

Where:
- uk: The kth mode
- ωk: The center frequency of the kth mode
- f: The input signal
- δ(t): The Dirac delta function
- *: Convolution operator

The solution is found using the Alternating Direction Method of Multipliers (ADMM), which iteratively updates each mode and its central frequency.

STVMD extends this by:
1. Dividing the signal into overlapping windows
2. Applying VMD to each window
3. Allowing central frequencies to either:
   - Remain fixed across all windows (non-dynamic STVMD)
   - Vary with time (dynamic STVMD)
4. Reconstructing the full signal using overlap-add synthesis

## Usage

### Prerequisites
- MATLAB R2018a or later
- Signal Processing Toolbox

### Running the Example

1. Load or generate ISAR radar data
2. Configure STVMD parameters
3. Apply STVMD-based ISAR imaging
4. Analyze and visualize results

Example:

```matlab
% Load ISAR data
load('data_ship.mat');

% Configure STVMD parameters
params = struct();
params.K = 3;              % Number of modes
params.alpha = 2000;       % Balancing parameter
params.tau = 1e-4;         % Time-step of dual ascent
params.n_fft = 64;         % FFT size for time windows
params.window_length = 16; % Window length
params.overlap = 0.5;      % Window overlap
params.dynamic = true;     % Use dynamic STVMD

% Apply STVMD
[imf_stvmd, stvmd_isar] = ISAR_STVMD(radar_data, params);

% Display results
figure;
imagesc(20*log10(abs(stvmd_isar)/max(abs(stvmd_isar(:)))));
colormap('jet'); colorbar;
caxis([-30 0]);
```

## Performance Evaluation

The performance of STVMD-based ISAR imaging is evaluated using:

1. **Image Contrast**: Measures the sharpness and focus of the ISAR image
2. **Resolution**: Ability to distinguish between closely spaced scatterers
3. **Robustness**: Performance in the presence of noise and clutter

Compared to conventional ISAR imaging techniques, STVMD offers:
- Improved handling of non-stationary Doppler signatures
- Better preservation of time-varying frequency components
- Enhanced image contrast and resolution for targets with complex motions

## Parameter Tuning

The performance of STVMD depends on several parameters:

- **K**: Number of modes (typically 3-5 for ISAR data)
- **alpha**: Balancing parameter (controls bandwidth of extracted modes)
- **window_length**: Length of time windows (controls time-frequency resolution)
- **overlap**: Window overlap percentage (typically 50%)
- **dynamic**: Whether to use dynamic (true) or non-dynamic (false) STVMD

For ship targets with complex motions, dynamic STVMD with K=3-5 and high overlap (50-75%) often yields the best results.

## References

1. Dragomiretskiy, K., & Zosso, D. (2014). Variational mode decomposition. IEEE Transactions on Signal Processing, 62(3), 531-544.
2. Jia, H., Cao, P., & Liang, T. (2024). Short-time Variational Mode Decomposition. arXiv preprint arXiv:2501.09174.
3. Chen, V. C., & Martorella, M. (2014). Inverse synthetic aperture radar imaging: principles, algorithms, and applications. SciTech Publishing. 