C This program reads  the I and Q  data  from a ISAR record data  file  and 
C saves them to a decimal data file.  
C
	CHARACTER*20 NAME
	CHARACTER C(630),B(2)
	EQUIVALENCE (II,B(1))
C        
	WRITE(*,*)'This program reads the I and Q data form a ISAR', 
     &  ' record data file and saves themto a decimal data file.', 
	WRITE(*,*)'*************************************************'
	WRITE(*,*)'Now, please, input the file name to be read.'
	READ(*,998) NAME
	WRITE(*,*)'*************************************************'
998     FORMAT(A20)
	OPEN(1,FILE=NAME, STATUS='OLD',ACCESS='DIRECT',
     &  RECL=630,FORM='UNFORMATTED')
	WRITE(*,*)'Please, input the file name to put I and Q data.'
	READ(*,998) NAME
	WRITE(*,*)'*************************************************'
	OPEN(3,FILE=NAME, STATUS='NEW')
	WRITE(*,*)'How many records do you want to read ?'
	READ(*,*) N
	WRITE(*,*)'*************************************************'
C        
	WRITE(*,990) N
990     FORMAT(2X,'Total number of records to be read is ',I5,'.')
	WRITE(*,*)'   '
	DO 10 K=1,N
	WRITE(*,992) K
992     FORMAT(1H+,' Now the ',I5,'th record is being read.')
	READ(1) C
	DO 20 I=1,306
	FI=ICHAR(C(2*I-1))-133.
	FQ=ICHAR(C(2*I))-133.2
	WRITE(3,*) K,I,FI,FQ
20      CONTINUE
10      CONTINUE
	STOP
	END
