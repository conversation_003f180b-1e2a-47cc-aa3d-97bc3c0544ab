%-------------------------------------------------------------------------%
%--------        增强型ICVMD ISAR成像算法                            -------%
%--------        专为三维转动目标设计                                -------%
%-------------------------------------------------------------------------%

function [ISAR_image_enhanced, s_compensated_r_tm] = Enhanced_ICVMD_ISAR(s_r_tm2)
% Enhanced_ICVMD_ISAR - 增强型ICVMD ISAR成像算法
%
% 该算法专为三维转动目标设计，通过多尺度自适应模态分解、联合时频分析、
% 自适应相位误差估计和迭代相位补偿等技术，有效解决ISAR成像中的散焦问题。
%
% 输入:
%   s_r_tm2 - 距离压缩后的回波数据 (距离单元 x 方位单元)
%
% 输出:
%   ISAR_image_enhanced - 增强型ICVMD处理后的ISAR图像
%   s_compensated_r_tm - 相位补偿后的信号

% 获取数据尺寸
[N_r, N_tm] = size(s_r_tm2);

% --- 算法参数设置 ---
% 基本参数
alpha_base = 2000;    % 基础平衡参数
tau_base = 0.5;       % 基础拉格朗日乘子更新步长
tol_cvmd = 1e-7;      % 收敛容限
max_iter_cvmd = 300;  % 最大迭代次数
max_iter_global = 3;  % 全局迭代次数

% 多尺度参数
K_min = 3;            % 最小模态数
K_max = 7;            % 最大模态数
window_sizes = [16, 32, 64]; % 多尺度窗口大小

% 初始化存储补偿后的信号
s_compensated_r_tm = zeros(N_r, N_tm, 'like', 1j);
s_temp = s_r_tm2;     % 临时存储用于迭代处理的信号

% 创建慢时间轴（归一化）
tm_norm = (0:N_tm-1)/N_tm;
tm_indices = 0:N_tm-1;

% 频率轴（归一化频率，范围 [0, 1)）
omega_axis_norm = (0:N_tm-1) / N_tm;

% --- 初始相位补偿 ---
% 创建距离单元索引向量
r_indices = (1:N_r)';
ones_r = ones(N_r, 1);
ones_tm = ones(1, N_tm);

% 应用初始相位补偿（包含线性、二次和三次项）
linear_term = 200;    % 线性多普勒项 (Hz)
quadratic_term = 40;  % 二次多普勒项 (Hz/s)
cubic_term = 400;     % 三次多普勒项 (Hz/s^2)

% 计算完整的相位补偿项
s_phase_comp = exp(1j * ones_r * (2*pi*(linear_term*tm_norm + (1/2)*quadratic_term*tm_norm.^2 + (1/6)*cubic_term*tm_norm.^3)));
s_temp = s_temp .* s_phase_comp;

fprintf('开始增强型ICVMD处理 (共 %d 个距离单元)...\n', N_r);

% --- 全局迭代优化 ---
for global_iter = 1:max_iter_global
    fprintf('全局迭代: %d / %d\n', global_iter, max_iter_global);

    % 计算当前图像熵作为基准
    temp_image = fftshift(fft(s_temp, [], 2), 2);
    base_entropy = calculate_image_entropy(abs(temp_image));
    fprintf('当前图像熵: %.4f\n', base_entropy);

    % --- 对每个距离单元进行处理 ---
    for r_idx = 1:N_r
        if mod(r_idx, round(N_r/10)) == 0
            fprintf('处理距离单元: %d / %d\n', r_idx, N_r);
        end

        % 获取当前距离单元的信号
        signal_r_tm = s_temp(r_idx, :);

        % 跳过能量过低的距离单元
        if sum(abs(signal_r_tm).^2) < 1e-10
            s_compensated_r_tm(r_idx, :) = signal_r_tm;
            continue;
        end

        % --- 自适应确定模态数量 ---
        % 计算信号的频谱
        S_r_omega = fft(signal_r_tm);
        abs_spectrum = abs(S_r_omega);

        % 平滑频谱
        window_size = 5;
        smoothed_spectrum = conv(abs_spectrum, ones(1, window_size)/window_size, 'same');

        % 寻找频谱峰值
        [~, peak_indices] = findpeaks(smoothed_spectrum, 'MinPeakHeight', 0.1*max(smoothed_spectrum));

        % 根据峰值数量自适应确定模态数K
        K = min(max(length(peak_indices), K_min), K_max);

        % 自适应调整alpha参数（根据信号能量）
        signal_energy = sum(abs(signal_r_tm).^2);
        alpha_cvmd = alpha_base * (1 + 0.5*exp(-signal_energy/mean(abs(signal_r_tm).^2)));

        % 自适应调整tau参数
        tau_cvmd = tau_base * (1 - 0.3*exp(-signal_energy/mean(abs(signal_r_tm).^2)));

        % --- 多尺度联合时频分析 ---
        % 初始化多尺度分解结果
        u_k_tm_multiscale = cell(length(window_sizes), 1);
        omega_k_multiscale = cell(length(window_sizes), 1);

        for scale_idx = 1:length(window_sizes)
            win_size = window_sizes(scale_idx);

            % 短时傅里叶变换预处理
            stft_win = hamming(win_size)';
            num_segments = floor(N_tm / (win_size/2)) - 1;
            stft_result = zeros(win_size, num_segments);

            for seg_idx = 1:num_segments
                start_idx = (seg_idx-1)*(win_size/2) + 1;
                end_idx = start_idx + win_size - 1;
                if end_idx > N_tm
                    break;
                end
                segment = signal_r_tm(start_idx:end_idx) .* stft_win;
                stft_result(:, seg_idx) = fft(segment);
            end

            % 基于STFT结果初始化中心频率
            stft_energy = mean(abs(stft_result).^2, 2);
            [~, stft_peaks] = findpeaks(stft_energy, 'SortStr', 'descend', 'NPeaks', K);

            if length(stft_peaks) < K
                stft_peaks = [stft_peaks; randi([1, win_size], K-length(stft_peaks), 1)];
            end

            % 初始化中心频率
            omega_k_current_iter = zeros(K, 1);
            for k_idx = 1:K
                omega_k_current_iter(k_idx) = (stft_peaks(k_idx) - 1) / win_size;
                if omega_k_current_iter(k_idx) > 0.5
                    omega_k_current_iter(k_idx) = omega_k_current_iter(k_idx) - 1;
                end
            end

            % 初始化频域模式和拉格朗日乘子
            U_k_omega_current_iter = zeros(K, N_tm, 'like', 1j);
            lambda_omega_current_iter = zeros(1, N_tm, 'like', 1j);

            % --- ICVMD 迭代 ---
            for iter = 1:max_iter_cvmd
                % 保存上一次迭代的结果
                U_k_omega_prev_iter = U_k_omega_current_iter;
                omega_k_prev_iter = omega_k_current_iter;
                lambda_omega_prev_iter = lambda_omega_current_iter;

                % 1. 更新模式 U_k
                temp_U_k_next_iter = zeros(K, N_tm, 'like', 1j);
                for k_update_idx = 1:K
                    % 计算除当前模式外的所有模式之和
                    logical_idx_sum = true(1, K);
                    logical_idx_sum(k_update_idx) = false;

                    if K > 1
                        sum_U_j_neq_k_val = sum(U_k_omega_prev_iter(logical_idx_sum, :), 1);
                    else
                        sum_U_j_neq_k_val = zeros(1, N_tm, 'like', 1j);
                    end

                    % 更新公式
                    numerator_U = S_r_omega - sum_U_j_neq_k_val + lambda_omega_prev_iter/2;
                    denominator_U = 1 + 2 * alpha_cvmd * (omega_axis_norm - omega_k_prev_iter(k_update_idx)).^2;

                    % 防止除以零或极小值
                    denominator_U(abs(denominator_U) < 1e-10) = 1e-10;

                    temp_U_k_next_iter(k_update_idx, :) = numerator_U ./ denominator_U;
                end
                U_k_omega_current_iter = temp_U_k_next_iter;

                % 2. 更新中心频率 omega_k
                temp_omega_k_next_iter = zeros(K, 1);
                for k_update_idx = 1:K
                    abs_U_k_sq = abs(U_k_omega_current_iter(k_update_idx, :)).^2;
                    integrand_num_omega = omega_axis_norm .* abs_U_k_sq;

                    sum_abs_U_k_sq = sum(abs_U_k_sq);
                    if sum_abs_U_k_sq < 1e-12
                        temp_omega_k_next_iter(k_update_idx) = omega_k_prev_iter(k_update_idx);
                    else
                        % 确保频率在合理范围内
                        new_omega = sum(integrand_num_omega) / sum_abs_U_k_sq;
                        if isnan(new_omega) || isinf(new_omega)
                            temp_omega_k_next_iter(k_update_idx) = omega_k_prev_iter(k_update_idx);
                        else
                            % 限制在[-0.5, 0.5]范围内
                            if new_omega > 0.5
                                new_omega = 0.5;
                            elseif new_omega < -0.5
                                new_omega = -0.5;
                            end
                            temp_omega_k_next_iter(k_update_idx) = new_omega;
                        end
                    end
                end
                omega_k_current_iter = temp_omega_k_next_iter;

                % 3. 更新拉格朗日乘子 lambda
                sum_U_k_all_current_iter = sum(U_k_omega_current_iter, 1);
                lambda_omega_current_iter = lambda_omega_prev_iter + tau_cvmd * (S_r_omega - sum_U_k_all_current_iter);

                % 4. 检查收敛性
                diff_U_sq_sum = 0;
                norm_U_prev_sq_sum = 0;
                for k_conv_idx = 1:K
                    diff_U_sq_sum = diff_U_sq_sum + sum(abs(U_k_omega_current_iter(k_conv_idx,:) - U_k_omega_prev_iter(k_conv_idx,:)).^2);
                    norm_U_prev_sq_sum = norm_U_prev_sq_sum + sum(abs(U_k_omega_prev_iter(k_conv_idx,:)).^2);
                end

                if norm_U_prev_sq_sum < 1e-12
                    if diff_U_sq_sum < tol_cvmd
                        break;
                    end
                elseif (diff_U_sq_sum / norm_U_prev_sq_sum) < tol_cvmd && iter > 1
                    break;
                end

                % 检查是否有NaN或Inf值出现
                if any(isnan(U_k_omega_current_iter(:))) || any(isinf(U_k_omega_current_iter(:)))
                    fprintf('警告: 迭代 %d 中出现NaN或Inf值，使用上一次迭代结果\n', iter);
                    U_k_omega_current_iter = U_k_omega_prev_iter;
                    omega_k_current_iter = omega_k_prev_iter;
                    break;
                end
            end

            % --- 将频域模式转换回时域 ---
            u_k_tm_decomposed = zeros(K, N_tm, 'like', 1j);
            for k_idx_ifft = 1:K
                u_k_tm_decomposed(k_idx_ifft, :) = ifft(U_k_omega_current_iter(k_idx_ifft, :));
            end

            % 存储当前尺度的分解结果
            u_k_tm_multiscale{scale_idx} = u_k_tm_decomposed;
            omega_k_multiscale{scale_idx} = omega_k_current_iter;
        end

        % --- 多尺度融合相位误差估计 ---
        % 初始化融合权重和相位误差
        scale_weights = zeros(length(window_sizes), 1);
        phase_errors_multiscale = zeros(length(window_sizes), N_tm);

        % 计算每个尺度的权重和相位误差
        for scale_idx = 1:length(window_sizes)
            u_k_tm_decomposed = u_k_tm_multiscale{scale_idx};
            omega_k_current_iter = omega_k_multiscale{scale_idx};

            % 计算每个模式的能量和相干性
            K_current = size(u_k_tm_decomposed, 1);
            mode_energies = sum(abs(u_k_tm_decomposed).^2, 2);
            mode_coherence = zeros(K_current, 1);

            for k_idx = 1:K_current
                % 计算相干性指标（基于相位变化的平滑度）
                phase_seq = unwrap(angle(u_k_tm_decomposed(k_idx, :)));
                phase_diff = diff(phase_seq);

                % 检查相位差分是否包含NaN或Inf
                if any(isnan(phase_diff)) || any(isinf(phase_diff))
                    mode_coherence(k_idx) = 0;
                else
                    std_phase = std(phase_diff);
                    if std_phase < eps
                        mode_coherence(k_idx) = 1e6; % 设置一个较大但有限的值
                    else
                        mode_coherence(k_idx) = 1 / (std_phase + eps);
                    end
                end
            end

            % 结合能量和相干性选择最佳模式
            combined_score = mode_energies .* mode_coherence;

            % 检查是否有有效的分数
            if all(isnan(combined_score)) || all(combined_score == 0)
                % 如果所有分数无效，仅基于能量选择
                [best_score, best_mode_idx] = max(mode_energies);
            else
                % 正常情况下基于组合分数选择
                [best_score, best_mode_idx] = max(combined_score);
            end

            % 计算当前尺度的权重
            scale_weights(scale_idx) = best_score;

            % 获取主导模式的中心频率
            f_center_dominant_norm = omega_k_current_iter(best_mode_idx);

            % 计算相位误差
            u_dominant_tm = u_k_tm_decomposed(best_mode_idx, :);
            phase_error = angle(u_dominant_tm) - 2 * pi * f_center_dominant_norm .* tm_indices;

            % 解缠绕
            phase_error = unwrap(phase_error);

            % 使用多项式拟合平滑相位误差
            poly_order = min(6, floor(N_tm/10));
            p = polyfit(tm_indices, phase_error, poly_order);
            phase_error_smooth = polyval(p, tm_indices);

            % 移除均值
            phase_error_smooth = phase_error_smooth - mean(phase_error_smooth);

            % 存储当前尺度的相位误差
            phase_errors_multiscale(scale_idx, :) = phase_error_smooth;
        end

        % 归一化权重
        sum_weights = sum(scale_weights);
        if sum_weights > 0
            scale_weights = scale_weights / sum_weights;
        else
            % 如果所有权重都为零，使用均匀权重
            scale_weights = ones(length(window_sizes), 1) / length(window_sizes);
        end

        % 加权融合多尺度相位误差
        fused_phase_error = zeros(1, N_tm);
        for scale_idx = 1:length(window_sizes)
            % 检查相位误差是否包含NaN或Inf
            current_phase_error = phase_errors_multiscale(scale_idx, :);
            if any(isnan(current_phase_error)) || any(isinf(current_phase_error))
                fprintf('警告: 距离单元 %d 的尺度 %d 包含无效相位误差，跳过\n', r_idx, scale_idx);
                continue;
            end
            fused_phase_error = fused_phase_error + scale_weights(scale_idx) * current_phase_error;
        end

        % 检查融合后的相位误差是否有效
        if any(isnan(fused_phase_error)) || any(isinf(fused_phase_error))
            fprintf('警告: 距离单元 %d 的融合相位误差无效，使用零相位\n', r_idx);
            fused_phase_error = zeros(1, N_tm);
        end

        % --- 相位补偿 ---
        s_compensated_r_tm(r_idx, :) = signal_r_tm .* exp(-1j * fused_phase_error);
    end

    % 更新临时信号用于下一次迭代
    s_temp = s_compensated_r_tm;

    % 计算当前迭代后的图像熵
    temp_image = fftshift(fft(s_temp, [], 2), 2);
    current_entropy = calculate_image_entropy(abs(temp_image));

    % 检查是否收敛（熵减少不明显则停止）
    entropy_improvement = base_entropy - current_entropy;
    fprintf('迭代 %d 熵改进: %.4f\n', global_iter, entropy_improvement);

    if entropy_improvement < 0.01 && global_iter > 1
        fprintf('熵改进不明显，提前停止迭代\n');
        break;
    end
end

fprintf('增强型ICVMD处理完成。\n');

% --- 方位向FFT成像 ---
ISAR_image_enhanced = fftshift(fft(s_compensated_r_tm, [], 2), 2);

% --- 显示增强型ICVMD处理后的ISAR图像 ---
figure('name', '改进的ICVMD ISAR 成像结果 (dB)');
max_val = max(abs(ISAR_image_enhanced(:)));
if max_val == 0, max_val = 1; end
ISAR_image_enhanced_db = 20*log10(abs(ISAR_image_enhanced) / max_val);

imagesc(ISAR_image_enhanced_db);
xlabel('方位单元 (Azimuth Cell / Doppler)');
ylabel('距离单元 (Range Cell)');
title('改进的ICVMD ISAR 成像结果 (dB)');
colormap('jet');
axis xy;
colorbar;
caxis_min_db = -40;
caxis_max_db = 0;
caxis([caxis_min_db, caxis_max_db]);

fprintf('成像完成。\n');
end

% --- 辅助函数：计算图像熵 ---
function entropy = calculate_image_entropy(image_data)
% 检查输入数据是否有效
if any(isnan(image_data(:))) || any(isinf(image_data(:))) || all(image_data(:) == 0)
    entropy = Inf;
    return;
end

% 归一化图像数据
sum_data = sum(image_data(:));
if sum_data <= 0
    entropy = Inf;
    return;
end

normalized_data = image_data / sum_data;

% 计算熵
valid_data = normalized_data(normalized_data > 0);
if isempty(valid_data)
    entropy = Inf;
    return;
end

entropy = -sum(valid_data .* log2(valid_data));

% 检查熵是否有效
if isnan(entropy) || isinf(entropy)
    entropy = Inf;
end
end
