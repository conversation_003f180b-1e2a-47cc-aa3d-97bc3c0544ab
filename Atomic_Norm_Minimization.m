function [A_optimal, a_optimal, convergence_info] = Atomic_Norm_Minimization(signal, params)
% Atomic_Norm_Minimization - 原子范数最小化求解器
%
% 核心思想：
% 将每个能量岛视为多阶chirp原子，记信号为：
% s(t) = Σ A_k * exp{Σ (a_{k,p}/p!) * t^p} + w(t)
%
% 构造带"多阶多尺度"核函数的测量矩阵 Φ(a_p)
% 解原子范数最小化：
% min ||Σ A_k δ(a_{k,:})||_A  s.t. ||y - Φ*A||_2 ≤ ε
%
% 输入:
%   signal - 输入信号
%   params - 算法参数
%
% 输出:
%   A_optimal        - 最优原子幅度
%   a_optimal        - 最优原子参数
%   convergence_info - 收敛信息

%% 参数初始化
if nargin < 2
    params = get_default_atomic_params();
end

N = length(signal);
tm_norm = (0:N-1) / N; % 归一化时间

%% 阶段1：能量岛屿检测和初始化
fprintf('原子范数最小化：能量岛屿检测...\n');
[energy_islands, K_atoms] = detect_energy_islands(signal, params);

if K_atoms == 0
    warning('未检测到能量岛屿，返回FFT结果');
    A_optimal = abs(fft(signal));
    a_optimal = zeros(1, 4);
    convergence_info = struct('converged', false, 'iterations', 0);
    return;
end

fprintf('检测到 %d 个原子\n', K_atoms);

%% 阶段2：原子参数初始化
[A_init, a_init] = initialize_atomic_parameters(signal, energy_islands, tm_norm, params);

%% 阶段3：半定规划 + 梯度迭代求解
fprintf('原子范数最小化：SDP + 梯度迭代求解...\n');
[A_optimal, a_optimal, convergence_info] = solve_atomic_norm_sdp_gradient(...
    signal, A_init, a_init, tm_norm, params);

fprintf('原子范数最小化完成，收敛精度: %.2e\n', convergence_info.final_error);
end

%% 能量岛屿检测
function [energy_islands, K_atoms] = detect_energy_islands(signal, params)
% 检测信号中的能量岛屿

% FFT获取初始频谱
spectrum = fft(signal);
spectrum_power = abs(spectrum).^2;

% 自适应阈值
max_power = max(spectrum_power);
threshold = max_power * params.island_threshold_ratio;

% 检测峰值
energy_islands = find(spectrum_power > threshold);

% 聚类相邻的峰值
if ~isempty(energy_islands)
    % 合并相邻的岛屿
    island_groups = cluster_adjacent_peaks(energy_islands, params.island_merge_distance);
    
    % 每个组选择最强的峰值作为代表
    energy_islands = select_representative_peaks(spectrum_power, island_groups);
end

K_atoms = length(energy_islands);

% 限制原子数量
if K_atoms > params.max_atoms
    [~, sorted_indices] = sort(spectrum_power(energy_islands), 'descend');
    energy_islands = energy_islands(sorted_indices(1:params.max_atoms));
    K_atoms = params.max_atoms;
end
end

%% 聚类相邻峰值
function island_groups = cluster_adjacent_peaks(peaks, merge_distance)
% 将相邻的峰值聚类

if isempty(peaks)
    island_groups = {};
    return;
end

peaks = sort(peaks);
island_groups = {};
current_group = peaks(1);

for i = 2:length(peaks)
    if peaks(i) - peaks(i-1) <= merge_distance
        current_group = [current_group, peaks(i)];
    else
        island_groups{end+1} = current_group;
        current_group = peaks(i);
    end
end
island_groups{end+1} = current_group;
end

%% 选择代表性峰值
function representative_peaks = select_representative_peaks(spectrum_power, island_groups)
% 从每个岛屿组中选择最强的峰值

representative_peaks = [];
for i = 1:length(island_groups)
    group = island_groups{i};
    [~, max_idx] = max(spectrum_power(group));
    representative_peaks = [representative_peaks, group(max_idx)];
end
end

%% 原子参数初始化
function [A_init, a_init] = initialize_atomic_parameters(signal, energy_islands, tm_norm, params)
% 初始化原子参数

N = length(signal);
K_atoms = length(energy_islands);

% 初始化幅度
spectrum = fft(signal);
A_init = abs(spectrum(energy_islands));

% 初始化相位参数矩阵 [K_atoms × 4] for [f, α, β, γ]
a_init = zeros(K_atoms, 4);

for k = 1:K_atoms
    freq_idx = energy_islands(k);
    
    % 频率参数 (归一化)
    a_init(k, 1) = (freq_idx - 1) / N - 0.5; % 范围 [-0.5, 0.5]
    
    % 使用局部信号估计高阶参数
    local_params = estimate_local_chirp_parameters(signal, freq_idx, tm_norm, params);
    a_init(k, 2:4) = local_params;
end
end

%% 局部chirp参数估计
function local_params = estimate_local_chirp_parameters(signal, freq_idx, tm_norm, params)
% 估计局部chirp参数

N = length(signal);
window_size = min(params.local_window_size, N/4);

% 提取局部窗口
center_idx = freq_idx;
start_idx = max(1, center_idx - window_size/2);
end_idx = min(N, center_idx + window_size/2);
local_signal = signal(start_idx:end_idx);
local_time = tm_norm(start_idx:end_idx);

% 使用短时傅里叶变换估计瞬时频率
try
    [S, F, T] = spectrogram(local_signal, min(16, length(local_signal)/2), [], [], 1);
    [~, peak_indices] = max(abs(S), [], 1);
    inst_freq = F(peak_indices);
    
    % 多项式拟合 (最多3阶)
    if length(inst_freq) >= 3
        poly_coeffs = polyfit(T, inst_freq, min(2, length(inst_freq)-1));
        local_params = [0, 0, 0]; % [α, β, γ]
        
        % 分配系数 (注意顺序)
        for i = 1:min(length(poly_coeffs), 3)
            local_params(4-i) = poly_coeffs(i) * factorial(3-i); % 转换为标准形式
        end
    else
        local_params = [0, 0, 0];
    end
catch
    % 如果STFT失败，使用默认值
    local_params = [0, 0, 0];
end

% 添加小的随机扰动避免局部最优
local_params = local_params + randn(1, 3) * params.init_noise_level;
end

%% SDP + 梯度迭代求解
function [A_optimal, a_optimal, convergence_info] = solve_atomic_norm_sdp_gradient(...
    signal, A_init, a_init, tm_norm, params)
% 半定规划 + 梯度迭代求解原子范数最小化

K_atoms = size(a_init, 1);
N = length(signal);

% 初始化
A_current = A_init;
a_current = a_init;

% 收敛历史
max_iter = params.max_iterations;
tolerance = params.tolerance;
convergence_history = zeros(max_iter, 1);
objective_history = zeros(max_iter, 1);

% 自适应步长参数
step_size_A = params.step_size_amplitude;
step_size_a = params.step_size_phase;
momentum = 0.9;
velocity_A = zeros(size(A_current));
velocity_a = zeros(size(a_current));

fprintf('开始原子范数迭代优化...\n');

for iter = 1:max_iter
    % 构造测量矩阵
    Phi_matrix = construct_measurement_matrix(a_current, tm_norm);
    
    % 计算重构误差
    reconstructed_signal = Phi_matrix * A_current;
    residual = signal - reconstructed_signal;
    data_fidelity = 0.5 * norm(residual)^2;
    
    % 原子范数正则化项 (L1近似)
    atomic_norm_penalty = params.lambda_atomic * sum(A_current);
    
    % 总目标函数
    objective_val = data_fidelity + atomic_norm_penalty;
    objective_history(iter) = objective_val;
    
    % 计算梯度
    [grad_A, grad_a] = compute_atomic_gradients(signal, A_current, a_current, Phi_matrix, tm_norm, params);
    
    % 动量更新
    velocity_A = momentum * velocity_A - step_size_A * grad_A;
    velocity_a = momentum * velocity_a - step_size_a * grad_a;
    
    % 参数更新
    A_new = A_current + velocity_A;
    a_new = a_current + velocity_a;
    
    % 投影到可行域
    A_new = max(A_new, 0); % 幅度非负约束
    a_new = project_phase_parameters(a_new, params);
    
    % 收敛检查
    param_change = norm([A_new(:); a_new(:)] - [A_current(:); a_current(:)]) / ...
                   (norm([A_current(:); a_current(:)]) + eps);
    convergence_history(iter) = param_change;
    
    if param_change < tolerance
        fprintf('原子范数优化在第 %d 次迭代收敛\n', iter);
        break;
    end
    
    % 自适应步长调整
    if iter > 1 && objective_history(iter) > objective_history(iter-1)
        step_size_A = step_size_A * 0.8;
        step_size_a = step_size_a * 0.8;
    end
    
    % 更新当前参数
    A_current = A_new;
    a_current = a_new;
    
    % 进度显示
    if mod(iter, 20) == 0
        fprintf('  迭代 %d: 目标函数 = %.6f, 参数变化 = %.2e\n', iter, objective_val, param_change);
    end
end

% 输出结果
A_optimal = A_current;
a_optimal = a_current;

% 收敛信息
convergence_info.converged = (param_change < tolerance);
convergence_info.iterations = iter;
convergence_info.final_error = param_change;
convergence_info.convergence_history = convergence_history(1:iter);
convergence_info.objective_history = objective_history(1:iter);
convergence_info.final_objective = objective_val;
end

%% 构造测量矩阵
function Phi_matrix = construct_measurement_matrix(a_params, tm_norm)
% 构造多阶chirp原子的测量矩阵

N = length(tm_norm);
K_atoms = size(a_params, 1);
Phi_matrix = zeros(N, K_atoms, 'like', 1j);

for k = 1:K_atoms
    f_k = a_params(k, 1);
    alpha_k = a_params(k, 2);
    beta_k = a_params(k, 3);
    gamma_k = a_params(k, 4);
    
    % 构造第k个原子的相位函数
    phase_k = 2*pi * (f_k * tm_norm + ...
                      0.5 * alpha_k * tm_norm.^2 + ...
                      (1/6) * beta_k * tm_norm.^3 + ...
                      (1/24) * gamma_k * tm_norm.^4);
    
    Phi_matrix(:, k) = exp(1j * phase_k);
end
end

%% 计算原子梯度
function [grad_A, grad_a] = compute_atomic_gradients(signal, A_current, a_current, Phi_matrix, tm_norm, params)
% 计算原子参数的梯度

K_atoms = length(A_current);
N = length(signal);

% 重构误差
reconstructed_signal = Phi_matrix * A_current;
residual = signal - reconstructed_signal;

% 幅度梯度
grad_A = -real(Phi_matrix' * residual) + params.lambda_atomic * ones(K_atoms, 1);

% 相位参数梯度
grad_a = zeros(K_atoms, 4);
delta = 1e-6; % 数值梯度步长

for k = 1:K_atoms
    for p = 1:4
        % 数值梯度计算
        a_plus = a_current;
        a_plus(k, p) = a_plus(k, p) + delta;
        
        Phi_plus = construct_measurement_matrix(a_plus, tm_norm);
        residual_plus = signal - Phi_plus * A_current;
        
        grad_a(k, p) = real((norm(residual_plus)^2 - norm(residual)^2) / delta);
    end
end
end

%% 相位参数投影
function a_projected = project_phase_parameters(a_params, params)
% 将相位参数投影到可行域

a_projected = a_params;

% 频率约束 [-0.5, 0.5]
a_projected(:, 1) = max(-0.5, min(0.5, a_projected(:, 1)));

% 其他参数约束
a_projected(:, 2) = max(params.alpha_min, min(params.alpha_max, a_projected(:, 2)));
a_projected(:, 3) = max(params.beta_min, min(params.beta_max, a_projected(:, 3)));
a_projected(:, 4) = max(params.gamma_min, min(params.gamma_max, a_projected(:, 4)));
end

%% 默认参数
function params = get_default_atomic_params()
% 原子范数最小化默认参数

% 岛屿检测参数
params.island_threshold_ratio = 0.1; % 能量阈值比例
params.island_merge_distance = 3;    % 岛屿合并距离
params.max_atoms = 10;               % 最大原子数

% 初始化参数
params.local_window_size = 32;       % 局部窗口大小
params.init_noise_level = 0.01;     % 初始化噪声水平

% 优化参数
params.max_iterations = 200;         % 最大迭代次数
params.tolerance = 1e-6;             % 收敛容差
params.lambda_atomic = 0.01;         % 原子范数正则化参数

% 步长参数
params.step_size_amplitude = 0.01;   % 幅度步长
params.step_size_phase = 0.001;      % 相位步长

% 参数约束
params.alpha_min = -100; params.alpha_max = 100;
params.beta_min = -1000; params.beta_max = 1000;
params.gamma_min = -5000; params.gamma_max = 5000;
end
