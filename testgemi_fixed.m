% ISAR成像主脚本 - 增强版转动运动补偿
% 基于原始test_syntax.m改进，增强处理复杂旋转运动的能力

clear; close all; clc;

fprintf('开始ISAR成像仿真与深度融合处理...\n');

% 并行计算设置
use_parallel = false; % 设置为true启用并行计算，如果出现函数查找问题，请设为false

% -------------------- 1. 数据加载与仿真参数 -------------------- %

fprintf('加载/生成雷达回波数据...\n');

tic;

% 尝试加载实际数据，如果不存在则生成仿真数据
try
    load shipx2.mat; % 加载实际数据 shipx2_1000
    load s_r_tm2.mat
    echo_data = s_r_tm2;
    fprintf('实际数据 shipx2_1000.mat 加载成功。\n');
    
    % 为实际数据设置一个基础的sim_params结构体 (部分参数可能需要根据实际数据特性调整)
    sim_params = struct();
    sim_params.Num_r = size(echo_data, 1);
    sim_params.Num_tm = size(echo_data, 2);
    sim_params.PRF = 1400; % 假设值, PRF for shipx2_1000 if known, else adjust
    sim_params.fc = 5.2e9; % 假设值, Carrier frequency
    sim_params.c = 3e8;
    sim_params.B = 80e6; % 假设值, Bandwidth
    
    delta_r_res_actual = sim_params.c / (2*sim_params.B);
    r_center_actual = 0; % 假设目标中心距离
    sim_params.r_axis = linspace(r_center_actual - (sim_params.Num_r/2)*delta_r_res_actual, r_center_actual + (sim_params.Num_r/2-1)*delta_r_res_actual, sim_params.Num_r);
    sim_params.tm = linspace(0, (sim_params.Num_tm-1)/sim_params.PRF, sim_params.Num_tm);
catch
    fprintf('未找到数据文件，生成仿真数据...\n');
    [echo_data, sim_params] = generate_simulated_echo(); % 保留仿真数据生成作为备选
end

fprintf('数据加载/生成完毕。耗时: %.2f 秒\n', toc);
fprintf('回波数据尺寸: %d (距离单元) x %d (方位单元)\n', size(echo_data, 1), size(echo_data, 2));

% -------------------- 2. 设置处理参数 -------------------- %

params_proc = struct();

% VMD 参数 (用于ADMM子问题)
params_proc.vmd.K = 3; % 模态数量 (经验值, 可调整)
params_proc.vmd.alpha_vmd = 2000; % 带宽约束强度 (VMD内部的alpha)
params_proc.vmd.tau_vmd = 0; % 拉格朗日乘子更新率 (0表示无噪声，经典VMD中)
params_proc.vmd.tol_vmd_inner = 1e-3; % VMD内部迭代收敛容限
params_proc.vmd.max_iter_vmd_inner = 5; % VMD内部最大迭代次数 (在ADMM每一步中)
params_proc.vmd.init_omega_method = 'peaks'; % 'peaks' 或 'linear'
params_proc.vmd.alpha_phase_guidance = 0.5; % VMD引导中相位先验的权重 (用于VMD更新)

% 相位估算 (DCFT-like, 用于ADMM子问题) 参数 - 增强版
params_proc.phase_est.poly_order = 5;  % 增加到5阶多项式
params_proc.phase_est.fd_search_range_factor = 0.5;
params_proc.phase_est.ka_search_pts = 41;  % 增加搜索点数
params_proc.phase_est.kb_search_pts = 31;
params_proc.phase_est.kc_search_pts = 21;  % 新增高阶搜索
params_proc.phase_est.kd_search_pts = 15;  % 新增高阶搜索
params_proc.phase_est.max_iter_phase_inner = 20;  % 增加迭代次数

% 全局 ADMM 参数 (用于整体优化框架)
params_proc.admm_global.rho_X = 1.0; % 增广拉格朗日参数 (针对 X=FFT(S_comp) 的约束)
params_proc.admm_global.rho_U = 0.5; % 增广拉格朗日参数 (针对 u_k 分解的约束, s = sum(u_k))
params_proc.admm_global.rho_P = 0.5; % 增广拉格朗日参数 (针对相位平滑或先验的约束, 可选)
params_proc.admm_global.lambda_sparsity = 0.05; % 稀疏正则化权重 (针对 X)
params_proc.admm_global.max_iter = 5;  % 从1增加到5
params_proc.admm_global.tol = 1e-3; % ADMM全局收敛容限
params_proc.admm_global.alpha_data_fidelity = 1.0; % 数据保真项 ||s - sum(u_k)||^2 的权重
params_proc.admm_global.alpha_phase_sharpness = 0.1; % 相位聚焦项权重

% 增加旋转模型参数
params_proc.rotation_model = struct();
params_proc.rotation_model.enable_spatial_coherence = true;  % 启用空间相干性
params_proc.rotation_model.smoothing_window = 5;  % 平滑窗口大小
params_proc.rotation_model.position_weight = 0.8;  % 位置相关权重

% 其他处理参数
params_proc.num_azimuth = sim_params.Num_tm; % 方位单元数
params_proc.num_range_bins = sim_params.Num_r; % 距离单元数
params_proc.PRF = sim_params.PRF;
params_proc.fc = sim_params.fc;
params_proc.c = sim_params.c;
params_proc.tm_azimuth = sim_params.tm; % 慢时间轴
params_proc.normalized_tm = (0:params_proc.num_azimuth-1) / params_proc.num_azimuth; % 归一化慢时间

% 初始化全局相位系数存储
params_proc.global_phase_coeffs = cell(params_proc.num_range_bins, 1);

% 并行计算参数
params_proc.use_parallel = use_parallel;

% -------------------- 3. 执行深度融合ISAR成像算法 -------------------- %

fprintf('开始执行深度融合VMD-ADMM-DCFT ISAR成像算法 (增强版)...\n');

tic;

[ISAR_image_fused, dominant_mode_compensated_fft, vmd_modes_all_bins, phase_coeffs_all_bins, admm_convergence_all_bins] = perform_isar_imaging_fused_admm_enhanced(echo_data, params_proc, sim_params);

fprintf('深度融合ISAR成像处理完毕。耗时: %.2f 秒\n', toc);

% -------------------- 4. 显示结果 -------------------- %

fprintf('显示成像结果...\n');

% --- 原始数据和直接FFT ---
figure('Name', '原始数据和直接FFT');
subplot(1,2,1);
imagesc(sim_params.tm, sim_params.r_axis, abs(echo_data));
xlabel('慢时间 (秒)'); ylabel('距离 (米)'); title('距离压缩后的原始回波'); colorbar; axis xy;

raw_fft = fftshift(fft(echo_data, [], 2), 2);
doppler_axis = linspace(-params_proc.PRF/2, params_proc.PRF/2, params_proc.num_azimuth);

subplot(1,2,2);
imagesc(doppler_axis, sim_params.r_axis, abs(raw_fft));
xlabel('多普勒频率 (Hz)'); ylabel('距离 (米)'); title('原始数据直接FFT'); colorbar; axis xy;

% --- 融合算法成像结果 ---
ISAR_image_fused_shifted = fftshift(ISAR_image_fused, 2);

figure('Name', '深度融合ADMM成像结果');
imagesc(doppler_axis, sim_params.r_axis, abs(ISAR_image_fused_shifted));
xlabel('多普勒频率 (Hz)'); ylabel('距离 (米)'); title('深度融合VMD-ADMM-DCFT ISAR结果'); colorbar; axis xy;

% --- 对数尺度显示 ---
figure('Name', '对数尺度对比');
G_raw = 20*log10(abs(raw_fft)./max(abs(raw_fft(:))));
imagesc(doppler_axis, sim_params.r_axis, G_raw); caxis([-30,0]);
xlabel('多普勒频率 (Hz)'); ylabel('距离 (米)'); title('直接FFT (dB)'); colorbar; axis xy; colormap('jet');

figure('Name', '对数尺度对比');
G_fused = 20*log10(abs(ISAR_image_fused_shifted)./max(abs(ISAR_image_fused_shifted(:))));
imagesc(doppler_axis, sim_params.r_axis, G_fused); caxis([-40,0]);
xlabel('多普勒频率 (Hz)'); ylabel('距离 (米)'); title('深度融合ADMM (dB)'); colorbar; axis xy; colormap('jet');

% --- (可选) 主导模态补偿 + FFT 结果 (如果算法输出) ---
if exist('dominant_mode_compensated_fft', 'var') && ~isempty(dominant_mode_compensated_fft)
    ISAR_image_fft_dominant_shifted = fftshift(dominant_mode_compensated_fft, 2);
    figure('Name', '对数尺度对比');
    G_dominant = 20*log10(abs(ISAR_image_fft_dominant_shifted)./max(abs(ISAR_image_fft_dominant_shifted(:))));
    imagesc(doppler_axis, sim_params.r_axis, G_dominant); caxis([-30,0]);
    xlabel('多普勒频率 (Hz)'); ylabel('距离 (米)'); title('主导模态补偿+FFT (dB)'); colorbar; axis xy; colormap('jet');
end

% --- 图像质量指标 ---
contrast_fused = calculate_image_contrast(abs(ISAR_image_fused_shifted));
entropy_fused = calculate_image_entropy(abs(ISAR_image_fused_shifted));
contrast_raw = calculate_image_contrast(abs(raw_fft));
entropy_raw = calculate_image_entropy(abs(raw_fft));

fprintf('直接FFT图像质量指标:\n');
fprintf(' - 对比度: %.4f\n', contrast_raw);
fprintf(' - 熵: %.4f\n', entropy_raw);

fprintf('深度融合ADMM图像质量指标:\n');
fprintf(' - 对比度: %.4f\n', contrast_fused);
fprintf(' - 熵: %.4f\n', entropy_fused);

fprintf('处理完成。\n');

% 辅助函数

% 计算图像对比度
function contrast = calculate_image_contrast(image_abs)
    if isempty(image_abs) || numel(image_abs) < 2
        contrast = 0;
        return;
    end
    mean_val = mean(image_abs(:));
    std_val = std(image_abs(:));
    if mean_val == 0
        contrast = 0;
    else
        contrast = std_val / mean_val;
    end
end

% 计算图像熵
function entropy = calculate_image_entropy(image_abs)
    if isempty(image_abs)
        entropy = NaN;
        return;
    end
    image_power = image_abs(:).^2;
    sum_power = sum(image_power);
    if sum_power == 0
        entropy = 0;
        return;
    end
    normalized_power = image_power / sum_power;
    valid_indices = normalized_power > eps;
    if ~any(valid_indices)
        entropy = 0;
        return;
    end
    entropy = -sum(normalized_power(valid_indices) .* log2(normalized_power(valid_indices)));
end

% 生成仿真数据
function [s_r_tm2, sim_params] = generate_simulated_echo()
    % 目标散射点模型
    Pos = [-10 -1 0;-9 -1 0;-8 -1 0;-7 -1 0;-6 -1 0;-5 -1 0;-3.75 -1 0;-3 -1 0;-2 -1 0;-1 -1 0;...
        0 -1 0;...
        1 -1 0;2 -1 0;3 -1 0;4 -1 0;5 -1 0;6 -1 0;7 -1 0;8 -1 0;9 -1 0;10 -1 0;...
        -9.5 0.2 0.5;...
        -9.5 1.2 0.5;-9 1 0;-8 1 0;-7 1 0;-6 1 0;-5.2 1.2 0;-4.1 1 0;-3 1 0;-2 1 0;-1 1 0;...
        0 1 0;...
        1 1 0;2 1 0;3 1 0;4 1 0;5 1 0;6 1 0;7 1 0;8 1 0;9 1 0;10 1 0;...
        10.5 -0.75 0;10.5 0.75 0;11 -0.5 0;11 0.5 0;11.5 0 0;... % 尾部
        9.5 0.5 0.5;9.5 -0.5 0.5;9 0 0.5;8.5 0.5 0.5;8.5 -0.5 0.5;... % 头部
        5 0 0.5;5 0 1;5 0 1.5;5 0 2;5 0 2.5;5 0 3;5 0 3.5;5 0 4;... % 机头
        5.5 0.5 0.5;5.5 -0.5 0.5;4.5 0.5 0.5;4.5 -0.5 0.5;... % 机头运动
        0.5 0.5 0.9;0.5 -0.5 0.9;-0.5 0.5 0.9;-0.5 -0.5 0.9;0 0 0.5;... % 中间运动
        -5 0 0.2;-5 0.2 0.8;-5 0.2 1.4;-5 0 2;... % 机尾部
        -5.5 -0.5 0.5;-5.5 0.5 0.5;-4.4 0.5 0.5;-4.5 -0.6 0.5;...% 机尾运动
        ];
    x_Pos_orig = Pos(:,1)*5;
    y_Pos_orig = Pos(:,2)*5;
    z_Pos_orig = Pos(:,3)*5;
    x_Pos = x_Pos_orig;
    y_Pos = y_Pos_orig;
    z_Pos = z_Pos_orig;
    
    R_los = [cos(3*pi/8)*cos(0), cos(3*pi/8)*sin(0), sin(3*pi/8)];
    Num_point = size(x_Pos, 1);
    
    x_r_proj = zeros(1,Num_point);
    y_r_proj = zeros(1,Num_point);
    z_r_proj = zeros(1,Num_point);
    
    for n_point = 1:Num_point
        x_r_proj(n_point) = y_Pos(n_point)*R_los(3) - z_Pos(n_point)*R_los(2);
        y_r_proj(n_point) = z_Pos(n_point)*R_los(1) - x_Pos(n_point)*R_los(3);
        z_r_proj(n_point) = x_Pos(n_point)*R_los(2) - y_Pos(n_point)*R_los(1);
    end
    
    % 增强型旋转运动模型 - 包含更复杂的三维旋转
    x_omega = 0.05;  % 翻滚角速度
    y_omega = 0.2;   % 俯仰角速度
    z_omega = 0.05;  % 偏航角速度
    
    x_alpha_rot = 0.05;  % 翻滚角加速度
    y_alpha_rot = 0.1;   % 俯仰角加速度
    z_alpha_rot = 0.05;  % 偏航角加速度
    
    x_beta_rot = 0.05;   % 翻滚角加加速度
    y_beta_rot = 0.2;    % 俯仰角加加速度
    z_beta_rot = 0.05;   % 偏航角加加速度
    
    % 更加复杂的周期性摇摆 (模拟海浪影响)
    roll_period = 2.0;   % 翻滚周期 (秒)
    pitch_period = 3.0;  % 俯仰周期 (秒)
    yaw_period = 5.0;    % 偏航周期 (秒)
    
    f_v_coeffs = zeros(1,Num_point);
    alpha_v_coeffs = zeros(1,Num_point);
    beta_v_coeffs = zeros(1,Num_point);
    
    for n_point = 1:Num_point
        f_v_coeffs(n_point) = x_r_proj(n_point)*x_omega + y_r_proj(n_point)*y_omega + z_r_proj(n_point)*z_omega;
        alpha_v_coeffs(n_point) = x_r_proj(n_point)*x_alpha_rot + y_r_proj(n_point)*y_alpha_rot + z_r_proj(n_point)*z_alpha_rot;
        beta_v_coeffs(n_point) = x_r_proj(n_point)*x_beta_rot + y_r_proj(n_point)*y_beta_rot + z_r_proj(n_point)*z_beta_rot;
    end
    
    sim_params.B = 80*1e6;
    sim_params.c = 3*1e8;
    sim_params.PRF = 1400;
    sim_params.fc = 5.2*1e9;
    
    delta_r_res = sim_params.c / (2*sim_params.B);
    sim_params.r_axis = (-50*delta_r_res : delta_r_res : 50*delta_r_res-delta_r_res); % 确保长度为100
    
    if length(sim_params.r_axis) > 100
        sim_params.r_axis = sim_params.r_axis(1:100);
    elseif length(sim_params.r_axis) < 100
        sim_params.r_axis = linspace(min(sim_params.r_axis), max(sim_params.r_axis), 100);
    end
    
    sim_params.tm = (0 : (1/sim_params.PRF) : 0.501);
    sim_params.Num_r = length(sim_params.r_axis);
    sim_params.Num_tm = length(sim_params.tm);
    
    ones_r_vec = ones(1, sim_params.Num_r);
    ones_tm_vec = ones(1, sim_params.Num_tm);
    
    s_r_tm2 = zeros(sim_params.Num_r, sim_params.Num_tm);
    
    Delta_R0_init = zeros(1,Num_point);
    
    fprintf(' 逐散射点生成回波...\n');
    
    for n_point = 1:Num_point
        Delta_R0_init(n_point) = x_Pos(n_point)*R_los(1) + y_Pos(n_point)*R_los(2) + z_Pos(n_point)*R_los(3);
        
        % 基础相位模型 (多项式)
        Delta_R_t_base = Delta_R0_init(n_point) + ...
                     f_v_coeffs(n_point) .* sim_params.tm + ...
                     (1/2) * alpha_v_coeffs(n_point) .* sim_params.tm.^2 + ...
                     (1/6) * beta_v_coeffs(n_point) .* sim_params.tm.^3;
        
        % 添加周期性摇摆项 (正弦项)
        roll_term = 0.1 * sin(2*pi*sim_params.tm/roll_period) * x_r_proj(n_point);
        pitch_term = 0.15 * sin(2*pi*sim_params.tm/pitch_period) * y_r_proj(n_point);
        yaw_term = 0.05 * sin(2*pi*sim_params.tm/yaw_period) * z_r_proj(n_point);
        
        % 最终的径向距离变化
        Delta_R_t = Delta_R_t_base + roll_term + pitch_term + yaw_term;
        
        phase_term = (4*pi*sim_params.fc/sim_params.c) * Delta_R_t;
        
        amplitude_factor = 1.0;
        if n_point > 53 && n_point < 62
            amplitude_factor = 1.3;
        end
        
        s_r_tm2 = s_r_tm2 + amplitude_factor * sinc((2*sim_params.B/sim_params.c) * (sim_params.r_axis.' * ones_tm_vec - ones_r_vec.' * Delta_R_t)) .*exp(1j * ones_r_vec.' * phase_term);
        
        if mod(n_point, 20) == 0
            fprintf(' 已处理 %d / %d 个散射点\n', n_point, Num_point);
        end
    end
    
    fprintf(' 所有散射点回波生成完毕。\n');
end