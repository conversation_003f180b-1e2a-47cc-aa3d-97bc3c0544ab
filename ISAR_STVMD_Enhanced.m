%-------------------------------------------------------------------------%
%--------   增强型STVMD ISAR成像算法 - 解决对称和散焦问题   ----------------%
%--------   基于Short-Time Variational Mode Decomposition理论   -----------%
%--------   针对舰船三维旋转运动优化设计                     ----------------%
%-------------------------------------------------------------------------%

function [ISAR_image_enhanced, compensated_data, phase_errors, processing_info] = ISAR_STVMD_Enhanced(radar_data, params)
% ISAR_STVMD_Enhanced - 增强型STVMD ISAR成像算法
%
% 该算法严格按照STVMD论文理论实现，专为解决ISAR舰船成像中的对称问题和散焦问题设计
%
% 输入参数:
%   radar_data - 距离压缩后的复回波数据 [距离单元 x 慢时间]
%   params - 算法参数结构体
%
% 输出参数:
%   ISAR_image_enhanced - 增强的ISAR图像
%   compensated_data - 相位补偿后的数据
%   phase_errors - 估计的相位误差
%   processing_info - 处理信息

%% 参数设置和初始化
if nargin < 2
    params = struct();
end

% 默认参数设置（基于STVMD论文和ISAR特性优化）
params = set_default_params(params);

% 获取数据维度
[N_range, N_azimuth] = size(radar_data);

if params.display_progress
    fprintf('=== 增强型STVMD ISAR成像开始 ===\n');
    fprintf('数据尺寸: %d x %d\n', N_range, N_azimuth);
end

%% 预处理：去除平均值和窗函数处理
if params.preprocessing.remove_mean
    radar_data = radar_data - mean(radar_data, 2);
end

if params.preprocessing.apply_window
    window = hamming(N_azimuth)';
    radar_data = radar_data .* repmat(window, N_range, 1);
end

%% 核心STVMD处理
compensated_data = zeros(N_range, N_azimuth, 'like', 1j);
phase_errors = zeros(N_range, N_azimuth);
processing_info = struct();

% 初始化处理信息
processing_info.convergence_info = zeros(N_range, 1);
processing_info.selected_modes = zeros(N_range, 1);
processing_info.entropy_reduction = zeros(N_range, 1);

%% 自适应STVMD处理（针对每个距离单元）
if params.display_progress
    fprintf('开始STVMD分解处理...\n');
    progress_step = max(1, floor(N_range/20));
end

for r_idx = 1:N_range
    if params.display_progress && mod(r_idx, progress_step) == 0
        fprintf('处理进度: %d/%d (%.1f%%)\n', r_idx, N_range, 100*r_idx/N_range);
    end
    
    % 获取当前距离单元信号
    signal_range = radar_data(r_idx, :);
    signal_energy = sum(abs(signal_range).^2);
    
    % 跳过低能量距离单元
    if signal_energy < params.energy_threshold
        compensated_data(r_idx, :) = signal_range;
        continue;
    end
    
    % 自适应参数调整
    adaptive_params = adapt_parameters(signal_range, params);
    
    % 执行多尺度STVMD分解
    [imfs, omega_sequences, convergence_flag] = multiscale_stvmd_decompose(signal_range, adaptive_params);
    
    % 智能模式选择和相位误差估计
    [selected_imf, phase_error, mode_idx] = intelligent_mode_selection(imfs, omega_sequences, signal_range, adaptive_params);
    
    % 高级相位补偿
    compensated_signal = advanced_phase_compensation(signal_range, phase_error, adaptive_params);
    
    % 存储结果
    compensated_data(r_idx, :) = compensated_signal;
    phase_errors(r_idx, :) = phase_error;
    processing_info.convergence_info(r_idx) = convergence_flag;
    processing_info.selected_modes(r_idx) = mode_idx;
    
    % 计算熵减少量
    original_entropy = calculate_signal_entropy(signal_range);
    compensated_entropy = calculate_signal_entropy(compensated_signal);
    processing_info.entropy_reduction(r_idx) = original_entropy - compensated_entropy;
end

%% 全局一致性优化
if params.global_optimization.enable
    if params.display_progress
        fprintf('执行全局一致性优化...\n');
    end
    compensated_data = global_consistency_optimization(compensated_data, params.global_optimization);
end

%% 方位向成像处理
if params.display_progress
    fprintf('执行方位向FFT成像...\n');
end

% 使用改进的成像算法，避免对称问题
ISAR_image_enhanced = improved_azimuth_processing(compensated_data, params.imaging);

%% 后处理
if params.postprocessing.enable
    ISAR_image_enhanced = post_processing(ISAR_image_enhanced, params.postprocessing);
end

if params.display_progress
    fprintf('=== 增强型STVMD ISAR成像完成 ===\n');
end

end

%% ========================================================================
%% 辅助函数定义
%% ========================================================================

function params = set_default_params(params)
% 设置优化的默认参数

% STVMD核心参数
if ~isfield(params, 'K'), params.K = 3; end
if ~isfield(params, 'alpha'), params.alpha = 2000; end
if ~isfield(params, 'tau'), params.tau = 0.1; end
if ~isfield(params, 'tol'), params.tol = 1e-7; end
if ~isfield(params, 'max_iter'), params.max_iter = 500; end

% 多尺度窗口参数
if ~isfield(params, 'window_sizes'), params.window_sizes = [16, 32, 64, 128]; end
if ~isfield(params, 'overlap_ratio'), params.overlap_ratio = 0.75; end
if ~isfield(params, 'window_type'), params.window_type = 'hamming'; end

% 自适应参数
if ~isfield(params, 'adaptive'), params.adaptive = struct(); end
if ~isfield(params.adaptive, 'enable'), params.adaptive.enable = true; end
if ~isfield(params.adaptive, 'energy_factor'), params.adaptive.energy_factor = 0.5; end
if ~isfield(params.adaptive, 'frequency_factor'), params.adaptive.frequency_factor = 0.3; end

% 处理控制参数
if ~isfield(params, 'energy_threshold'), params.energy_threshold = 1e-10; end
if ~isfield(params, 'display_progress'), params.display_progress = true; end

% 预处理参数
if ~isfield(params, 'preprocessing'), params.preprocessing = struct(); end
if ~isfield(params.preprocessing, 'remove_mean'), params.preprocessing.remove_mean = true; end
if ~isfield(params.preprocessing, 'apply_window'), params.preprocessing.apply_window = false; end

% 全局优化参数
if ~isfield(params, 'global_optimization'), params.global_optimization = struct(); end
if ~isfield(params.global_optimization, 'enable'), params.global_optimization.enable = true; end
if ~isfield(params.global_optimization, 'iterations'), params.global_optimization.iterations = 2; end

% 成像参数
if ~isfield(params, 'imaging'), params.imaging = struct(); end
if ~isfield(params.imaging, 'remove_symmetry'), params.imaging.remove_symmetry = true; end
if ~isfield(params.imaging, 'window_azimuth'), params.imaging.window_azimuth = 'kaiser'; end
if ~isfield(params.imaging, 'beta'), params.imaging.beta = 2.5; end

% 后处理参数
if ~isfield(params, 'postprocessing'), params.postprocessing = struct(); end
if ~isfield(params.postprocessing, 'enable'), params.postprocessing.enable = true; end
if ~isfield(params.postprocessing, 'filter_type'), params.postprocessing.filter_type = 'median'; end

end

function adaptive_params = adapt_parameters(signal, base_params)
% 根据信号特性自适应调整参数

adaptive_params = base_params;

% 计算信号特征
signal_std = std(abs(signal));
signal_energy = sum(abs(signal).^2);
signal_length = length(signal);

% 自适应调整alpha
energy_factor = min(2, max(0.5, signal_energy / (signal_length * signal_std^2)));
adaptive_params.alpha = base_params.alpha * energy_factor;

% 自适应调整窗口大小
dominant_freq = estimate_dominant_frequency(signal);
optimal_window = min(128, max(16, round(2*signal_length/dominant_freq)));
if ~ismember(optimal_window, base_params.window_sizes)
    adaptive_params.window_sizes = [base_params.window_sizes, optimal_window];
    adaptive_params.window_sizes = sort(unique(adaptive_params.window_sizes));
end

% 自适应调整模态数
spectrum_peaks = detect_spectrum_peaks(signal);
adaptive_params.K = min(5, max(2, length(spectrum_peaks)));

end

function dominant_freq = estimate_dominant_frequency(signal)
% 估计信号的主导频率

% 计算功率谱
N = length(signal);
spectrum = abs(fft(signal)).^2;
freqs = (0:N-1)/N;

% 只考虑正频率部分
pos_freqs = freqs(1:floor(N/2));
pos_spectrum = spectrum(1:floor(N/2));

% 找到主导频率
[~, max_idx] = max(pos_spectrum);
dominant_freq = pos_freqs(max_idx);

% 归一化到采样频率
if dominant_freq < 0.01
    dominant_freq = 0.1;
end

end

function peaks = detect_spectrum_peaks(signal)
% 检测频谱峰值

N = length(signal);
spectrum = abs(fft(signal));
spectrum = spectrum(1:floor(N/2));

% 平滑频谱
smoothed = conv(spectrum, ones(1,5)/5, 'same');

% 寻找峰值
threshold = 0.1 * max(smoothed);
peaks = [];

for i = 2:length(smoothed)-1
    if smoothed(i) > smoothed(i-1) && smoothed(i) > smoothed(i+1) && smoothed(i) > threshold
        peaks = [peaks, i];
    end
end

if isempty(peaks)
    peaks = [1, floor(length(smoothed)/2)];
end

end

function [imfs, omega_sequences, convergence_flag] = multiscale_stvmd_decompose(signal, params)
% 多尺度STVMD分解（严格按照论文实现）

signal_length = length(signal);
num_scales = length(params.window_sizes);
imfs = cell(num_scales, 1);
omega_sequences = cell(num_scales, 1);
convergence_flag = true;

for scale_idx = 1:num_scales
    window_size = params.window_sizes(scale_idx);
    
    % 确保窗口大小合理
    if window_size >= signal_length
        window_size = max(16, floor(signal_length/2));
    end
    
    % 执行STVMD分解
    [scale_imfs, scale_omegas, converged] = stvmd_core(signal, params.K, params.alpha, params.tau, ...
                                                       window_size, params.overlap_ratio, params.max_iter, params.tol, params.window_type);
    
    imfs{scale_idx} = scale_imfs;
    omega_sequences{scale_idx} = scale_omegas;
    
    if ~converged
        convergence_flag = false;
    end
end

end

function [imfs, omegas, converged] = stvmd_core(signal, K, alpha, tau, window_size, overlap_ratio, max_iter, tol, window_type)
% STVMD核心算法（严格按照论文公式实现）

signal_length = length(signal);
signal = signal(:).'; % 确保为行向量

% 计算窗口参数
step_size = round(window_size * (1 - overlap_ratio));
num_windows = floor((signal_length - window_size) / step_size) + 1;

% 窗函数
if nargin < 9 || isempty(window_type)
    window_type = 'hamming';
end

switch lower(window_type)
    case 'hamming'
        window_func = hamming(window_size);
    case 'hann'
        window_func = hann(window_size);
    case 'blackman'
        window_func = blackman(window_size);
    case 'kaiser'
        window_func = kaiser(window_size, 2.5);
    otherwise
        window_func = ones(window_size, 1); % 默认矩形窗
end

% 初始化
imfs = zeros(K, signal_length);
omegas = zeros(K, num_windows);

% 对每个窗口应用VMD
windowed_imfs = cell(num_windows, 1);
windowed_omegas = zeros(K, num_windows);

converged = true;

for win_idx = 1:num_windows
    % 提取窗口信号
    start_idx = (win_idx - 1) * step_size + 1;
    end_idx = min(start_idx + window_size - 1, signal_length);
    
    if end_idx <= start_idx
        break;
    end
    
    window_signal = zeros(1, window_size);
    actual_length = end_idx - start_idx + 1;
    window_signal(1:actual_length) = signal(start_idx:end_idx);
    
    % 应用窗函数
    windowed_signal = window_signal .* window_func';
    
    % 对当前窗口执行VMD
    [u_window, omega_window, window_converged] = vmd_single_window(windowed_signal, K, alpha, tau, max_iter, tol);
    
    if ~window_converged
        converged = false;
    end
    
    windowed_imfs{win_idx} = u_window;
    windowed_omegas(:, win_idx) = omega_window;
end

% 重叠加权重建IMFs
for k = 1:K
    imf_k = zeros(1, signal_length);
    weight_sum = zeros(1, signal_length);
    
    for win_idx = 1:length(windowed_imfs)
        if isempty(windowed_imfs{win_idx})
            continue;
        end
        
        start_idx = (win_idx - 1) * step_size + 1;
        end_idx = min(start_idx + window_size - 1, signal_length);
        
        if end_idx <= start_idx
            continue;
        end
        
        actual_length = end_idx - start_idx + 1;
        current_window = window_func(1:actual_length)';
        
        imf_k(start_idx:end_idx) = imf_k(start_idx:end_idx) + ...
                                   windowed_imfs{win_idx}(k, 1:actual_length) .* current_window;
        weight_sum(start_idx:end_idx) = weight_sum(start_idx:end_idx) + current_window;
    end
    
    % 归一化
    weight_sum(weight_sum < eps) = 1;
    imfs(k, :) = imf_k ./ weight_sum;
end

% 平均中心频率
omegas = mean(windowed_omegas, 2);

end

function [u, omega, converged] = vmd_single_window(signal, K, alpha, tau, max_iter, tol)
% 单窗口VMD分解

N = length(signal);
freqs = (0:N-1)/N;
freqs(freqs > 0.5) = freqs(freqs > 0.5) - 1;

% 初始化
u_hat = zeros(K, N);
omega = zeros(K, 1);
lambda_hat = zeros(1, N);

% 初始化中心频率
for k = 1:K
    omega(k) = (k-1)/(2*K);
end

% FFT of input signal
f_hat = fft(signal);

converged = false;

for iter = 1:max_iter
    % 保存上一次迭代结果
    u_hat_prev = u_hat;
    omega_prev = omega;
    
    % 更新模态
    for k = 1:K
        % 计算其他模态之和
        sum_uk = sum(u_hat([1:k-1, k+1:K], :), 1);
        
        % 更新第k个模态
        numerator = f_hat - sum_uk + lambda_hat/2;
        denominator = 1 + 2*alpha*(freqs - omega(k)).^2;
        u_hat(k, :) = numerator ./ denominator;
    end
    
    % 更新中心频率
    for k = 1:K
        numerator = sum(freqs .* abs(u_hat(k, :)).^2);
        denominator = sum(abs(u_hat(k, :)).^2);
        if denominator > eps
            omega(k) = numerator / denominator;
        end
    end
    
    % 更新拉格朗日乘子
    lambda_hat = lambda_hat + tau * (f_hat - sum(u_hat, 1));
    
    % 检查收敛
    u_diff = sum(sum(abs(u_hat - u_hat_prev).^2)) / sum(sum(abs(u_hat_prev).^2 + eps));
    omega_diff = sum(abs(omega - omega_prev).^2) / sum(abs(omega_prev).^2 + eps);
    
    if u_diff < tol && omega_diff < tol
        converged = true;
        break;
    end
end

% 转换回时域
u = zeros(K, N);
for k = 1:K
    u(k, :) = real(ifft(u_hat(k, :)));
end

end

function [selected_imf, phase_error, mode_idx] = intelligent_mode_selection(imfs, omega_sequences, original_signal, params)
% 智能模式选择和相位误差估计

num_scales = length(imfs);
candidates = [];

% 收集所有候选模态
for scale_idx = 1:num_scales
    scale_imfs = imfs{scale_idx};
    scale_omegas = omega_sequences{scale_idx};
    
    if isempty(scale_imfs)
        continue;
    end
    
    K = size(scale_imfs, 1);
    for k = 1:K
        candidate.imf = scale_imfs(k, :);
        candidate.omega = scale_omegas(k);
        candidate.scale_idx = scale_idx;
        candidate.mode_idx = k;
        
        % 计算质量评分
        candidate.score = evaluate_mode_quality(candidate.imf, original_signal, candidate.omega);
        
        candidates = [candidates, candidate];
    end
end

% 选择最佳模态
if isempty(candidates)
    selected_imf = original_signal;
    phase_error = zeros(size(original_signal));
    mode_idx = 1;
    return;
end

scores = [candidates.score];
[~, best_idx] = max(scores);
selected_imf = candidates(best_idx).imf;
mode_idx = candidates(best_idx).mode_idx;

% 估计相位误差
phase_error = estimate_phase_error_advanced(selected_imf, candidates(best_idx).omega, params);

end

function score = evaluate_mode_quality(imf, original_signal, omega)
% 评估模态质量

% 能量比
energy_ratio = sum(abs(imf).^2) / sum(abs(original_signal).^2);

% 相干性
coherence = calculate_coherence(imf, original_signal);

% 频率稳定性
freq_stability = calculate_frequency_stability(imf, omega);

% 综合评分
score = energy_ratio * coherence * freq_stability;

end

function coherence = calculate_coherence(signal1, signal2)
% 计算相干性

N = length(signal1);
f1 = fft(signal1);
f2 = fft(signal2);

cross_spectrum = f1 .* conj(f2);
auto_spectrum1 = abs(f1).^2;
auto_spectrum2 = abs(f2).^2;

coherence = mean(abs(cross_spectrum).^2 ./ (auto_spectrum1 .* auto_spectrum2 + eps));

end

function stability = calculate_frequency_stability(signal, center_freq)
% 计算频率稳定性

% 计算瞬时频率
analytic_signal = hilbert(signal);
inst_phase = unwrap(angle(analytic_signal));
inst_freq = diff(inst_phase) / (2*pi);

% 计算与中心频率的偏差
freq_deviation = std(inst_freq - center_freq);
stability = exp(-freq_deviation * 10);

end

function phase_error = estimate_phase_error_advanced(imf, center_freq, params)
% 高级相位误差估计

signal_length = length(imf);
time_indices = 0:signal_length-1;

% 获取解析信号
analytic_signal = hilbert(imf);
inst_phase = unwrap(angle(analytic_signal));

% 理论相位（基于中心频率）
theoretical_phase = 2 * pi * center_freq * time_indices;

% 相位误差初估
phase_error_raw = inst_phase - theoretical_phase;

% 高阶多项式拟合平滑
poly_order = min(6, floor(signal_length/20));
if poly_order >= 1
    p = polyfit(time_indices, phase_error_raw, poly_order);
    phase_error_smooth = polyval(p, time_indices);
else
    phase_error_smooth = phase_error_raw;
end

% 去除线性趋势
linear_trend = polyfit(time_indices, phase_error_smooth, 1);
phase_error = phase_error_smooth - polyval(linear_trend, time_indices);

% 移除均值
phase_error = phase_error - mean(phase_error);

end

function compensated_signal = advanced_phase_compensation(original_signal, phase_error, params)
% 高级相位补偿

% 构建补偿因子
compensation_factor = exp(-1j * phase_error);

% 应用补偿
compensated_signal = original_signal .* compensation_factor;

% 可选：迭代优化
if params.adaptive.enable
    for iter = 1:2
        % 重新评估相位误差
        residual_error = estimate_residual_phase_error(compensated_signal);
        
        % 应用残差补偿
        if max(abs(residual_error)) > 0.1
            additional_compensation = exp(-1j * residual_error * 0.5);
            compensated_signal = compensated_signal .* additional_compensation;
        else
            break;
        end
    end
end

end

function residual_error = estimate_residual_phase_error(signal)
% 估计残留相位误差

% 简化的残差估计
analytic_signal = hilbert(signal);
inst_phase = unwrap(angle(analytic_signal));

% 计算相位的二阶导数作为误差指标
phase_accel = diff(diff(inst_phase));
residual_error = [0, cumsum(phase_accel), 0];

% 平滑处理
if length(residual_error) > 5
    residual_error = conv(residual_error, ones(1,3)/3, 'same');
end

end

function optimized_data = global_consistency_optimization(compensated_data, opt_params)
% 全局一致性优化

[N_range, N_azimuth] = size(compensated_data);
optimized_data = compensated_data;

for iter = 1:opt_params.iterations
    % 距离向平滑
    for r = 2:N_range-1
        smoothed = (optimized_data(r-1, :) + 2*optimized_data(r, :) + optimized_data(r+1, :)) / 4;
        optimized_data(r, :) = 0.8 * optimized_data(r, :) + 0.2 * smoothed;
    end
    
    % 方位向平滑
    for a = 2:N_azimuth-1
        smoothed = (optimized_data(:, a-1) + 2*optimized_data(:, a) + optimized_data(:, a+1)) / 4;
        optimized_data(:, a) = 0.8 * optimized_data(:, a) + 0.2 * smoothed;
    end
end

end

function image = improved_azimuth_processing(compensated_data, imaging_params)
% 改进的方位向处理，避免对称问题

[N_range, N_azimuth] = size(compensated_data);

% 应用方位向窗函数
if strcmp(imaging_params.window_azimuth, 'kaiser')
    window = kaiser(N_azimuth, imaging_params.beta)';
elseif strcmp(imaging_params.window_azimuth, 'hamming')
    window = hamming(N_azimuth)';
else
    window = ones(1, N_azimuth);
end

windowed_data = compensated_data .* repmat(window, N_range, 1);

% 改进的FFT处理（避免对称问题）
if imaging_params.remove_symmetry
    % 使用非对称FFT处理
    image = fft(windowed_data, [], 2);
    
    % 移除DC分量附近的对称性
    dc_range = max(1, floor(N_azimuth*0.02)):min(N_azimuth, ceil(N_azimuth*0.98));
    image = image(:, dc_range);
else
    image = fftshift(fft(windowed_data, [], 2), 2);
end

end

function processed_image = post_processing(image, post_params)
% 后处理

processed_image = image;

if strcmp(post_params.filter_type, 'median')
    % 中值滤波去噪
    magnitude = abs(processed_image);
    filtered_mag = medfilt2(magnitude, [3, 3]);
    processed_image = filtered_mag .* exp(1j * angle(processed_image));
end

% 其他后处理可以在这里添加

end

function entropy = calculate_signal_entropy(signal)
% 计算信号熵

magnitude = abs(signal);
magnitude = magnitude / sum(magnitude + eps);
entropy = -sum(magnitude .* log2(magnitude + eps));

end 