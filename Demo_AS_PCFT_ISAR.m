%% Demo_AS_PCFT_ISAR - 自适应稀疏-多阶多尺度 Chirp 变换 ISAR成像演示
%
% 本演示展示AS-PCFT算法的核心功能：
% 1. 多阶PCFT核函数 (0-4阶多项式相位)
% 2. 原子范数最小化精细聚焦
% 3. 稀疏-低秩耦合正则化
% 4. 与现有DCFT算法的性能对比

clear; close all; clc;

%% 设置演示参数
demo_config = struct();
demo_config.use_real_data = false;  % true: 使用真实数据, false: 使用仿真数据
demo_config.data_file = 'shipx2.mat'; % 真实数据文件名
demo_config.comparison_algorithms = {'FFT', 'DCFT', 'AS_PCFT'}; % 对比算法
demo_config.save_results = true;    % 是否保存结果
demo_config.display_plots = true;   % 是否显示图像

fprintf('=== AS-PCFT ISAR成像算法演示 ===\n\n');

%% 数据准备
if demo_config.use_real_data
    fprintf('加载真实雷达数据...\n');
    if exist(demo_config.data_file, 'file')
        load(demo_config.data_file);
        echo_data = hrrp; % 假设变量名为hrrp
        fprintf('数据维度: %d × %d\n', size(echo_data, 1), size(echo_data, 2));
    else
        error('数据文件 %s 不存在，请检查文件路径', demo_config.data_file);
    end
else
    fprintf('生成仿真雷达数据...\n');
    [echo_data, simulation_params] = generate_simulation_data();
    fprintf('仿真数据维度: %d × %d\n', size(echo_data, 1), size(echo_data, 2));
end

%% AS-PCFT算法参数设置
fprintf('\n配置AS-PCFT算法参数...\n');
as_pcft_params = get_optimized_as_pcft_params();

% 根据数据特性调整参数
[N_range, N_azimuth] = size(echo_data);
as_pcft_params.radar.PRF = 1000; % 根据实际情况调整
as_pcft_params.processing.block_size = min(50, N_range);

%% 算法性能对比
fprintf('\n开始算法性能对比...\n');
results = struct();

for i = 1:length(demo_config.comparison_algorithms)
    algorithm = demo_config.comparison_algorithms{i};
    fprintf('\n--- 运行 %s 算法 ---\n', algorithm);

    tic;
    switch algorithm
        case 'FFT'
            [ISAR_image, processing_info] = run_fft_baseline(echo_data);

        case 'DCFT'
            [ISAR_image, processing_info] = run_dcft_baseline(echo_data);

        case 'AS_PCFT'
            [ISAR_image, processing_info] = AS_PCFT_ISAR_Framework(echo_data, as_pcft_params);

        otherwise
            error('不支持的算法: %s', algorithm);
    end
    computation_time = toc;

    % 存储结果
    results.(algorithm).image = ISAR_image;
    results.(algorithm).processing_info = processing_info;
    results.(algorithm).computation_time = computation_time;

    % 计算图像质量指标
    quality_metrics = compute_image_quality_metrics(ISAR_image);
    results.(algorithm).quality_metrics = quality_metrics;

    fprintf('%s 算法完成，耗时: %.2f 秒\n', algorithm, computation_time);
    fprintf('图像质量指标 - 对比度: %.2f, 熵: %.2f, 聚焦度: %.2f\n', ...
        quality_metrics.contrast, quality_metrics.entropy, quality_metrics.focus);
end

%% 结果可视化
if demo_config.display_plots
    fprintf('\n生成对比图像...\n');
    create_comparison_plots(results, demo_config.comparison_algorithms);
end

%% 性能分析报告
fprintf('\n=== 性能分析报告 ===\n');
generate_performance_report(results, demo_config.comparison_algorithms);

%% 保存结果
if demo_config.save_results
    fprintf('\n保存结果到文件...\n');
    save_results_to_file(results, demo_config);
    fprintf('结果已保存到: AS_PCFT_ISAR_Demo_Results.mat\n');
end

fprintf('\n=== AS-PCFT ISAR演示完成 ===\n');

%% 辅助函数定义

function [echo_data, simulation_params] = generate_simulation_data()
% 生成仿真雷达数据

% 仿真参数
simulation_params = struct();
simulation_params.N_range = 128;      % 距离单元数
simulation_params.N_azimuth = 256;    % 方位单元数
simulation_params.PRF = 1000;         % 脉冲重复频率
simulation_params.num_targets = 5;    % 目标数量
simulation_params.SNR_dB = 20;        % 信噪比

N_r = simulation_params.N_range;
N_a = simulation_params.N_azimuth;

% 时间轴
tm = (0:N_a-1) / simulation_params.PRF;

% 初始化回波数据
echo_data = zeros(N_r, N_a, 'like', 1j);

% 生成多个散射点目标
for target_idx = 1:simulation_params.num_targets
    % 随机目标参数
    range_idx = randi([20, N_r-20]);
    amplitude = 1 + 0.5*randn;

    % 复杂运动参数 (包含高阶项)
    f0 = 0.1 * randn;                    % 多普勒频移
    alpha = 50 * randn;                  % 二阶项 (加速度)
    beta = 200 * randn;                  % 三阶项 (加加速度)
    gamma = 100 * randn;                 % 四阶项

    % 生成复杂运动信号
    phase_motion = 2*pi * (f0*tm + 0.5*alpha*tm.^2 + (1/6)*beta*tm.^3 + (1/24)*gamma*tm.^4);
    target_signal = amplitude * exp(1j * phase_motion);

    % 添加到回波数据
    echo_data(range_idx, :) = echo_data(range_idx, :) + target_signal;
end

% 添加噪声
noise_power = 10^(-simulation_params.SNR_dB/10);
noise = sqrt(noise_power/2) * (randn(N_r, N_a) + 1j*randn(N_r, N_a));
echo_data = echo_data + noise;
end

function [ISAR_image, processing_info] = run_fft_baseline(echo_data)
% FFT基线算法

% 简单的距离-多普勒处理
ISAR_image = fftshift(fft(echo_data, [], 2), 2);

processing_info = struct();
processing_info.algorithm = 'FFT';
processing_info.description = '传统FFT距离-多普勒处理';
end

function [ISAR_image, processing_info] = run_dcft_baseline(echo_data)
% DCFT基线算法 (简化版本)

[N_range, N_azimuth] = size(echo_data);
ISAR_image = zeros(N_range, N_azimuth, 'like', 1j);

% 时间轴
tm_norm = (0:N_azimuth-1) / N_azimuth;

% DCFT参数搜索范围
alpha_range = -50:10:50;
beta_range = -500:100:500;

% 逐距离单元处理
for r_idx = 1:N_range
    signal = echo_data(r_idx, :);

    % 跳过低能量单元
    if sum(abs(signal).^2) < 1e-8 * N_azimuth
        ISAR_image(r_idx, :) = fft(signal);
        continue;
    end

    best_energy = 0;
    best_spectrum = [];

    % 网格搜索
    for alpha = alpha_range
        for beta = beta_range
            % 相位补偿
            phase_comp = exp(-1j*2*pi*(0.5*alpha*tm_norm.^2 + (1/6)*beta*tm_norm.^3));

            % 解调和FFT
            signal_comp = signal .* phase_comp;
            spectrum = fft(signal_comp);

            % 能量评估
            current_energy = max(abs(spectrum).^2);
            if current_energy > best_energy
                best_energy = current_energy;
                best_spectrum = spectrum;
            end
        end
    end

    ISAR_image(r_idx, :) = best_spectrum;
end

processing_info = struct();
processing_info.algorithm = 'DCFT';
processing_info.description = '传统DCFT三阶多项式相位补偿';
end

function quality_metrics = compute_image_quality_metrics(ISAR_image)
% 计算图像质量指标

% 转换为幅度图像
image_magnitude = abs(ISAR_image);

% 对比度 (标准差与均值的比值)
image_mean = mean(image_magnitude(:));
image_std = std(image_magnitude(:));
contrast = image_std / (image_mean + eps);

% 熵
image_normalized = image_magnitude / sum(image_magnitude(:));
image_normalized(image_normalized == 0) = eps;
entropy = -sum(image_normalized(:) .* log2(image_normalized(:)));

% 聚焦度 (峰值与平均值的比值)
peak_value = max(image_magnitude(:));
focus = peak_value / (image_mean + eps);

% 旁瓣抑制比
sorted_values = sort(image_magnitude(:), 'descend');
main_lobe_power = mean(sorted_values(1:10));
side_lobe_power = mean(sorted_values(11:end));
sidelobe_ratio = 20 * log10(main_lobe_power / (side_lobe_power + eps));

quality_metrics = struct();
quality_metrics.contrast = contrast;
quality_metrics.entropy = entropy;
quality_metrics.focus = focus;
quality_metrics.sidelobe_ratio = sidelobe_ratio;
end

function create_comparison_plots(results, algorithms)
% 创建对比图像

num_algorithms = length(algorithms);
figure('Position', [100, 100, 1200, 400]);

for i = 1:num_algorithms
    algorithm = algorithms{i};

    subplot(1, num_algorithms, i);
    imagesc(20*log10(abs(results.(algorithm).image) + eps));
    colormap(gray);
    colorbar;
    title(sprintf('%s算法结果', algorithm));
    xlabel('多普勒单元');
    ylabel('距离单元');

    % 添加质量指标文本
    metrics = results.(algorithm).quality_metrics;
    text(10, 20, sprintf('对比度: %.2f\n熵: %.2f\n聚焦度: %.2f', ...
        metrics.contrast, metrics.entropy, metrics.focus), ...
        'Color', 'white', 'FontSize', 8, 'BackgroundColor', 'black');
end

sgtitle('AS-PCFT与传统算法性能对比');
end

function generate_performance_report(results, algorithms)
% 生成性能分析报告

fprintf('\n算法性能对比表:\n');
fprintf('%-10s %-10s %-10s %-10s %-10s %-10s\n', '算法', '计算时间(s)', '对比度', '熵', '聚焦度', '旁瓣抑制(dB)');
fprintf('%-10s %-10s %-10s %-10s %-10s %-10s\n', '----', '--------', '----', '--', '----', '----------');

for i = 1:length(algorithms)
    algorithm = algorithms{i};
    metrics = results.(algorithm).quality_metrics;
    time = results.(algorithm).computation_time;

    fprintf('%-10s %-10.2f %-10.2f %-10.2f %-10.2f %-10.2f\n', ...
        algorithm, time, metrics.contrast, metrics.entropy, metrics.focus, metrics.sidelobe_ratio);
end

% 性能提升分析
if length(algorithms) >= 2
    fprintf('\nAS-PCFT相对于传统算法的性能提升:\n');
    baseline_algorithm = algorithms{1}; % 通常是FFT
    as_pcft_algorithm = algorithms{end}; % AS-PCFT

    baseline_metrics = results.(baseline_algorithm).quality_metrics;
    as_pcft_metrics = results.(as_pcft_algorithm).quality_metrics;

    contrast_improvement = (as_pcft_metrics.contrast - baseline_metrics.contrast) / baseline_metrics.contrast * 100;
    focus_improvement = (as_pcft_metrics.focus - baseline_metrics.focus) / baseline_metrics.focus * 100;

    fprintf('对比度提升: %.1f%%\n', contrast_improvement);
    fprintf('聚焦度提升: %.1f%%\n', focus_improvement);
end
end

function save_results_to_file(results, demo_config)
% 保存结果到文件

save_data = struct();
save_data.results = results;
save_data.demo_config = demo_config;
save_data.timestamp = datestr(now);

save('AS_PCFT_ISAR_Demo_Results.mat', 'save_data');
end

function params = get_optimized_as_pcft_params()
% 获取优化的AS-PCFT参数

% 直接定义参数而不是调用不存在的函数
params = struct();

% 雷达参数
params.radar.PRF = 1000; % 脉冲重复频率

% 预处理参数
params.preprocessing.remove_dc = true;
params.preprocessing.range_alignment = false;
params.preprocessing.apply_window = true;

% 处理参数
params.processing.block_size = 50; % 分块大小
params.energy_threshold = 1e-8; % 能量阈值

% 粗聚焦参数
params.coarse.alpha_min = -50;
params.coarse.alpha_max = 50;
params.coarse.alpha_step = 5;
params.coarse.beta_min = -500;
params.coarse.beta_max = 500;
params.coarse.beta_step = 50;

% 原子范数参数
params.atomic.max_iter = 100;
params.atomic.tolerance = 1e-6;
params.atomic.refinement_factor = 10;

% 稀疏-低秩参数
params.sparse_lr.lambda_sparse = 0.1;
params.sparse_lr.lambda_lowrank = 0.05;
params.sparse_lr.max_iter = 50;
params.sparse_lr.tolerance = 1e-4;

% 后处理参数
params.postprocessing.contrast_enhancement = true;

% 针对演示优化的参数
params.coarse.alpha_step = 8;        % 粗搜索步长
params.coarse.beta_step = 80;        % 粗搜索步长
params.atomic.max_iter = 50;         % 原子范数迭代次数
params.sparse_lr.max_iter = 30;      % 稀疏-低秩迭代次数
params.processing.block_size = 25;   % 分块大小

% 正则化参数调整
params.sparse_lr.lambda_sparse = 0.05;
params.sparse_lr.lambda_lowrank = 0.02;
end
