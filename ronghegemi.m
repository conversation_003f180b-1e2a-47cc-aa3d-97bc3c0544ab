% integrated_vmd_dcft_admm_isar.m
% 主脚本，用于运行ISAR回波仿真和VMD-DCFT-ADMM深度融合算法
% 该算法实现了VMD模态分解、DCFT相位估计和ADMM稀疏重建的深度融合
% 不再是简单的串联关系，而是统一优化框架下的交互式迭代

clear; close all; clc;
load shipx2.mat;

fprintf('开始ISAR成像仿真与深度融合处理...\n');

% -------------------- 1. 生成仿真回波 -------------------- %
fprintf('正在加载/生成仿真回波数据...\n');
tic;
if ~exist('shipx2', 'var')
    [echo_data, sim_params] = generate_simulated_echo();
    fprintf('回波数据生成完毕。耗时: %.2f 秒\n', toc);
else
    echo_data = shipx2;
    fprintf('回波数据加载完毕。耗时: %.2f 秒\n', toc);
    % 如果没有sim_params，构造一个基本的
    if ~exist('sim_params', 'var')
        sim_params = struct();
        sim_params.Num_r = size(echo_data, 1);
        sim_params.Num_tm = size(echo_data, 2);
        sim_params.PRF = 1400;
        sim_params.fc = 5.2*1e9;
        sim_params.c = 3*1e8;
        sim_params.tm = (0 : (1/sim_params.PRF) : 0.501);
        sim_params.r_axis = linspace(-50, 50, sim_params.Num_r);
    end
end
fprintf('回波数据尺寸: %d (距离单元) x %d (方位单元)\n', size(echo_data, 1), size(echo_data, 2));

% -------------------- 2. 设置处理参数 -------------------- %
params_proc = struct();

% VMD 参数 - 优化以提高分辨率
params_proc.vmd.K = 4;             % 增加模态数量以提高分辨率
params_proc.vmd.alpha = 2500;      % 增加带宽约束强度以改善分辨率
params_proc.vmd.tau = 0.05;        % 降低拉格朗日乘子更新率，更好地处理噪声
params_proc.vmd.tol = 1e-6;        % 提高收敛精度
params_proc.vmd.max_iter = 150;    % 增加VMD迭代次数以确保收敛
params_proc.vmd.init_omega_method = 'peaks'; % 使用峰值初始化

% 相位估算 (优化版 "DCFT") 参数
params_proc.phase_est.poly_order = 3;       % 保持相位多项式阶数
params_proc.phase_est.fd_search_range_factor = 0.6; % 扩大搜索范围
params_proc.phase_est.ka_search_pts = 41;   % 增加搜索点数提高精度
params_proc.phase_est.kb_search_pts = 41;   % 增加搜索点数提高精度

% ADMM 参数
params_proc.admm.rho = 1.2;               % 增大增广拉格朗日参数以加速收敛
params_proc.admm.lambda_sparsity = 0.03;  % 降低稀疏正则化权重以保留更多细节
params_proc.admm.max_iter = 40;           % 保持足够迭代次数
params_proc.admm.tol = 1e-5;              % 提高收敛精度

% 深度融合参数
params_proc.integrated.max_global_iter = 3;   % 保持3次全局迭代，平衡效率和效果
params_proc.integrated.global_tol = 1e-4;     % 提高收敛精度
params_proc.integrated.phase_mode_weight = 0.4; % 增加模态与相位互约束权重
params_proc.integrated.image_feedback_weight = 0.3; % 增加图像反馈权重
params_proc.integrated.complex_penalty = true;
params_proc.integrated.adapt_params = true;
params_proc.integrated.fast_mode = true;       % 保持快速模式
params_proc.integrated.zero_dc_suppression = false; % 添加直流抑制以去除中央亮线

% 其他处理参数
params_proc.num_azimuth = sim_params.Num_tm; % 方位单元数
params_proc.num_range_bins = sim_params.Num_r; % 距离单元数
params_proc.PRF = sim_params.PRF;
params_proc.fc = sim_params.fc;
params_proc.c = sim_params.c;
params_proc.tm_azimuth = sim_params.tm; % 慢时间轴

% 加入快速模式标记和并行处理优化
if isfield(params_proc.integrated, 'fast_mode')
    params_proc.vmd.fast_mode = params_proc.integrated.fast_mode;
    params_proc.phase_est.fast_mode = params_proc.integrated.fast_mode;
    params_proc.admm.fast_mode = params_proc.integrated.fast_mode;
end

% 配置并行处理池
try
    % 获取可用的CPU核心数，留一个核心给系统使用
    num_cores = feature('numcores');
    if num_cores > 2
        parallel_pool = gcp('nocreate');
        if isempty(parallel_pool)
            parpool('local', num_cores-1);
        elseif parallel_pool.NumWorkers ~= num_cores-1
            delete(parallel_pool);
            parpool('local', num_cores-1);
        end
        fprintf('已配置并行处理池，使用 %d 个核心\n', num_cores-1);
    end
catch
    fprintf('无法配置并行处理池，使用单线程处理\n');
end

% -------------------- 3. 执行ISAR成像算法 -------------------- %
fprintf('开始执行VMD-DCFT-ADMM深度融合ISAR成像算法...\n');

% 保存一份原始数据用于对比
radar_data = echo_data;

% 执行传统串联算法
% tic;
% [ISAR_image_traditional, s_compensated_traditional] = perform_isar_imaging_va_dcft(radar_data, params_proc);
% fprintf('传统串联算法处理完毕。耗时: %.2f 秒\n', toc);

% 执行新的深度融合算法
tic;
[ISAR_image_integrated, s_compensated_integrated, processing_data] = perform_isar_imaging_integrated(radar_data, params_proc);
fprintf('深度融合算法处理完毕。耗时: %.2f 秒\n', toc);

% -------------------- 4. 显示回波数据和处理结果 -------------------- %
fprintf('显示回波数据和处理结果...\n');

% 显示距离压缩后的原始回波
figure('Name', '原始回波数据');
imagesc(sim_params.tm, sim_params.r_axis, abs(echo_data));
xlabel('慢时间 (秒)');
ylabel('距离 (米)');
title('距离压缩后的原始回波 (幅度)');
colorbar;
axis xy;
colormap jet;

% 准备频率轴
doppler_axis = linspace(-params_proc.PRF/2, params_proc.PRF/2, params_proc.num_azimuth);

% 图像归一化和dB转换
%ISAR_image_traditional = fftshift(ISAR_image_traditional, 2);
ISAR_image_integrated = fftshift(ISAR_image_integrated, 2);

%G_traditional = 20*log10(abs(ISAR_image_traditional)./max(abs(ISAR_image_traditional(:))));
G_integrated = 20*log10(abs(ISAR_image_integrated)./max(abs(ISAR_image_integrated(:))));
% 
% % 传统方法结果
% figure('Name', '传统串联算法结果');
% imagesc(doppler_axis, sim_params.r_axis, G_traditional);
% caxis([-40,0]);
% xlabel('多普勒频率 (Hz)');
% ylabel('距离 (米)');
% title('传统串联VMD-DCFT-ADMM成像结果');
% colorbar;
% axis xy;
% colormap jet;

% 深度融合方法结果
figure('Name', '深度融合算法结果');
imagesc(doppler_axis, sim_params.r_axis, G_integrated);
caxis([-40,0]);
xlabel('多普勒频率 (Hz)');
ylabel('距离 (米)');
title('深度融合VMD-DCFT-ADMM成像结果');
colorbar;
axis xy;
colormap jet;

% 直接FFT结果（无补偿）
Y_direct_fft = fftshift(fft(radar_data, [], 2), 2);
G_direct_fft = 20*log10(abs(Y_direct_fft)./max(abs(Y_direct_fft(:))));

figure('Name', '直接FFT结果');
imagesc(doppler_axis, sim_params.r_axis, G_direct_fft);
caxis([-30,0]);
xlabel('多普勒频率 (Hz)');
ylabel('距离 (米)');
title('直接FFT成像结果 (无相位补偿)');
colorbar;
axis xy;
colormap jet;

% -------------------- 5. 显示处理过程中的分析图 -------------------- %
fprintf('显示处理过程分析图...\n');

% 选择一个代表性的距离单元进行分析
[~, max_range_idx] = max(sum(abs(radar_data).^2, 2));
example_range_cell = min(max_range_idx, size(radar_data, 1)); % 确保索引不超出范围

% 显示原始信号、VMD分解、相位补偿过程
figure('Name', '信号分解与补偿过程');
subplot(3,1,1);
t_axis = sim_params.tm(1:min(length(sim_params.tm), size(radar_data, 2))); % 确保时间轴长度匹配
plot(t_axis, abs(radar_data(example_range_cell, 1:length(t_axis))));
hold on;
plot(t_axis, angle(radar_data(example_range_cell, 1:length(t_axis)))/pi, '--');
title(sprintf('距离单元 #%d 原始信号', example_range_cell));
legend('幅度', '相位/\pi');
xlabel('慢时间 (秒)');
grid on;

% 提取并显示VMD分解结果
subplot(3,1,2);
modes = processing_data.modes(example_range_cell,:,:);
for k = 1:params_proc.vmd.K
    plot(sim_params.tm, abs(squeeze(modes(1,k,:)))); hold on;
end
title('VMD模态分解结果 (幅度)');
legend_str = cell(1, params_proc.vmd.K);
for k = 1:params_proc.vmd.K
    legend_str{k} = sprintf('模态 #%d', k);
end
legend(legend_str);
xlabel('慢时间 (秒)');
grid on;

% 显示相位补偿结果
subplot(3,1,3);
signal_orig = radar_data(example_range_cell,:);
signal_comp = s_compensated_integrated(example_range_cell,:);
plot(sim_params.tm, abs(fft(signal_orig))); hold on;
plot(sim_params.tm, abs(fft(signal_comp)), '--');
title('补偿前后的频谱');
legend('补偿前', '补偿后');
xlabel('频率 (Hz)');
grid on;

% 显示相位估计结果
figure('Name', '相位估计与补偿');
subplot(2,1,1);
phases = processing_data.phases(example_range_cell,:,:);
for k = 1:params_proc.vmd.K
    plot(sim_params.tm, unwrap(squeeze(phases(1,k,:)))/pi); hold on;
end
title('估计的相位多项式');
legend(legend_str);
xlabel('慢时间 (秒)');
ylabel('相位/\pi');
grid on;

subplot(2,1,2);
plot(sim_params.tm, angle(signal_orig)/pi); hold on;
plot(sim_params.tm, angle(signal_comp)/pi, '--');
title('补偿前后的相位');
legend('补偿前', '补偿后');
xlabel('慢时间 (秒)');
ylabel('相位/\pi');
grid on;

% 显示ADMM迭代过程
figure('Name', 'ADMM迭代收敛过程');
plot(processing_data.admm_residuals{example_range_cell});
title('ADMM残差变化');
xlabel('迭代次数');
ylabel('残差');
grid on;

% 显示全局迭代过程
figure('Name', '全局迭代收敛过程');
plot(processing_data.global_residuals);
title('全局优化残差');
xlabel('迭代次数');
ylabel('残差');
grid on;

% 计算并显示图像质量指标
contrast_traditional = calculate_image_contrast(abs(ISAR_image_traditional));
entropy_traditional = calculate_image_entropy(abs(ISAR_image_traditional));
contrast_integrated = calculate_image_contrast(abs(ISAR_image_integrated));
entropy_integrated = calculate_image_entropy(abs(ISAR_image_integrated));

fprintf('图像质量对比:\n');
fprintf(' - 传统方法: 对比度: %.4f, 熵: %.4f\n', contrast_traditional, entropy_traditional);
fprintf(' - 深度融合: 对比度: %.4f, 熵: %.4f\n', contrast_integrated, entropy_integrated);
fprintf(' - 对比度提升: %.2f%%\n', 100*(contrast_integrated-contrast_traditional)/contrast_traditional);
fprintf(' - 熵降低: %.2f%%\n', 100*(entropy_traditional-entropy_integrated)/entropy_traditional);

fprintf('处理完成。\n');



% generate_simulated_echo.m
% 生成ISAR仿真回波数据

function [s_r_tm2, sim_params] = generate_simulated_echo()

% 目标散射点模型 (来自用户)
Pos = [-10 -1 0;-9 -1 0;-8 -1 0;-7 -1 0;-6 -1 0;-5 -1 0;-3.75 -1 0;-3 -1 0;-2 -1 0;-1 -1 0;...
       0 -1 0;...
       1 -1 0;2 -1 0;3 -1 0;4 -1 0;5 -1 0;6 -1 0;7 -1 0;8 -1 0;9 -1 0;10 -1 0;...
       -9.5 0.2 0.5;...
       -9.5 1.2 0.5;-9 1 0;-8 1 0;-7 1 0;-6 1 0;-5.2 1.2 0;-4.1 1 0;-3 1 0;-2 1 0;-1 1 0;...
       0 1 0;...
       1 1 0;2 1 0;3 1 0;4 1 0;5 1 0;6 1 0;7 1 0;8 1 0;9 1 0;10 1 0;...
       10.5 -0.75 0;10.5 0.75 0;11 -0.5 0;11 0.5 0;11.5 0 0;... % 尾部
       9.5 0.5 0.5;9.5 -0.5 0.5;9 0 0.5;8.5 0.5 0.5;8.5 -0.5 0.5;... % 头部
       5 0 0.5;5 0 1;5 0 1.5;5 0 2;5 0 2.5;5 0 3;5 0 3.5;5 0 4;...   % 机头
       5.5 0.5 0.5;5.5 -0.5 0.5;4.5 0.5 0.5;4.5 -0.5 0.5;... % 机头运动
       0.5 0.5 0.9;0.5 -0.5 0.9;-0.5 0.5 0.9;-0.5 -0.5 0.9;0 0 0.5;... % 中间运动
       -5 0 0.2;-5 0.2 0.8;-5 0.2 1.4;-5 0 2;... % 机尾部
       -5.5 -0.5 0.5;-5.5 0.5 0.5;-4.4 0.5 0.5;-4.5 -0.6 0.5;...% 机尾运动
       ];

% 坐标变换和显示 (来自用户，稍作调整用于理解)
x_Pos_orig = Pos(:,1)*5;
y_Pos_orig = Pos(:,2)*5;
z_Pos_orig = Pos(:,3)*5;

% 用户原始代码中的坐标平移：
% min_x_Pos = min(x_Pos_orig); x_Pos = x_Pos_orig + min_x_Pos;
% min_y_Pos = min(y_Pos_orig); y_Pos = y_Pos_orig + min_y_Pos;
% min_z_Pos = min(z_Pos_orig); z_Pos = z_Pos_orig + min_z_Pos;
% 通常的归一化是减去最小值使最小为0，例如 x_Pos = x_Pos_orig - min(x_Pos_orig);
% 这里遵循用户代码的逻辑，尽管它可能不寻常。
x_Pos = x_Pos_orig; % 使用原始坐标或按需调整
y_Pos = y_Pos_orig;
z_Pos = z_Pos_orig;
% 若要使所有坐标非负且从0开始，可以取消注释以下行：
% x_Pos = x_Pos - min(x_Pos);
% y_Pos = y_Pos - min(y_Pos);
% z_Pos = z_Pos - min(z_Pos);


% figure; plot3(x_Pos,y_Pos,z_Pos,'*'); grid on;
% xlabel('X (m)'); ylabel('Y (m)'); zlabel('Z (m)'); title('目标散射点模型 (仿真用)');

R_los = [cos(3*pi/8)*cos(0), cos(3*pi/8)*sin(0), sin(3*pi/8)]; % 雷达目标视线单位矢量
Num_point = size(x_Pos, 1); % 目标点数

% 投影到垂直于视线的平面，用于计算旋转引起的径向速度等
x_r_proj = zeros(1,Num_point);
y_r_proj = zeros(1,Num_point);
z_r_proj = zeros(1,Num_point);
for n_point = 1:Num_point
    x_r_proj(n_point) = y_Pos(n_point)*R_los(3) - z_Pos(n_point)*R_los(2);
    y_r_proj(n_point) = z_Pos(n_point)*R_los(1) - x_Pos(n_point)*R_los(3);
    z_r_proj(n_point) = x_Pos(n_point)*R_los(2) - y_Pos(n_point)*R_los(1);
end

% 目标旋转参数 (来自用户)
x_omega = 0.05; % 目标旋转初始角速度 (rad/s)
y_omega = 0.2;
z_omega = 0.05;
x_alpha_rot = 0.05; % 目标旋转角加速度 (rad/s^2)
y_alpha_rot = 0.1;
z_alpha_rot = 0.05;
x_beta_rot = 0.05; % 目标旋转角加加速度 (rad/s^3)
y_beta_rot = 0.4;
z_beta_rot = 0.05;

% 每个散射点的径向运动系数 (f_v, alpha_v, beta_v -> 对应距离变化中的 t, t^2, t^3 系数)
f_v_coeffs = zeros(1,Num_point);      % 速度项系数 (m/s)
alpha_v_coeffs = zeros(1,Num_point);  % 加速度项系数 (m/s^2)
beta_v_coeffs = zeros(1,Num_point);   % 加加速度项系数 (m/s^3)

for n_point = 1:Num_point
    f_v_coeffs(n_point) = x_r_proj(n_point)*x_omega + y_r_proj(n_point)*y_omega + z_r_proj(n_point)*z_omega;
    alpha_v_coeffs(n_point) = x_r_proj(n_point)*x_alpha_rot + y_r_proj(n_point)*y_alpha_rot + z_r_proj(n_point)*z_alpha_rot;
    beta_v_coeffs(n_point) = x_r_proj(n_point)*x_beta_rot + y_r_proj(n_point)*y_beta_rot + z_r_proj(n_point)*z_beta_rot;
end

% 雷达系统参数与时域变量 (来自用户)
sim_params.B = 80*1e6;     % 带宽 (Hz)
sim_params.c = 3*1e8;      % 光速 (m/s)
sim_params.PRF = 1400;     % 脉冲重复频率 (Hz)
sim_params.fc = 5.2*1e9;   % 载频 (Hz)
delta_r_res = sim_params.c / (2*sim_params.B); % 距离分辨率
sim_params.r_axis = (-50*delta_r_res : delta_r_res : 50*delta_r_res); % 距离单元轴
sim_params.tm = (0 : (1/sim_params.PRF) : 0.501); % 慢时间轴

sim_params.Num_r = length(sim_params.r_axis);
sim_params.Num_tm = length(sim_params.tm);
ones_r_vec = ones(1, sim_params.Num_r);
ones_tm_vec = ones(1, sim_params.Num_tm);

s_r_tm2 = zeros(sim_params.Num_r, sim_params.Num_tm); % 初始化回波矩阵

Delta_R0_init = zeros(1,Num_point); % 初始时刻的距离值

fprintf('  逐散射点生成回波...\n');
for n_point = 1:Num_point
    % 初始径向距离
    Delta_R0_init(n_point) = x_Pos(n_point)*R_los(1) + y_Pos(n_point)*R_los(2) + z_Pos(n_point)*R_los(3);

    % 瞬时径向距离 (修正用户代码中的多项式构造)
    % R(t) = R0 + v*t + 0.5*a*t^2 + (1/6)*j*t^3
    Delta_R_t = Delta_R0_init(n_point) + ...
                  f_v_coeffs(n_point) .* sim_params.tm + ...
                  (1/2) * alpha_v_coeffs(n_point) .* sim_params.tm.^2 + ...
                  (1/6) * beta_v_coeffs(n_point) .* sim_params.tm.^3;

    % 相位项: phi(t) = (4*pi*fc/c) * R(t)
    phase_term = (4*pi*sim_params.fc/sim_params.c) * Delta_R_t;

    % 幅度因子 (来自用户第一个代码块的逻辑)
    amplitude_factor = 1.0;
    if n_point > 53 && n_point < 62 % 特定点增强
        amplitude_factor = 1.3;
    end
    % 用户代码中对点48的特殊处理: amplitude_factor = amplitude_factor * 1;
    % 这行在 amplitude_factor=1 时无效果。如果点48有特殊幅度，应明确指定。
    % 这里假设点48若不在53-62范围内，则为1.0。

    % 生成单个散射点的回波并累加
    % sinc((2*B/c)*(r - R(t)))
    s_r_tm2 = s_r_tm2 + amplitude_factor * ...
              sinc((2*sim_params.B/sim_params.c) * (sim_params.r_axis.' * ones_tm_vec - ones_r_vec.' * Delta_R_t)) .* ...
              exp(1j * ones_r_vec.' * phase_term);
    if mod(n_point, 20) == 0
        fprintf('    已处理 %d / %d 个散射点\n', n_point, Num_point);
    end
end
fprintf('  所有散射点回波生成完毕。\n');

end

% sinc 函数定义 (MATLAB的sinc是 sin(pi*x)/(pi*x))
% function y = sinc(x)
%     y = ones(size(x));
%     idx = x~=0;
%     y(idx) = sin(pi*x(idx))./(pi*x(idx));
% end


% perform_isar_imaging_va_dcft.m
% 实现VMD-ADMM-OptimizedDCFT ISAR成像算法

% perform_isar_imaging_va_dcft.m
% 实现VMD-ADMM-OptimizedDCFT ISAR成像算法

% perform_isar_imaging_va_dcft.m
% 实现VMD-ADMM-OptimizedDCFT ISAR成像算法

function [ISAR_image_sparse, s_compensated_dominant_mode] = perform_isar_imaging_va_dcft(radar_data, params_proc)

% 1. 参数初始化
[num_range_bins, num_azimuth] = size(radar_data);
fprintf('  处理数据尺寸: %d x %d\n', num_range_bins, num_azimuth);

% 从params_proc中提取参数
K_vmd = params_proc.vmd.K;
tm_normalized = (0:num_azimuth-1) / num_azimuth; % VMD和相位估计内部使用的归一化时间

% 初始化输出
ISAR_image_sparse = zeros(num_range_bins, num_azimuth, 'like', 1j*radar_data(1));
s_compensated_dominant_mode = zeros(size(radar_data), 'like', radar_data); % 用于可选的非稀疏对比

% 归一化输入数据 (逐距离单元处理时内部进行，或全局进行)
% radar_data = radar_data / max(abs(radar_data(:))); % 可选的全局归一化

% 2. 主循环: 处理每个距离单元
fprintf('  开始逐距离单元处理 (共 %d 个)...\n', num_range_bins);
for r_idx = 1:num_range_bins % 将parfor改为for循环
    if mod(r_idx, round(num_range_bins/10)) == 0 && r_idx > 1
        fprintf('    正在处理距离单元: %d/%d\n', r_idx, num_range_bins);
    end

    signal_orig = radar_data(r_idx, :); % 当前距离单元的信号

    % 跳过能量过低的距离单元
    if sum(abs(signal_orig).^2) < 1e-12 % 能量阈值
        ISAR_image_sparse(r_idx, :) = fft(signal_orig); % 或置零
        s_compensated_dominant_mode(r_idx, :) = signal_orig;
        continue;
    end

    % 对当前信号进行归一化，避免数值问题
    signal_norm_factor = max(abs(signal_orig));
    if signal_norm_factor == 0, signal_norm_factor = 1; end
    signal = signal_orig / signal_norm_factor;

    % 初始化VMD模态和中心频率
    u_k = zeros(K_vmd, num_azimuth, 'like', 1j*signal(1));
    omega_k_vmd = zeros(K_vmd, 1); 
    % VMD初始化可以更智能，例如基于FFT峰值
    if strcmp(params_proc.vmd.init_omega_method, 'peaks')
        [~, locs] = findpeaks(abs(fft(signal)), 'SortStr', 'descend', 'NPeaks', K_vmd);
        if ~isempty(locs)
            omega_k_vmd(1:length(locs)) = (locs-1)/num_azimuth;
        end
        % 补齐剩余的 omega_k
        if length(locs) < K_vmd
            remaining_indices = (length(locs)+1):K_vmd;
            omega_k_vmd(remaining_indices) = linspace(0.1, 0.4, length(remaining_indices))'; % 均匀分布
        end
    else % linear
         for k_idx = 1:K_vmd
            omega_k_vmd(k_idx) = (k_idx-1)/(K_vmd); % 简单线性初始化
         end
    end


    % 初始化相位多项式系数 [fd, ka, kb] (对应相位中的 t, t^2, t^3)
    % 这些系数是针对归一化时间 tm_normalized 的
    poly_coeffs_k = zeros(K_vmd, params_proc.phase_est.poly_order); 

    % 初始化ADMM变量 (对于当前距离单元)
    X_admm = fft(signal); % 初始稀疏重建结果 (频谱)
    Z_admm = X_admm;      % 辅助变量
    Lambda_admm_dual = zeros(size(X_admm), 'like', 1j*signal(1)); % 拉格朗日乘子 (对偶变量)

    phi_k_phase_model = zeros(K_vmd, num_azimuth); % 存储每个模态的相位模型 phi(t)

    % 3. 联合迭代优化 (VMD <-> 相位估计)
    for global_iter = 1:params_proc.integrated.max_global_iter
        signal_sum_prev_iter = sum(u_k, 1); % 用于收敛检查

        % 3.1 VMD引导的信号分解
        % 'phi_k_phase_model' 是上一轮迭代得到的相位模型，用于引导VMD
        [u_k, omega_k_vmd, ~] = vmd_decompose(signal, params_proc.vmd, u_k, omega_k_vmd, phi_k_phase_model, params_proc.integrated.phase_mode_weight);

        % 3.2 优化后的相位参数估计 (逐模态)
        for k_idx = 1:K_vmd
            if sum(abs(u_k(k_idx,:))) < 1e-6 % 跳过低能量模态
                poly_coeffs_k(k_idx,:) = 0;
                phi_k_phase_model(k_idx,:) = 0;
                continue;
            end
            % 'poly_coeffs_k(k_idx,:)' 是该模态上一轮的系数，用于初始化当前估计
            [estimated_coeffs, estimated_phase] = estimate_phase_polynomial(u_k(k_idx,:), ...
                                                              poly_coeffs_k(k_idx,:), ...
                                                              params_proc.phase_est, ...
                                                              tm_normalized, params_proc.PRF);
            poly_coeffs_k(k_idx,:) = estimated_coeffs;
            phi_k_phase_model(k_idx,:) = estimated_phase;
        end

        % 3.3 全局收敛检查
        signal_sum_current_iter = sum(u_k, 1);
        if global_iter > 1
            change = norm(signal_sum_current_iter - signal_sum_prev_iter) / (norm(signal_sum_prev_iter) + eps);
            if change < params_proc.integrated.global_tol
                % fprintf('  距离单元 %d: 全局迭代在第 %d 次收敛。\n', r_idx, global_iter);
                break;
            end
        end
    end % end global iteration

    % 4. 基于最终的u_k和phi_k_phase_model进行ADMM稀疏重建
    %   首先构造补偿并叠加后的信号 Y
    Y_compensated_sum = zeros(1, num_azimuth, 'like', 1j*signal(1));
    for k_idx = 1:K_vmd
        Y_compensated_sum = Y_compensated_sum + u_k(k_idx,:) .* exp(-1j * phi_k_phase_model(k_idx,:));
    end
    Y_fft = fft(Y_compensated_sum);

    %   执行ADMM
    [X_reconstructed_spectrum, ~, ~] = admm_reconstruct(Y_fft, params_proc.admm, X_admm, Z_admm, Lambda_admm_dual);

    % 存储当前距离单元的稀疏频谱 (未移位)
    ISAR_image_sparse(r_idx, :) = X_reconstructed_spectrum * signal_norm_factor; % 恢复幅度

    % (可选) 保存用主导模态相位补偿的原始信号，用于对比
    mode_energies = sum(abs(u_k).^2, 2);
    [~, dominant_idx] = max(mode_energies);
    dominant_phase_compensation = phi_k_phase_model(dominant_idx, :);
    s_compensated_dominant_mode(r_idx, :) = signal_orig .* exp(-1j * dominant_phase_compensation);

end % end parfor r_idx

fprintf('  所有距离单元处理完毕。\n');

end

% vmd_decompose.m
% 变分模态分解 (Variational Mode Decomposition)
% 基于K. Dragomiretskiy, D. Zosso, "Variational Mode Decomposition", IEEE Trans. Sig. Proc., 2014.
% 可选地加入相位引导

function [u, omega_modes, lambda_dual] = vmd_decompose(signal, params_vmd, u_init, omega_init, guidance_phi_k, alpha_guidance)
% 输入:
%   signal       - 输入信号 (1D)
%   params_vmd   - VMD参数结构体: alpha, tau, K, tol, max_iter
%   u_init       - 初始模态 (K x N)
%   omega_init   - 初始中心频率 (K x 1)
%   guidance_phi_k - (可选) 引导相位模型 (K x N), 每个模态的 phi(t)
%   alpha_guidance - (可选) 引导项的权重
% 输出:
%   u            - 分解后的模态 (K x N)
%   omega_modes  - 各模态的中心频率 (K x 1)
%   lambda_dual  - 拉格朗日对偶变量

% 参数提取
alpha = params_vmd.alpha; % 带宽限制参数
tau = params_vmd.tau;     % 对偶上升步长 (噪声容忍度, 0表示无噪声)
K = params_vmd.K;         % 模态数量
tol = params_vmd.tol;     % 收敛容限
max_iter = params_vmd.max_iter;

if nargin < 5
    guidance_phi_k = [];
    alpha_guidance = 0;
end
if isempty(guidance_phi_k)
    alpha_guidance = 0;
end


N = length(signal);
signal_fft = fft(signal);
f_axis_normalized = (0:N-1)/N; % 归一化频率轴

% 初始化模态和频率
u = u_init;
omega_modes = omega_init;
u_fft = zeros(K, N, 'like', 1j*signal(1));
for k_idx = 1:K
    u_fft(k_idx,:) = fft(u(k_idx,:));
end

% 初始化拉格朗日乘子
lambda_dual = zeros(1, N, 'like', 1j*signal(1)); 

% VMD 主迭代
for iter = 1:max_iter
    u_prev_sum_fft = sum(u_fft, 1); % 用于计算残差

    for k_idx = 1:K
        % 计算除当前模态外的所有模态之和的fft
        sum_other_modes_fft = u_prev_sum_fft - u_fft(k_idx,:);

        % 在频域更新 u_k
        % 分子项: signal_fft - sum_other_modes_fft + lambda_dual_fft/2
        numerator_fft = signal_fft - sum_other_modes_fft + lambda_dual/2; % lambda_dual is already in freq domain if tau=0, otherwise it's time domain
                                                                       % Assuming lambda_dual is updated in time domain as per original VMD
                                                                       % So, fft(lambda_dual)/2 if lambda_dual is time domain.
                                                                       % The original paper's ADMM updates lambda in time domain.
                                                                       % If tau > 0, lambda is updated. If tau = 0, lambda is not updated from init.
                                                                       % Let's assume lambda_dual is time-domain lambda_hat in paper.

        if tau > 0 % lambda is updated and is time-domain
            numerator_fft = signal_fft - sum_other_modes_fft + fft(lambda_dual)/2;
        else % lambda is fixed at 0 (or some init), effectively no lambda term if init at 0.
             % If lambda_dual is init as 0, this term vanishes.
            numerator_fft = signal_fft - sum_other_modes_fft + lambda_dual/2; % if lambda_dual is freq-domain Lagrange multiplier
        end


        % 分母项: 1 + 2*alpha*(omega - omega_k)^2
        denominator = 1 + 2*alpha*(f_axis_normalized - omega_modes(k_idx)).^2;

        % 加入引导相位 (如果提供)
        if alpha_guidance > 0 && ~isempty(guidance_phi_k) && size(guidance_phi_k,1) >= k_idx && any(guidance_phi_k(k_idx,:))
            phase_prior_term_fft = fft(exp(1j * guidance_phi_k(k_idx,:))); % 构造相位先验的频谱

            % 加权更新分子和分母
            % This is a heuristic way to incorporate phase prior.
            % Original VMD does not have this. A more rigorous way might involve modifying the objective.
            % One interpretation: add a term to objective || u_k - A*exp(j*phi_k) ||^2
            % In frequency domain, this would be || U_k - FFT(A*exp(j*phi_k)) ||^2
            % The update for U_k would be ( ... + weight * FFT(A*exp(j*phi_k)) ) / ( ... + weight )

            numerator_fft = numerator_fft + alpha_guidance * phase_prior_term_fft;
            denominator = denominator + alpha_guidance;
        end

        u_fft(k_idx,:) = numerator_fft ./ denominator;
        u(k_idx,:) = ifft(u_fft(k_idx,:)); % 更新时域模态

        % 更新中心频率 omega_k (重心法)
        power_spectrum_uk = abs(u_fft(k_idx,:)).^2;
        if sum(power_spectrum_uk) > 1e-10
            omega_modes(k_idx) = sum(f_axis_normalized .* power_spectrum_uk) / sum(power_spectrum_uk);
        else
            % 若模态能量过低，保持不变或重新初始化
            % omega_modes(k_idx) = omega_modes(k_idx); 
        end
    end

    % 更新拉格朗日乘子 lambda_dual (时域)
    if tau > 0 % Only update if tau is non-zero (meaning there's a quadratic penalty for reconstruction error)
        current_sum_u = sum(u, 1);
        lambda_dual = lambda_dual + tau * (signal - current_sum_u);
    end

    % 检查收敛性 (基于模态总和的变化)
    if iter > 1
        u_sum_change = norm(sum(u_fft,1) - u_prev_sum_fft_iter_start) / norm(u_prev_sum_fft_iter_start + eps);
        if u_sum_change < tol
            break;
        end
    end
    u_prev_sum_fft_iter_start = sum(u_fft,1); % Store for next iteration's convergence check

end % end VMD iteration
end % end function


% estimate_phase_polynomial.m
% 估计信号模态的多项式相位系数 (fd, ka, kb)
% 采用分阶段估计策略以提高效率

% estimate_phase_polynomial.m
% 估计信号模态的多项式相位系数 (fd, ka, kb)
% 采用分阶段估计策略，使用物理单位和实际时间轴

% estimate_phase_polynomial.m
% 估计信号模态的多项式相位系数 (fd, ka, kb)
% 采用分阶段估计策略以提高效率

function [poly_coeffs_estimated, phase_estimated] = estimate_phase_polynomial(signal_mode, initial_coeffs, params_phase_est, tm_normalized, PRF)
% 输入:
%   signal_mode       - 单个VMD模态 (1D)
%   initial_coeffs    - [fd0, ka0, kb0] 初始系数估计 (用于迭代优化或范围中心)
%   params_phase_est  - 相位估计参数: poly_order, fd_search_range_factor, etc.
%   tm_normalized     - 归一化慢时间轴 (0 to N-1)/N
%   PRF               - 脉冲重复频率 (用于fd的实际单位转换和搜索范围)
% 输出:
%   poly_coeffs_estimated - [fd_est, ka_est, kb_est] 估计的系数 (针对 tm_normalized)
%   phase_estimated       - 2*pi*(fd*t + 0.5*ka*t^2 + (1/6)*kb*t^3) 估计的相位

N = length(signal_mode);
poly_order = params_phase_est.poly_order;
poly_coeffs_estimated = zeros(1, poly_order);

% --- 1. 估计 fd (线性项系数, 多普勒中心) ---
%    方法: FFT峰值检测
signal_mode_fft = fft(signal_mode);
[~, idx_max_fft] = max(abs(signal_mode_fft));
fd_est_norm = (idx_max_fft - 1) / N; % 归一化频率 (0 to 1)
% 将fd_est_norm调整到 [-0.5, 0.5] 范围 (如果需要)
if fd_est_norm > 0.5 
    fd_est_norm = fd_est_norm - 1;
end
poly_coeffs_estimated(1) = fd_est_norm; % 这是针对归一化时间的fd

% --- 2. 估计 ka (二次项系数, 线性调频率) ---
if poly_order >= 2
    % 补偿fd
    signal_comp_fd = signal_mode .* exp(-1j * 2*pi * poly_coeffs_estimated(1) * tm_normalized);

    % 搜索ka: 最大化去啁啾后信号频谱的能量集中度 (例如，对比度或峭度)
    % ka_search_range_norm: ka的范围需要根据信号特性确定，这里用一个启发式范围
    % ka的物理意义: 频率变化率。PRF^2 * (ka_norm) 约等于实际的 chirp rate in Hz/s
    % 一个粗略的ka范围可以基于fd的变化或经验
    max_chirp_rate_hz_s = (PRF/2)^2; % 假设最大频率变化能达到 (PRF/2) over 0.5s 
    ka_norm_max_abs = max_chirp_rate_hz_s / PRF^2 * 0.5; % 对应于归一化时间单位的ka
                                                      % (d(fd_norm)/dt_norm) = ka_norm
    ka_search_values = linspace(-ka_norm_max_abs*2, ka_norm_max_abs*2, params_phase_est.ka_search_pts); % 示例范围

    sharpness_ka = zeros(size(ka_search_values));
    for i_ka = 1:length(ka_search_values)
        ka_current = ka_search_values(i_ka);
        dechirped_signal = signal_comp_fd .* exp(-1j * 2*pi * 0.5 * ka_current * tm_normalized.^2);
        spectrum_dechirped = abs(fft(dechirped_signal)).^2;
        sharpness_ka(i_ka) = sum(spectrum_dechirped.^2); % L4 norm as sharpness (or use contrast)
    end
    [~, idx_max_ka] = max(sharpness_ka);
    poly_coeffs_estimated(2) = ka_search_values(idx_max_ka);
else
    if poly_order >=2; poly_coeffs_estimated(2) = 0; end
end

% --- 3. 估计 kb (三次项系数) ---
if poly_order >= 3
    % 补偿fd和ka
    signal_comp_fd_ka = signal_mode .* exp(-1j * 2*pi * (poly_coeffs_estimated(1) * tm_normalized + ...
                                                       0.5 * poly_coeffs_estimated(2) * tm_normalized.^2));

    % 搜索kb: 类似于ka的搜索
    % kb的物理意义: 频率变化率的变化率。PRF^3 * (kb_norm) 约等于实际 Hz/s^2
    kb_norm_max_abs = (PRF/2)^3 / PRF^3 * 0.2; % 启发式范围
    kb_search_values = linspace(-kb_norm_max_abs*2, kb_norm_max_abs*2, params_phase_est.kb_search_pts); % 示例范围

    sharpness_kb = zeros(size(kb_search_values));
    for i_kb = 1:length(kb_search_values)
        kb_current = kb_search_values(i_kb);
        decubic_signal = signal_comp_fd_ka .* exp(-1j * 2*pi * (1/6) * kb_current * tm_normalized.^3);
        spectrum_decubic = abs(fft(decubic_signal)).^2;
        sharpness_kb(i_kb) = sum(spectrum_decubic.^2);
    end
    [~, idx_max_kb] = max(sharpness_kb);
    poly_coeffs_estimated(3) = kb_search_values(idx_max_kb);
else
    if poly_order >=3; poly_coeffs_estimated(3) = 0; end
end

% --- 构建最终的估计相位 ---
phase_estimated = zeros(size(tm_normalized));
if poly_order >= 1
    phase_estimated = phase_estimated + 2*pi * poly_coeffs_estimated(1) * tm_normalized;
end
if poly_order >= 2
    phase_estimated = phase_estimated + 2*pi * 0.5 * poly_coeffs_estimated(2) * tm_normalized.^2;
end
if poly_order >= 3
    phase_estimated = phase_estimated + 2*pi * (1/6) * poly_coeffs_estimated(3) * tm_normalized.^3;
end
% Higher orders can be added if poly_order allows

end



% admm_reconstruct.m
% 基于ADMM的稀疏重建 (L1范数最小化)

function [X_sparse, Z_sparse, Lambda_dual] = admm_reconstruct(Y_fft, params_admm, X_init, Z_init, Lambda_init)
% 输入:
%   Y_fft        - 观测到的频谱 (经VMD分解、相位补偿、叠加后FFT的结果)
%   params_admm  - ADMM参数: rho, lambda_sparsity, max_iter, tol
%   X_init       - X 的初始值
%   Z_init       - Z 的初始值
%   Lambda_init  - Lambda (对偶变量) 的初始值
% 输出:
%   X_sparse     - 重建的稀疏频谱
%   Z_sparse     - 辅助稀疏变量
%   Lambda_dual  - 更新后的对偶变量

% ADMM参数
rho = params_admm.rho;
lambda_s = params_admm.lambda_sparsity; % 稀疏项权重
max_iter = params_admm.max_iter;
tol = params_admm.tol;

% 初始化
X = X_init;
Z = Z_init;
Lambda_dual = Lambda_init; % 这是对偶变量 Y 在ADMM文献中常用

% ADMM迭代 (求解 min 0.5*||X - Y_fft||^2 + lambda_s*||Z||_1 s.t. X=Z)
% 这里的 formulation 和原代码的 alpha4, alpha5 有点不同，
% 原代码: (alpha4/2)*||X-Y_fft||^2 + alpha5*||Z||_1
% 如果 alpha4=1, lambda_s = alpha5
% X-update: (Y_fft + rho*Z - Lambda_dual) / (1 + rho) if alpha4=1

for iter = 1:max_iter
    X_prev = X;
    Z_prev = Z;

    % 更新X (数据保真度)
    % L_rho = 0.5*||X - Y_fft||^2 + Lambda_dual^T*(X-Z) + (rho/2)*||X-Z||^2
    % dL/dX = X - Y_fft + Lambda_dual + rho*(X-Z) = 0
    % X(1+rho) = Y_fft - Lambda_dual + rho*Z
    X = (Y_fft - Lambda_dual + rho * Z) / (1 + rho);

    % 更新Z (稀疏性)
    % L_rho = lambda_s*||Z||_1 + Lambda_dual^T*(X-Z) + (rho/2)*||X-Z||^2
    % argmin_Z lambda_s*||Z||_1 + (rho/2)*||Z - (X + Lambda_dual/rho)||^2
    Z = soft_threshold(X + Lambda_dual/rho, lambda_s/rho);

    % 更新Lambda_dual (拉格朗日乘子 / 对偶变量)
    Lambda_dual = Lambda_dual + rho * (X - Z);

    % 收敛检查
    primal_residual = norm(X - Z);
    dual_residual = rho * norm(Z - Z_prev);

    if primal_residual < tol && dual_residual < tol
        % fprintf('    ADMM 在第 %d 次迭代收敛。\n', iter);
        break;
    end
end

X_sparse = X;
Z_sparse = Z;

end

% 辅助函数: 软阈值
function y = soft_threshold(x, threshold_val)
    y = sign(x) .* max(abs(x) - threshold_val, 0);
end
% calculate_image_contrast.m
% 计算图像对比度

function contrast = calculate_image_contrast(image_abs)
% 输入:
%   image_abs - ISAR图像的幅度值

    if isempty(image_abs) || numel(image_abs) < 2
        contrast = 0;
        return;
    end
    mean_val = mean(image_abs(:));
    std_val = std(image_abs(:));

    if mean_val == 0
        contrast = 0; % 或者 NaN，取决于如何定义
    else
        contrast = std_val / mean_val;
    end
end

% calculate_image_entropy.m
% 计算图像熵

function entropy = calculate_image_entropy(image_abs)
% 输入:
%   image_abs - ISAR图像的幅度值

    if isempty(image_abs)
        entropy = NaN;
        return;
    end

    % 归一化图像能量，使其像概率分布
    image_power = image_abs(:).^2;
    sum_power = sum(image_power);

    if sum_power == 0
        entropy = 0; % 或者 NaN
        return;
    end

    normalized_power = image_power / sum_power;

    % 避免 log2(0)
    valid_indices = normalized_power > eps; % eps 是最小的正浮点数

    if ~any(valid_indices)
        entropy = 0;
        return;
    end

    entropy = -sum(normalized_power(valid_indices) .* log2(normalized_power(valid_indices)));
end
G1=20*log10(abs(ISAR_image_sparse)./max(abs(ISAR_image_sparse(:))));

figure('name','稀疏');
imagesc(G1);caxis([-40,0]);
grid on;axis xy;colorbar;%axis equal;
% axis([-0.6 0.6 y(select_row(1)) y(select_row(end))]);%set(gca,'xtick',[-0.2 0 0.2]);
xlabel('azimuth');ylabel('range (m)');colormap jet;


G2=20*log10(abs(ISAR_image_fft_dominant)./max(abs(ISAR_image_fft_dominant(:))));
figure('name','DCT成像结果');
imagesc(G2);caxis([-40,0]);
grid on;axis xy;colorbar;%axis equal;
% axis([-0.6 0.6 y(select_row(1)) y(select_row(end))]);%set(gca,'xtick',[-0.2 0 0.2]);
xlabel('azimuth');ylabel('range (m)');colormap jet;


Y=fft(radar_data); %直接FFT
G1=20*log10(abs(Y)./max(abs(Y(:))));
figure('name','DCT成像结果');
imagesc(G1);caxis([-30,0]);
grid on;axis xy;colorbar;%axis equal;
% axis([-0.6 0.6 y(select_row(1)) y(select_row(end))]);%set(gca,'xtick',[-0.2 0 0.2]);
xlabel('azimuth');ylabel('range (m)');colormap jet;





% perform_isar_imaging_integrated.m
% 实现VMD-DCFT-ADMM深度融合的ISAR成像算法

function [ISAR_image, s_compensated, processing_data] = perform_isar_imaging_integrated(radar_data, params_proc)
% 该函数实现了VMD-DCFT-ADMM的深度融合ISAR成像算法
% 不再是简单的串联关系，而是在统一优化框架下进行交互式迭代
%
% 输入:
%   radar_data   - 距离压缩后的雷达回波数据，大小为 [num_range_bins, num_azimuth]
%   params_proc  - 处理参数结构体
% 输出:
%   ISAR_image   - 处理后的ISAR图像
%   s_compensated - 相位补偿后的信号
%   processing_data - 处理过程中的中间结果，用于可视化和分析

% 1. 参数初始化
[num_range_bins, num_azimuth] = size(radar_data);
fprintf('  处理数据尺寸: %d x %d\n', num_range_bins, num_azimuth);

% 从params_proc中提取参数
K_vmd = params_proc.vmd.K;  % VMD模态数量
tm_normalized = (0:num_azimuth-1) / num_azimuth; % 归一化时间轴
max_global_iter = params_proc.integrated.max_global_iter;  % 全局迭代次数
global_tol = params_proc.integrated.global_tol;  % 全局收敛容限
fast_mode = params_proc.integrated.fast_mode;  % 快速模式

% 在快速模式下选择性处理有效距离单元
if fast_mode
    % 计算每个距离单元的能量
    energy_bins = sum(abs(radar_data).^2, 2);
    [sorted_energy, sorted_idx] = sort(energy_bins, 'descend');
    
    % 计算能量累积分布，选择包含总能量90%的距离单元（提高到90%以增加分辨率）
    cumulative_energy = cumsum(sorted_energy) / sum(sorted_energy);
    valid_bins_idx = sorted_idx(cumulative_energy <= 0.90);
    
    % 确保至少处理25%的距离单元（提高比例以改善成像质量）
    min_bins = ceil(0.25 * num_range_bins);
    if length(valid_bins_idx) < min_bins
        valid_bins_idx = sorted_idx(1:min_bins);
    end
    
    % 添加中心区域的距离单元，确保关键区域得到处理
    center_start = max(1, round(num_range_bins/2) - 15);
    center_end = min(num_range_bins, round(num_range_bins/2) + 15);
    center_bins = center_start:center_end;
    valid_bins_idx = unique([valid_bins_idx; center_bins']);
    
    fprintf('  快速模式：处理 %d / %d 个有效距离单元 (%.1f%%)\n', ...
            length(valid_bins_idx), num_range_bins, ...
            100*length(valid_bins_idx)/num_range_bins);
else
    % 处理所有距离单元
    valid_bins_idx = 1:num_range_bins;
end

% 初始化输出
ISAR_image = zeros(num_range_bins, num_azimuth, 'like', 1j*radar_data(1));
s_compensated = zeros(size(radar_data), 'like', radar_data);

% 初始化中间处理数据结构（用于返回分析结果）
processing_data = struct();
processing_data.modes = zeros(num_range_bins, K_vmd, num_azimuth, 'like', 1j*radar_data(1));
processing_data.phases = zeros(num_range_bins, K_vmd, num_azimuth);
processing_data.global_residuals = zeros(max_global_iter, 1);
processing_data.admm_residuals = cell(num_range_bins, 1);

% 创建临时数组来收集所有距离单元的结果
all_modes = zeros(num_range_bins, K_vmd, num_azimuth, 'like', 1j*radar_data(1));
all_phases = zeros(num_range_bins, K_vmd, num_azimuth);
all_admm_residuals = cell(num_range_bins, 1);
all_global_residuals = zeros(max_global_iter, 1); % 最终只保留第一个距离单元的全局残差

% 对低能量区域直接使用FFT处理
low_energy_bins = setdiff(1:num_range_bins, valid_bins_idx);
if ~isempty(low_energy_bins)
    fprintf('  对 %d 个低能量距离单元使用快速FFT处理...\n', length(low_energy_bins));
    for r_idx = low_energy_bins
        ISAR_image(r_idx, :) = fft(radar_data(r_idx, :));
        s_compensated(r_idx, :) = radar_data(r_idx, :);
    end
end

% 2. 主循环: 处理每个有效距离单元
fprintf('  开始逐距离单元处理 (共 %d 个有效单元)...\n', length(valid_bins_idx));

% 准备并行处理
% 创建临时存储数组
temp_ISAR_image = zeros(size(ISAR_image), 'like', ISAR_image);
temp_s_compensated = zeros(size(s_compensated), 'like', s_compensated);
temp_all_modes = zeros(size(all_modes), 'like', all_modes);
temp_all_phases = zeros(size(all_phases), 'like', all_phases);
temp_all_admm_residuals = cell(size(all_admm_residuals));

% 创建进度指示器
progress_increment = max(1, round(length(valid_bins_idx)/10));
progress_points = progress_increment:progress_increment:length(valid_bins_idx);
progress_shown = false(size(progress_points));

% 并行处理有效距离单元
for i = 1:length(valid_bins_idx)
    r_idx = valid_bins_idx(i);
    
    % 显示进度（仅主工作进程）
    for p = 1:length(progress_points)
        if i == progress_points(p) && ~progress_shown(p)
            fprintf('    正在处理距离单元: %d/%d (全局索引: %d)\n', i, length(valid_bins_idx), r_idx);
            progress_shown(p) = true;
            break;
        end
    end

    signal_orig = radar_data(r_idx, :); % 当前距离单元的信号

    % 跳过能量过低的距离单元
    if sum(abs(signal_orig).^2) < 1e-12 % 能量阈值
        ISAR_image(r_idx, :) = fft(signal_orig); % 或置零
        s_compensated(r_idx, :) = signal_orig;
        continue;
    end

    % 对当前信号进行归一化，避免数值问题
    signal_norm_factor = max(abs(signal_orig));
    if signal_norm_factor == 0, signal_norm_factor = 1; end
    signal = signal_orig / signal_norm_factor;

    % 初始化VMD模态和相位估计
    u_k = zeros(K_vmd, num_azimuth, 'like', 1j*signal(1));
    phi_k = zeros(K_vmd, num_azimuth);
    
    % 传递快速模式参数
    if fast_mode
        if ~isfield(params_proc.vmd, 'fast_mode')
            params_proc.vmd.fast_mode = true;
        end
        if ~isfield(params_proc.phase_est, 'fast_mode')
            params_proc.phase_est.fast_mode = true;
        end
        if ~isfield(params_proc.admm, 'fast_mode')
            params_proc.admm.fast_mode = true;
        end
    end
    
    % 初始化中心频率 (从FFT峰值)
    omega_k_vmd = initialize_center_frequencies(signal, K_vmd, params_proc.vmd.init_omega_method);
    
    % 初始化相位多项式系数 [fd, ka, kb]
    poly_coeffs_k = zeros(K_vmd, params_proc.phase_est.poly_order);
    
    % 初始化ADMM变量
    X_admm = fft(signal);  % 频域表示
    Z_admm = X_admm;       % 辅助变量
    Lambda_admm = zeros(size(X_admm), 'like', 1j*signal(1)); % 拉格朗日乘子
    
    % 初始化补偿信号变量
    s_comp = zeros(size(signal), 'like', signal);
    
    % 储存ADMM迭代残差和全局残差
    admm_residuals = zeros(params_proc.admm.max_iter, 1);
    local_global_residuals = zeros(max_global_iter, 1);
    
    % 3. 统一优化框架下的迭代过程
    signal_recon_prev = zeros(size(signal), 'like', signal);
    for iter = 1:max_global_iter
        % 3.1 联合VMD分解和相位估计
        [u_k, phi_k, poly_coeffs_k, omega_k_vmd] = joint_vmd_phase_estimation(...
            signal, u_k, phi_k, poly_coeffs_k, omega_k_vmd, X_admm, params_proc);
        
        % 3.2 构造相位补偿信号
        s_comp = zeros(size(signal), 'like', signal);
        for k = 1:K_vmd
            s_comp = s_comp + u_k(k,:) .* exp(-1j * phi_k(k,:));
        end
        
        % 3.3 ADMM稀疏重建（考虑模态结构和相位信息）
        [X_admm, Z_admm, Lambda_admm, admm_res] = integrated_admm_reconstruction(...
            s_comp, u_k, phi_k, X_admm, Z_admm, Lambda_admm, params_proc.admm);
        
        % % 应用零频抑制（如果启用）
        % if isfield(params_proc.integrated, 'zero_dc_suppression') && params_proc.integrated.zero_dc_suppression
        %     X_admm = apply_dc_suppression(X_admm);
        % end
        % 
        % 存储当前迭代的ADMM残差
        admm_residuals(1:length(admm_res)) = admm_res;
        
        % 3.4 从频域反向更新VMD模态
        if iter < max_global_iter
            X_time = ifft(X_admm);
            % 使用图像域信息反馈更新模态分解
            u_k = refine_modes_with_image_feedback(u_k, X_time, phi_k, params_proc.integrated.image_feedback_weight);
        end
        
        % 3.5 全局收敛检查
        signal_recon = ifft(X_admm);
        if iter > 1
            change = norm(signal_recon - signal_recon_prev) / (norm(signal_recon_prev) + eps);
            local_global_residuals(iter) = change;
            if change < global_tol
                break;
            end
        end
        signal_recon_prev = signal_recon;
    end
    
    % 4. 存储结果
    temp_ISAR_image(r_idx, :) = X_admm * signal_norm_factor; % 恢复幅度
    temp_s_compensated(r_idx, :) = s_comp * signal_norm_factor;
    
    % 存储中间处理结果（用于分析）到临时数组
    temp_all_modes(r_idx,:,:) = u_k;
    temp_all_phases(r_idx,:,:) = phi_k;
    temp_all_admm_residuals{r_idx} = admm_residuals;
    
    % 只存储第一个距离单元的全局残差
    if r_idx == valid_bins_idx(1)
        all_global_residuals = local_global_residuals;
    end
end

% 从临时数组提取结果
ISAR_image = temp_ISAR_image;
s_compensated = temp_s_compensated;

% 循环结束后，将临时数组复制到处理数据结构
processing_data.modes = temp_all_modes;
processing_data.phases = temp_all_phases;
processing_data.admm_residuals = temp_all_admm_residuals;
processing_data.global_residuals = all_global_residuals;

fprintf('  所有距离单元处理完毕。\n');
end

% initialize_center_frequencies 函数
% 基于信号特性初始化VMD中心频率
function omega_k = initialize_center_frequencies(signal, K, method)
    % 输入:
    %   signal - 输入信号
    %   K - 模态数量
    %   method - 初始化方法 ('peaks' 或 'linear')
    % 输出:
    %   omega_k - 中心频率 [0,1]

    num_azimuth = length(signal);
    omega_k = zeros(K, 1);
    
    if strcmp(method, 'peaks')
        % 基于FFT峰值初始化
        signal_fft = fft(signal);
        [~, locs] = findpeaks(abs(signal_fft), 'SortStr', 'descend', 'NPeaks', K);
        if ~isempty(locs)
            omega_k(1:length(locs)) = (locs-1)/num_azimuth;
        end
        % 补齐剩余的 omega_k
        if length(locs) < K
            remaining_indices = (length(locs)+1):K;
            omega_k(remaining_indices) = linspace(0.1, 0.4, length(remaining_indices))';
        end
    else % linear
        for k = 1:K
            omega_k(k) = (k-1)/(K); % 简单线性初始化
        end
    end
    
    % 确保频率在[0,1]范围内
    omega_k = min(max(omega_k, 0), 1);
end

% joint_vmd_phase_estimation 函数
% 联合VMD分解和相位估计（深度融合）
function [u_k, phi_k, poly_coeffs_k, omega_k] = joint_vmd_phase_estimation(signal, u_k_init, phi_k_init, poly_coeffs_init, omega_k_init, X_freq_feedback, params)
    % 输入:
    %   signal - 输入信号
    %   u_k_init - 初始模态
    %   phi_k_init - 初始相位
    %   poly_coeffs_init - 初始相位多项式系数
    %   omega_k_init - 初始中心频率
    %   X_freq_feedback - 频域反馈信息
    %   params - 参数结构体
    % 输出:
    %   u_k - 更新后的模态
    %   phi_k - 更新后的相位
    %   poly_coeffs_k - 更新后的相位多项式系数
    %   omega_k - 更新后的中心频率
    
    % 参数提取
    K = params.vmd.K; % 模态数量
    N = length(signal);
    alpha = params.vmd.alpha; % 带宽约束
    tau = params.vmd.tau; % 噪声容忍度
    tol = params.vmd.tol; % 收敛容限
    max_iter = params.vmd.max_iter; % 最大迭代次数
    phase_mode_weight = params.integrated.phase_mode_weight; % 模态与相位互约束权重
    
    % 快速模式检查
    fast_mode = false;
    if isfield(params.integrated, 'fast_mode')
        fast_mode = params.integrated.fast_mode;
    end
    
    % 在快速模式下减少迭代次数
    if fast_mode
        max_iter = max(30, round(max_iter * 0.5)); % 减少迭代次数
        tol = tol * 5; % 放宽收敛条件
    end
    
    % 准备归一化时间轴和频率轴
    tm_normalized = (0:N-1)/N;
    f_axis_normalized = (0:N-1)/N;
    
    % 初始化
    u_k = u_k_init;
    phi_k = phi_k_init;
    poly_coeffs_k = poly_coeffs_init;
    omega_k = omega_k_init;
    
    % 频域变量
    u_fft = zeros(K, N, 'like', 1j*signal(1));
    for k = 1:K
        u_fft(k,:) = fft(u_k(k,:));
    end
    
    % 拉格朗日乘子
    lambda_dual = zeros(1, N, 'like', 1j*signal(1));
    
    % 准备频域反馈信息
    if ~isempty(X_freq_feedback)
        signal_freq_feedback = ifft(X_freq_feedback);
    else
        signal_freq_feedback = [];
    end
    
    % 主迭代
    for iter = 1:max_iter
        % 更新模态和中心频率
        for k = 1:K
            % 计算除当前模态外的所有模态之和
            sum_other_modes = sum(u_k, 1) - u_k(k,:);
            
            % 构造余弦模态（考虑相位信息）
            residual = signal - sum_other_modes;
            
            % 加入相位先验（从上一次迭代得到的相位估计）
            if iter > 1 && phase_mode_weight > 0
                phase_prior = exp(1j * phi_k(k,:));
                residual_with_phase = (1-phase_mode_weight) * residual + phase_mode_weight * phase_prior;
            else
                residual_with_phase = residual;
            end
            
            % 加入频域反馈（如果有）
            if ~isempty(signal_freq_feedback) && params.integrated.image_feedback_weight > 0
                residual_with_phase = (1-params.integrated.image_feedback_weight) * residual_with_phase + params.integrated.image_feedback_weight * signal_freq_feedback;
            end
            
            % 频域更新
            residual_fft = fft(residual_with_phase) + lambda_dual/2;
            
            % 计算模态的频域表示
            denominator = 1 + 2*alpha*(f_axis_normalized - omega_k(k)).^2;
            u_fft(k,:) = residual_fft ./ denominator;
            
            % 更新时域模态
            u_k(k,:) = ifft(u_fft(k,:));
            
            % 更新中心频率（重心法）
            power_spectrum = abs(u_fft(k,:)).^2;
            if sum(power_spectrum) > 1e-10
                omega_k(k) = sum(f_axis_normalized .* power_spectrum) / sum(power_spectrum);
            end
        end
        
        % 更新拉格朗日乘子
        if tau > 0
            signal_sum = sum(u_k, 1);
            lambda_dual = lambda_dual + tau * (signal - signal_sum);
        end
        
        % 迭代更新相位估计
        for k = 1:K
            if sum(abs(u_k(k,:))) < 1e-6 % 跳过低能量模态
                continue;
            end
            
            % 联合估计相位（考虑模态结构）
            [estimated_coeffs, estimated_phase] = estimate_phase_with_mode_constraint(...
                u_k(k,:), poly_coeffs_k(k,:), params.phase_est, tm_normalized, params.PRF, omega_k(k));
            
            poly_coeffs_k(k,:) = estimated_coeffs;
            phi_k(k,:) = estimated_phase;
        end
        
        % 检查收敛性
        if iter > 1
            change = norm(sum(u_k,1) - sum_prev) / norm(sum_prev + eps);
            if change < tol
                break;
            end
        end
        sum_prev = sum(u_k,1);
    end
end

% estimate_phase_with_mode_constraint 函数
% 考虑模态结构约束的相位估计
function [poly_coeffs, phase] = estimate_phase_with_mode_constraint(signal_mode, initial_coeffs, params_phase, tm_normalized, PRF, omega_center)
    % 输入:
    %   signal_mode - 单个VMD模态
    %   initial_coeffs - 初始相位系数
    %   params_phase - 相位估计参数
    %   tm_normalized - 归一化时间轴
    %   PRF - 脉冲重复频率
    %   omega_center - 模态的中心频率
    % 输出:
    %   poly_coeffs - 估计的相位多项式系数
    %   phase - 估计的相位
    
    N = length(signal_mode);
    poly_order = params_phase.poly_order;
    poly_coeffs = zeros(1, poly_order);
    
    % 快速模式检查 - 如果上层有fast_mode参数，减少搜索点数
    fast_mode = false;
    if isfield(params_phase, 'fast_mode')
        fast_mode = params_phase.fast_mode;
    end
    
    % 根据模态能量自适应调整处理
    mode_energy = sum(abs(signal_mode).^2);
    if mode_energy < 1e-8 % 极低能量模态简化处理
        if ~isempty(omega_center)
            if omega_center > 0.5
                poly_coeffs(1) = omega_center - 1;
            else
                poly_coeffs(1) = omega_center;
            end
        end
        phase = 2*pi * poly_coeffs(1) * tm_normalized;
        return;
    end
    
    % 使用FFT峰值
    signal_mode_fft = fft(signal_mode);
    [~, idx_max_fft] = max(abs(signal_mode_fft));
    fd_est_norm = (idx_max_fft - 1) / N;
    if fd_est_norm > 0.5
        fd_est_norm = fd_est_norm - 1;
    end
    poly_coeffs(1) = fd_est_norm;
    
    % 补偿fd后估计ka（二次项）
    if poly_order >= 2
        signal_comp_fd = signal_mode .* exp(-1j * 2*pi * poly_coeffs(1) * tm_normalized);
        
        % 物理范围的ka搜索
        max_chirp_rate_hz_s = (PRF/2)^2;
        ka_norm_max_abs = max_chirp_rate_hz_s / PRF^2 * 0.5;
        
        % 考虑模态特性的ka搜索范围缩放
        ka_search_range_factor = 2.0;  
        if ~isempty(omega_center) && abs(omega_center - 0.5) < 0.1
            % 中心频率接近0.5的模态可能具有更大的频率变化率
            ka_search_range_factor = 3.0;
        end
        
        ka_search_values = linspace(-ka_norm_max_abs*ka_search_range_factor, ka_norm_max_abs*ka_search_range_factor, params_phase.ka_search_pts);
        
        % 优化搜索指标：使用相位连续性和频谱聚焦度的组合
        sharpness_ka = zeros(size(ka_search_values));
        for i_ka = 1:length(ka_search_values)
            ka_current = ka_search_values(i_ka);
            dechirped_signal = signal_comp_fd .* exp(-1j * 2*pi * 0.5 * ka_current * tm_normalized.^2);
            
            % 频谱聚焦度评价
            spectrum_dechirped = abs(fft(dechirped_signal)).^2;
            spectrum_focus = sum(spectrum_dechirped.^2);
            
            % 相位连续性评价
            phase_continuity = -std(diff(unwrap(angle(dechirped_signal))));
            
            % 组合指标
            sharpness_ka(i_ka) = spectrum_focus + 0.2 * phase_continuity;
        end
        
        [~, idx_max_ka] = max(sharpness_ka);
        poly_coeffs(2) = ka_search_values(idx_max_ka);
    else
        if poly_order >= 2
            poly_coeffs(2) = 0;
        end
    end
    
    % 补偿fd和ka后估计kb（三次项）
    if poly_order >= 3
        phase_term = poly_coeffs(1) * tm_normalized + 0.5 * poly_coeffs(2) * tm_normalized.^2;
        signal_comp_fd_ka = signal_mode .* exp(-1j * 2*pi * phase_term);
        
        % 物理范围的kb搜索
        kb_norm_max_abs = (PRF/2)^3 / PRF^3 * 0.2;
        
        % 考虑ka值自适应调整kb搜索范围
        kb_search_range_factor = 2.0;
        if abs(poly_coeffs(2)) > 0.1 * ka_norm_max_abs
            kb_search_range_factor = 3.0;
        end
        
        kb_search_values = linspace(-kb_norm_max_abs*kb_search_range_factor, kb_norm_max_abs*kb_search_range_factor, params_phase.kb_search_pts);
        
        sharpness_kb = zeros(size(kb_search_values));
        for i_kb = 1:length(kb_search_values)
            kb_current = kb_search_values(i_kb);
            decubic_signal = signal_comp_fd_ka .* exp(-1j * 2*pi * (1/6) * kb_current * tm_normalized.^3);
            
            % 频谱聚焦度评价
            spectrum_decubic = abs(fft(decubic_signal)).^2;
            spectrum_focus = sum(spectrum_decubic.^2);
            
            % 相位连续性评价
            phase_continuity = -std(diff(unwrap(angle(decubic_signal))));
            
            % 组合指标
            sharpness_kb(i_kb) = spectrum_focus + 0.2 * phase_continuity;
        end
        
        [~, idx_max_kb] = max(sharpness_kb);
        poly_coeffs(3) = kb_search_values(idx_max_kb);
    else
        if poly_order >= 3
            poly_coeffs(3) = 0;
        end
    end
    
    % 构建最终的估计相位
    phase = zeros(size(tm_normalized));
    if poly_order >= 1
        phase = phase + 2*pi * poly_coeffs(1) * tm_normalized;
    end
    if poly_order >= 2
        phase = phase + 2*pi * 0.5 * poly_coeffs(2) * tm_normalized.^2;
    end
    if poly_order >= 3
        phase = phase + 2*pi * (1/6) * poly_coeffs(3) * tm_normalized.^3;
    end
end

% integrated_admm_reconstruction 函数
% 改进的ADMM稀疏重建（考虑模态结构和相位信息）
function [X, Z, Lambda, residuals] = integrated_admm_reconstruction(signal, u_k, phi_k, X_init, Z_init, Lambda_init, params_admm)
    % 输入:
    %   signal - 相位补偿后的信号
    %   u_k - VMD模态
    %   phi_k - 相位估计
    %   X_init, Z_init, Lambda_init - ADMM初始变量
    %   params_admm - ADMM参数
    % 输出:
    %   X, Z, Lambda - 更新后的ADMM变量
    %   residuals - 迭代残差
    
    % 参数提取
    rho = params_admm.rho;
    lambda_s = params_admm.lambda_sparsity;
    max_iter = params_admm.max_iter;
    tol = params_admm.tol;
    
    % 快速模式检查
    fast_mode = false;
    if isfield(params_admm, 'fast_mode')
        fast_mode = params_admm.fast_mode;
    end
    
    % 在快速模式下减少迭代次数
    if fast_mode
        max_iter = max(15, round(max_iter * 0.6)); % 减少迭代次数
        tol = tol * 2; % 放宽收敛条件
    end
    
    % 计算信号的频谱
    Y_fft = fft(signal);
    
    % 初始化
    X = X_init;
    Z = Z_init;
    Lambda = Lambda_init;
    
    % 存储残差
    residuals = zeros(max_iter, 1);
    
    % 构建模态特性约束矩阵（用于指导稀疏重建）
    K = size(u_k, 1);
    N = length(signal);
    
    % 优化：仅计算主要模态的约束（节省计算）
    mode_constraint = zeros(size(Y_fft), 'like', 1j*Y_fft(1));
    
    % 计算模态能量
    mode_energies = sum(abs(u_k).^2, 2);
    [~, sorted_indices] = sort(mode_energies, 'descend');
    
    % 仅使用能量最大的模态构建约束（快速模式）
    if fast_mode && K > 1
        top_modes = sorted_indices(1:min(2, K));
    else
        top_modes = 1:K;
    end
    
    for k_idx = 1:length(top_modes)
        k = top_modes(k_idx);
        % 计算每个模态的补偿频谱
        mode_comp = u_k(k,:) .* exp(-1j * phi_k(k,:));
        mode_fft = fft(mode_comp);
        
        % 累加模态频谱（加权）
        mode_energy = mode_energies(k);
        total_energy = sum(mode_energies);
        
        if total_energy > 0
            mode_weight = mode_energy / total_energy;
            mode_constraint = mode_constraint + mode_weight * mode_fft;
        end
    end
    
    % ADMM迭代
    for iter = 1:max_iter
        X_prev = X;
        Z_prev = Z;
        
        % 更新X（数据保真度 + 模态约束）
        % 考虑模态结构的数据保真度项
        X = (Y_fft + 0.2*mode_constraint + rho*Z - Lambda) / (1 + 0.2 + rho);
        
        % 更新Z（稀疏性）
        % 使用复数域软阈值
        Z = complex_domain_soft_threshold(X + Lambda/rho, lambda_s/rho);
        
        % 更新Lambda
        Lambda = Lambda + rho * (X - Z);
        
        % 计算残差
        primal_residual = norm(X - Z);
        dual_residual = rho * norm(Z - Z_prev);
        residuals(iter) = primal_residual + dual_residual;
        
        % 收敛检查
        if primal_residual < tol && dual_residual < tol
            residuals = residuals(1:iter);
            break;
        end
    end
end

% complex_domain_soft_threshold 函数
% 复数域的软阈值操作
function Z = complex_domain_soft_threshold(X, threshold)
    % 输入:
    %   X - 复数域输入
    %   threshold - 阈值
    % 输出:
    %   Z - 阈值处理后的结果
    
    % 计算幅度和相位
    magnitude = abs(X);
    phase = angle(X);
    
    % 对幅度进行软阈值
    shrunk_magnitude = max(magnitude - threshold, 0);
    
    % 保持相位不变
    Z = shrunk_magnitude .* exp(1j * phase);
end

% refine_modes_with_image_feedback 函数
% 使用图像域信息反馈优化模态分解
function u_k_refined = refine_modes_with_image_feedback(u_k, X_time, phi_k, feedback_weight)
    % 输入:
    %   u_k - 当前模态
    %   X_time - 时域图像信息
    %   phi_k - 当前相位估计
    %   feedback_weight - 反馈权重
    % 输出:
    %   u_k_refined - 优化后的模态
    
    K = size(u_k, 1);
    u_k_refined = u_k;
    
    % 计算总能量（用于归一化）
    total_energy = sum(abs(X_time).^2);
    
    if total_energy < 1e-12 || feedback_weight <= 0
        return;
    end
    
    % 将图像域信息分配到各个模态
    for k = 1:K
        % 计算当前模态能量占比
        mode_energy = sum(abs(u_k(k,:)).^2);
        total_mode_energy = sum(sum(abs(u_k).^2));
        
        if total_mode_energy > 0
            mode_ratio = mode_energy / total_mode_energy;
            
            % 应用相位补偿的逆操作
            phase_decompensation = exp(1j * phi_k(k,:));
            
            % 将图像域信息与模态混合
            u_k_refined(k,:) = (1-feedback_weight) * u_k(k,:) + feedback_weight * mode_ratio * X_time .* phase_decompensation;
        end
    end
end

EntropyImage(ISAR_image_integrated+eps)
contrast(ISAR_image_integrated)
EntropyImage(G1_original+eps)
contrast(G1_original)

% apply_dc_suppression 函数
% 实现零频/直流分量抑制，消除图像中央亮线
function X_filtered = apply_dc_suppression(X)
    % 输入:
    %   X - 频域信号
    % 输出:
    %   X_filtered - 零频抑制后的频域信号
    
    % 获取信号长度
    N = length(X);
    
    % 创建陷波滤波器 - 高斯形状的零频抑制器
    dc_idx = floor(N/2) + 1;  % 零频索引 (假设频谱已经fftshift)
    
    % 不使用fftshift的情况下，零频在第一个位置
    if abs(X(1)) > abs(X(dc_idx))
        dc_idx = 1;
    end
    
    % 创建陷波滤波器
    filter_width = max(3, round(N * 0.01));  % 滤波器宽度，至少3个点
    notch_filter = ones(size(X));
    
    % 在零频附近应用窗函数
    for i = 1:N
        dist = min([abs(i - dc_idx), abs(i - dc_idx - N), abs(i - dc_idx + N)]);
        if dist <= filter_width * 2
            % 使用平滑的高斯形状抑制
            notch_filter(i) = 1 - exp(-(dist/filter_width)^2);
        end
    end
    
    % 应用滤波器
    X_filtered = X .* notch_filter;
end



