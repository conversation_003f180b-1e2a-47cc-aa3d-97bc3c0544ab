%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%真实雷达数据的成像结果

clear all;
clc;
load data_ship3.mat
%hrrp=data.hrrp;
%x1=hrrp%(180:491,1350:1701);
%x1=hrrp(3200:3501,5000:25001)
%x1=hrrp(180:500,800:2000);
[N,M]=size(x1);
figure(1);
plot(abs(x1));
title('包络对齐前的一维距离像');

figure(2);
imagesc(abs(x1));
title('包络对齐前的一维距离像');

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%包络对齐%%%%%%%%%%%%%%%%%%%%%%%%

%s3=fftshift(ifft(fftshift(TmpData)));
%x1=RangeAlign_Corr(x1 ,8,30);                 %以两包络相关系数最大为包络对齐标准（数据，插值，窗）
%x1=RangeAlign_MinEnt(s3,8,300,300);        %最小熵包络对齐（数据，插值，对齐窗，搜索窗）

x1=InterPoleCumulateCorrelateAlignXcorrFast2(x1,8,30);
% figure('name','包络对齐后');
% imagesc(20*log10(abs(x1)./max(abs(x1(:)))));
% caxis([-30,0]);
% grid on;axis xy;colorbar;
% 
 %x1=InterPoleCumulateCorrelateAlignXcorrFast2(hrrp,8,30)
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%x1=hrrp(180:490,1350:1700);
x2=PhaseComp_MidTr(x1);  
miu = 1e-3;
P=150;

% 
% [ret_data fai]=PhaseComp_MinEntropy_NI(x2,miu,P);
% 
% x3 = ret_data;
% 
%x3=CompensateByMultiProNo(x1,30);

Y=RDimaging(x2); %直接FFT
G1=20*log10(abs(Y)./max(abs(Y(:))));
figure('name','DCT成像结果');
imagesc(G1);caxis([-30,0]);
grid on;axis xy;colorbar;%axis equal;
% axis([-0.6 0.6 y(select_row(1)) y(select_row(end))]);%set(gca,'xtick',[-0.2 0 0.2]);
xlabel('azimuth');ylabel('range (m)');colormap jet;




