% 清除环境
clc;
clear all;
close all;

% 定义基本参数
j = sqrt(-1);
pi2 = 2 * pi;
c = 3e8; % 光速 (m/s)

%% 雷达参数
fc = 10e9; % 载频 (Hz)
lamda = c / fc; % 波长 (m)
Tp = 2e-6; % 脉冲宽度 (s)
B = 800e6; % 带宽 (Hz)
K = B / Tp; % 调频率 (Hz/s)
PRF = 1000; % 脉冲重复频率 (Hz)
rr = c / 2 / B; % 距离分辨率 (m)

%% 目标参数（飞机模型，多个散射点）
L = 13; % 散射点数量
% 散射点相对于质心的 x 和 y 坐标 (m)，模拟飞机形状
x = [0, 5, 10, 15, -5, -10, -15, 5, 10, 15, -5, -10, -15];
y = [0, 5, 10, 15, 5, 10, 15, -5, -10, -15, -5, -10, -15];
scatters = x + j * y; % 散射点初始位置（复数表示）

% 目标平动参数
y0 = 100e3; % 初始斜距 (m)
fai0 = 3 / 180 * pi; % 初始方位角 (rad)
v = 7.5e3; % 平动速度 (m/s)
x0 = y0 * tan(fai0) + (-500:v/PRF:500); % 方位向平动轨迹
bulk_pos = x0 + j * y0; % 质心位置序列
M = length(bulk_pos); % 脉冲数量

% 目标旋转参数
omega = 0.08; % 角速度 (rad/s)，控制旋转快慢
theta = omega * (0:M-1) / PRF; % 每个脉冲对应的旋转角度

%% 接收设计
% 参考距离（包含随机误差）
sigma = 1;
Rrn = sqrt(sigma) * randn(1, M);
Rref = abs(bulk_pos) + Rrn;
taoref = 2 * Rref / c;

% 接收时间窗
ts_min = -2 * 15 * sqrt(2) / c - Tp / 2;
ts_max = 2 * 15 * sqrt(2) / c + Tp / 2;
osv = 1.2; % 过采样率
fs = (ts_max - ts_min - Tp) * K * osv; % 采样率
ts = ts_min:1/fs:ts_max; % 快时间向量
N = length(ts); % 快时间采样点数

%% 回波仿真（考虑平动和旋转）
raw = zeros(N, M);
for i = 1:M
    % 计算旋转后的散射点坐标
    rot_matrix = exp(j * theta(i)); % 旋转算子
    rotated_scatters = scatters * rot_matrix; % 绕质心旋转 theta(i)
    
    for l = 1:L
        % 散射点绝对位置 = 质心位置 + 旋转后的相对位置
        R = abs(bulk_pos(i) + rotated_scatters(l));
        t = ts + taoref(i);
        tao = 2 * R / c;
        win = (t - tao >= -Tp/2 & t - tao < Tp/2);
        raw(:, i) = raw(:, i) + ...
            win.' .* exp(j * pi2 * (fc * (t - tao) + 1/2 * K * (t - tao).^2)).';
    end
    
    % 解调（Dechirp）参考信号
    sref = exp(j * 2 * pi * fc * (t - taoref(i)) + j * pi * K * (t - taoref(i)).^2);
    raw(:, i) = raw(:, i) .* sref';
end

% 保存仿真数据
save data_airplane raw N M PRF fs ts bulk_pos fai0 v omega theta;
disp('回波仿真完成，数据已保存为 data_airplane.mat');