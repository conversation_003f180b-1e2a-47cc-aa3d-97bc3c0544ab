%-------------------------------------------------------------------------%
%--------        运行增强型ICVMD ISAR成像算法                        -------%
%--------        专为三维转动目标设计                                -------%
%-------------------------------------------------------------------------%
clc; clear all; close all;

% 加载原始数据
load shipx2.mat;

% 显示原始数据的基本信息
[N_r, N_tm] = size(shipx2);
fprintf('原始数据尺寸: %d x %d (距离单元 x 方位单元)\n', N_r, N_tm);

% 显示传统FFT成像结果（用于比较）
Y_original = fftshift(fft(shipx2, [], 2), 2);
max_val_orig = max(abs(Y_original(:)));
G1_original = 20*log10(abs(Y_original)./max_val_orig);

figure('name','传统FFT成像结果');
imagesc(G1_original);
caxis([-40,0]);
grid on;
axis xy;
colorbar;
xlabel('方位单元 (Azimuth Cell / Doppler)');
ylabel('距离单元 (Range Cell)');
colormap jet;
title('传统FFT成像结果 (dB)');

% 运行增强型ICVMD ISAR成像算法
try
    tic;
    [ISAR_image_enhanced, s_compensated] = Enhanced_ICVMD_ISAR(shipx2);
    processing_time = toc;
    fprintf('处理时间: %.2f 秒\n', processing_time);

    % 检查结果中是否有NaN值
    if any(isnan(ISAR_image_enhanced(:))) || any(isnan(s_compensated(:)))
        fprintf('警告: 结果中包含NaN值，尝试使用STVMD ISAR算法替代...\n');

        % 如果有NaN值，使用STVMD ISAR算法替代
        params = struct();
        params.K = 5;                      % 最大模态数
        params.alpha = 2000;               % 平衡参数
        params.tau = 0.1;                  % 拉格朗日乘子更新步长
        params.tol = 1e-7;                 % 收敛容限
        params.window_sizes = [16, 32, 64]; % 多尺度窗口大小
        params.overlap = 0.75;             % 窗口重叠率
        params.dynamic = true;             % 使用动态中心频率
        params.max_iter = 300;             % 最大迭代次数
        params.global_iterations = 3;      % 全局迭代次数
        params.display_progress = true;    % 显示处理进度
        params.display_intermediate = false; % 不显示中间结果

        tic;
        [ISAR_image_enhanced, s_compensated] = STVMD_ISAR(shipx2, params);
        processing_time = toc;
        fprintf('STVMD ISAR处理时间: %.2f 秒\n', processing_time);
    end

    % 显示增强型ISAR成像结果
    figure('name', '增强型ISAR成像结果 (dB)');
    max_val = max(abs(ISAR_image_enhanced(:)));
    if max_val == 0, max_val = 1; end
    ISAR_image_enhanced_db = 20*log10(abs(ISAR_image_enhanced) / max_val);

    imagesc(ISAR_image_enhanced_db);
    caxis([-40, 0]);
    grid on;
    axis xy;
    colorbar;
    xlabel('方位单元 (Azimuth Cell / Doppler)');
    ylabel('距离单元 (Range Cell)');
    colormap jet;
    title('增强型ISAR成像结果 (dB)');

    % 保存结果
    save('Enhanced_ICVMD_ISAR_Results.mat', 'ISAR_image_enhanced', 's_compensated');

    fprintf('处理完成。结果已保存到 Enhanced_ICVMD_ISAR_Results.mat\n');

catch ME
    fprintf('错误: %s\n', ME.message);
    fprintf('尝试使用STVMD ISAR算法替代...\n');

    % 如果出现错误，使用STVMD ISAR算法替代
    params = struct();
    params.K = 5;                      % 最大模态数
    params.alpha = 2000;               % 平衡参数
    params.tau = 0.1;                  % 拉格朗日乘子更新步长
    params.tol = 1e-7;                 % 收敛容限
    params.window_sizes = [16, 32, 64]; % 多尺度窗口大小
    params.overlap = 0.75;             % 窗口重叠率
    params.dynamic = true;             % 使用动态中心频率
    params.max_iter = 300;             % 最大迭代次数
    params.global_iterations = 3;      % 全局迭代次数
    params.display_progress = true;    % 显示处理进度
    params.display_intermediate = false; % 不显示中间结果

    tic;
    [ISAR_image_enhanced, s_compensated] = STVMD_ISAR(shipx2, params);
    processing_time = toc;
    fprintf('STVMD ISAR处理时间: %.2f 秒\n', processing_time);

    % 显示STVMD ISAR成像结果
    figure('name', 'STVMD ISAR成像结果 (dB)');
    max_val = max(abs(ISAR_image_enhanced(:)));
    if max_val == 0, max_val = 1; end
    ISAR_image_enhanced_db = 20*log10(abs(ISAR_image_enhanced) / max_val);

    imagesc(ISAR_image_enhanced_db);
    caxis([-40, 0]);
    grid on;
    axis xy;
    colorbar;
    xlabel('方位单元 (Azimuth Cell / Doppler)');
    ylabel('距离单元 (Range Cell)');
    colormap jet;
    title('STVMD ISAR成像结果 (dB)');

    % 保存结果
    save('STVMD_ISAR_Results.mat', 'ISAR_image_enhanced', 's_compensated');

    fprintf('处理完成。结果已保存到 STVMD_ISAR_Results.mat\n');
end
