%-------------------------------------------------------------------------%
%--------   ISAR Imaging based on Short-Time Variational Mode Decomposition   -------%
%--------   专为三维转动目标设计，解决散焦问题                              -------%
%-------------------------------------------------------------------------%

function [ISAR_image_enhanced, s_compensated] = STVMD_ISAR(radar_data, params)
% STVMD_ISAR - 基于短时变分模态分解的ISAR成像算法
%
% 该算法结合了短时傅里叶变换和变分模态分解的优点，专为处理非平稳信号设计，
% 能有效解决三维转动目标ISAR成像中的散焦问题。
%
% 输入:
%   radar_data - 距离压缩后的回波数据 (距离单元 x 方位单元)
%   params - 算法参数结构体:
%     .K - 模态数量 (默认: 3)
%     .alpha - 平衡参数 (默认: 2000)
%     .tau - 拉格朗日乘子更新步长 (默认: 0.1)
%     .tol - 收敛容限 (默认: 1e-7)
%     .window_sizes - 多尺度窗口大小数组 (默认: [16, 32, 64])
%     .overlap - 窗口重叠率 (默认: 0.5)
%     .dynamic - 是否使用动态中心频率 (默认: true)
%     .max_iter - 最大迭代次数 (默认: 500)
%     .global_iterations - 全局迭代次数 (默认: 3)
%     .display_progress - 是否显示处理进度 (默认: true)
%     .display_intermediate - 是否显示中间结果 (默认: false)
%
% 输出:
%   ISAR_image_enhanced - 增强型STVMD处理后的ISAR图像
%   s_compensated - 相位补偿后的信号

% 设置默认参数
if nargin < 2
    params = struct();
end

if ~isfield(params, 'K'), params.K = 3; end
if ~isfield(params, 'alpha'), params.alpha = 2000; end
if ~isfield(params, 'tau'), params.tau = 0.1; end
if ~isfield(params, 'tol'), params.tol = 1e-7; end
if ~isfield(params, 'window_sizes'), params.window_sizes = [16, 32, 64]; end
if ~isfield(params, 'overlap'), params.overlap = 0.5; end
if ~isfield(params, 'dynamic'), params.dynamic = true; end
if ~isfield(params, 'max_iter'), params.max_iter = 500; end
if ~isfield(params, 'global_iterations'), params.global_iterations = 3; end
if ~isfield(params, 'display_progress'), params.display_progress = true; end
if ~isfield(params, 'display_intermediate'), params.display_intermediate = false; end

% 获取数据尺寸
[N_r, N_tm] = size(radar_data);

% 创建慢时间轴（归一化）
tm_norm = (0:N_tm-1)/N_tm;
tm_indices = 0:N_tm-1;

% 频率轴（归一化频率，范围 [0, 1)）
omega_axis_norm = (0:N_tm-1) / N_tm;

% 初始化存储补偿后的信号
s_compensated = zeros(N_r, N_tm, 'like', 1j);
s_temp = radar_data;  % 临时存储用于迭代处理的信号

% --- 初始相位补偿 ---
% 创建距离单元索引向量
r_indices = (1:N_r)';
ones_r = ones(N_r, 1);

% 应用初始相位补偿（包含线性、二次和三次项）
linear_term = 200;    % 线性多普勒项 (Hz)
quadratic_term = 40;  % 二次多普勒项 (Hz/s)
cubic_term = 400;     % 三次多普勒项 (Hz/s^2)

% 计算完整的相位补偿项
s_phase_comp = exp(1j * ones_r * (2*pi*(linear_term*tm_norm + (1/2)*quadratic_term*tm_norm.^2 + (1/6)*cubic_term*tm_norm.^3)));
s_temp = s_temp .* s_phase_comp;

if params.display_progress
    fprintf('开始STVMD ISAR处理 (共 %d 个距离单元)...\n', N_r);
end

% --- 全局迭代优化 ---
for global_iter = 1:params.global_iterations
    if params.display_progress
        fprintf('全局迭代: %d / %d\n', global_iter, params.global_iterations);
    end

    % 计算当前图像熵作为基准
    temp_image = fftshift(fft(s_temp, [], 2), 2);
    base_entropy = calculate_image_entropy(abs(temp_image));

    if params.display_progress
        fprintf('当前图像熵: %.4f\n', base_entropy);
    end

    % 显示中间结果
    if params.display_intermediate
        figure('name', sprintf('迭代 %d - 中间结果', global_iter));
        max_val = max(abs(temp_image(:)));
        if max_val == 0, max_val = 1; end
        temp_image_db = 20*log10(abs(temp_image) / max_val);

        imagesc(temp_image_db);
        caxis([-40, 0]);
        colormap('jet');
        colorbar;
        xlabel('方位单元 (Azimuth Cell / Doppler)');
        ylabel('距离单元 (Range Cell)');
        title(sprintf('迭代 %d - 中间结果 (dB)', global_iter));
        axis xy;
    end

    % --- 对每个距离单元进行处理 ---
    for r_idx = 1:N_r
        if params.display_progress && mod(r_idx, round(N_r/10)) == 0
            fprintf('处理距离单元: %d / %d\n', r_idx, N_r);
        end

        % 获取当前距离单元的信号
        signal_r_tm = s_temp(r_idx, :);

        % 跳过能量过低的距离单元
        signal_energy = sum(abs(signal_r_tm).^2);
        if signal_energy < 1e-10
            s_compensated(r_idx, :) = signal_r_tm;
            continue;
        end

        % --- 自适应确定模态数量 ---
        % 计算信号的频谱
        S_r_omega = fft(signal_r_tm);
        abs_spectrum = abs(S_r_omega);

        % 平滑频谱
        window_size = 5;
        smoothed_spectrum = conv(abs_spectrum, ones(1, window_size)/window_size, 'same');

        % 寻找频谱峰值
        [~, peak_indices] = findpeaks(smoothed_spectrum, 'MinPeakHeight', 0.1*max(smoothed_spectrum));

        % 根据峰值数量自适应确定模态数K
        K = min(max(length(peak_indices), 2), params.K);

        % 自适应调整alpha参数（根据信号能量）
        mean_energy = mean(abs(signal_r_tm).^2);
        if mean_energy < eps
            mean_energy = eps;
        end
        alpha_stvmd = params.alpha * (1 + 0.5*exp(-signal_energy/mean_energy));

        % 自适应调整tau参数
        tau_stvmd = params.tau * (1 - 0.3*exp(-signal_energy/mean_energy));

        % --- 多尺度STVMD分解 ---
        % 初始化多尺度分解结果
        u_k_tm_multiscale = cell(length(params.window_sizes), 1);
        omega_k_multiscale = cell(length(params.window_sizes), 1);

        for scale_idx = 1:length(params.window_sizes)
            win_size = params.window_sizes(scale_idx);

            % 确保窗口大小不超过信号长度
            if win_size > N_tm
                win_size = N_tm;
            end

            % 应用STVMD分解
            [u_k_tm, omega_k] = stvmd_decompose(signal_r_tm, K, alpha_stvmd, tau_stvmd, win_size, params.overlap, params.dynamic, params.max_iter, params.tol);

            % 存储当前尺度的分解结果
            u_k_tm_multiscale{scale_idx} = u_k_tm;
            omega_k_multiscale{scale_idx} = omega_k;
        end

        % --- 多尺度融合相位误差估计 ---
        % 初始化融合权重和相位误差
        scale_weights = zeros(length(params.window_sizes), 1);
        phase_errors_multiscale = zeros(length(params.window_sizes), N_tm);

        % 计算每个尺度的权重和相位误差
        for scale_idx = 1:length(params.window_sizes)
            u_k_tm_decomposed = u_k_tm_multiscale{scale_idx};
            omega_k_current = omega_k_multiscale{scale_idx};

            % 计算每个模式的能量和相干性
            K_current = size(u_k_tm_decomposed, 1);
            mode_energies = sum(abs(u_k_tm_decomposed).^2, 2);
            mode_coherence = zeros(K_current, 1);

            for k_idx = 1:K_current
                % 计算相干性指标（基于相位变化的平滑度）
                phase_seq = unwrap(angle(u_k_tm_decomposed(k_idx, :)));
                phase_diff = diff(phase_seq);
                mode_coherence(k_idx) = 1 / (std(phase_diff) + eps);
            end

            % 结合能量和相干性选择最佳模式
            combined_score = mode_energies .* mode_coherence;
            [best_score, best_mode_idx] = max(combined_score);

            % 计算当前尺度的权重
            scale_weights(scale_idx) = best_score;

            % 获取主导模式的中心频率
            f_center_dominant_norm = omega_k_current(best_mode_idx);

            % 计算相位误差
            u_dominant_tm = u_k_tm_decomposed(best_mode_idx, :);
            phase_error = angle(u_dominant_tm) - 2 * pi * f_center_dominant_norm .* tm_indices;

            % 解缠绕
            phase_error = unwrap(phase_error);

            % 使用多项式拟合平滑相位误差
            poly_order = min(6, floor(N_tm/10));
            p = polyfit(tm_indices, phase_error, poly_order);
            phase_error_smooth = polyval(p, tm_indices);

            % 移除均值
            phase_error_smooth = phase_error_smooth - mean(phase_error_smooth);

            % 存储当前尺度的相位误差
            phase_errors_multiscale(scale_idx, :) = phase_error_smooth;
        end

        % 归一化权重
        sum_weights = sum(scale_weights);
        if sum_weights > 0
            scale_weights = scale_weights / sum_weights;
        else
            scale_weights = ones(length(params.window_sizes), 1) / length(params.window_sizes);
        end

        % 加权融合多尺度相位误差
        fused_phase_error = zeros(1, N_tm);
        for scale_idx = 1:length(params.window_sizes)
            fused_phase_error = fused_phase_error + scale_weights(scale_idx) * phase_errors_multiscale(scale_idx, :);
        end

        % --- 相位补偿 ---
        s_compensated(r_idx, :) = signal_r_tm .* exp(-1j * fused_phase_error);
    end

    % 更新临时信号用于下一次迭代
    s_temp = s_compensated;

    % 计算当前迭代后的图像熵
    temp_image = fftshift(fft(s_temp, [], 2), 2);
    current_entropy = calculate_image_entropy(abs(temp_image));

    % 检查是否收敛（熵减少不明显则停止）
    entropy_improvement = base_entropy - current_entropy;

    if params.display_progress
        fprintf('迭代 %d 熵改进: %.4f\n', global_iter, entropy_improvement);
    end

    if entropy_improvement < 0.01 && global_iter > 1
        if params.display_progress
            fprintf('熵改进不明显，提前停止迭代\n');
        end
        break;
    end
end

if params.display_progress
    fprintf('STVMD ISAR处理完成。\n');
end

% --- 方位向FFT成像 ---
ISAR_image_enhanced = fftshift(fft(s_compensated, [], 2), 2);

end

% --- 辅助函数：计算图像熵 ---
function entropy = calculate_image_entropy(image_data)
% 归一化图像数据
normalized_data = image_data / (sum(image_data(:)) + eps);
% 计算熵
entropy = -sum(normalized_data(normalized_data > 0) .* log2(normalized_data(normalized_data > 0)));
end

% --- 辅助函数：STVMD分解 ---
function [u_k_tm, omega_k] = stvmd_decompose(signal, K, alpha, tau, window_length, overlap, dynamic, max_iter, tol)
% STVMD_DECOMPOSE - 短时变分模态分解核心算法
%
% 该函数实现了短时变分模态分解算法，结合了短时傅里叶变换的时频局部化特性，
% 能更好地处理非平稳信号。
%
% 输入:
%   signal - 待分解的复信号
%   K - 模态数量
%   alpha - 平衡参数
%   tau - 拉格朗日乘子更新步长
%   window_length - 窗口长度
%   overlap - 窗口重叠率
%   dynamic - 是否使用动态中心频率
%   max_iter - 最大迭代次数
%   tol - 收敛容限
%
% 输出:
%   u_k_tm - 分解后的模态函数 (K x signal_length)
%   omega_k - 各模态的中心频率 (K x 1)

% 初始化参数
signal_length = length(signal);
signal = signal(:).'; % 确保为行向量

% --- 步骤1: 将信号分割为重叠窗口 ---
step = floor(window_length * (1 - overlap));
num_segments = floor((signal_length - window_length) / step) + 1;

% 初始化窗口
segments = zeros(num_segments, window_length);
for i = 1:num_segments
    start_idx = (i-1) * step + 1;
    end_idx = start_idx + window_length - 1;
    if end_idx <= signal_length
        segments(i, :) = signal(start_idx:end_idx);
    else
        % 处理边界情况
        segments(i, 1:(signal_length-start_idx+1)) = signal(start_idx:end);
        segments(i, (signal_length-start_idx+2):end) = 0;
    end
end

% --- 步骤2: 应用窗函数 ---
window_func = hamming(window_length)';
windowed_segments = segments .* repmat(window_func, num_segments, 1);

% --- 步骤3: 准备频域表示 ---
n_fft = 2^nextpow2(window_length); % 使用2的幂次方长度进行FFT
f_hat_s = fft(windowed_segments, n_fft, 2);

% 预分配内存
u_hat_s = zeros(num_segments, n_fft, K);
omega_s = zeros(K, num_segments);

% 初始化各模态的中心频率（均匀分布）
for k = 1:K
    omega_s(k, :) = (0.5 / K) * (k - 1) * ones(1, num_segments);
end

% --- 步骤4: 迭代VMD优化 ---
% 预分配拉格朗日乘子和累积模态
lambda_hat = zeros(num_segments, n_fft);
sum_uk = zeros(num_segments, n_fft);

for iter = 1:max_iter
    % 保存上一次迭代的值用于收敛检查
    u_hat_prev = u_hat_s;
    omega_prev = omega_s;

    % 更新每个模态
    for k = 1:K
        % 计算除当前模态外的所有模态之和
        sum_uk = sum(u_hat_s, 3) - u_hat_s(:, :, k);

        % 更新第k个模态的频域表示
        for t = 1:num_segments
            % 构建频域窗口
            freqs = (0:n_fft-1) / n_fft;
            freqs(freqs > 0.5) = freqs(freqs > 0.5) - 1; % 移到[-0.5, 0.5]区间

            % 计算滤波器（维纳滤波器）
            filter = 1 ./ (1 + alpha * ((freqs - omega_s(k, t)).^2));

            % 更新窗口t，模态k的u_hat
            u_hat_s(t, :, k) = (f_hat_s(t, :) - sum_uk(t, :) + (lambda_hat(t, :) / 2)) .* filter;
        end

        % 更新omega_k（模态k的中心频率）
        if dynamic
            % 动态STVMD：允许中心频率随时间变化
            for t = 1:num_segments
                freqs = (0:n_fft-1) / n_fft;
                freqs(freqs > 0.5) = freqs(freqs > 0.5) - 1;

                % 找到使能量最小化的新中心频率
                power_spectrum = abs(u_hat_s(t, :, k)).^2;
                weighted_sum = sum(freqs .* power_spectrum);
                total_power = sum(power_spectrum);

                if total_power > eps
                    omega_s(k, t) = weighted_sum / total_power;
                end
            end
        else
            % 非动态STVMD：所有窗口使用相同的中心频率
            power_spectrum = sum(abs(u_hat_s(:, :, k)).^2, 1);
            freqs = (0:n_fft-1) / n_fft;
            freqs(freqs > 0.5) = freqs(freqs > 0.5) - 1;

            weighted_sum = sum(freqs .* power_spectrum);
            total_power = sum(power_spectrum);

            if total_power > eps
                omega_s(k, :) = (weighted_sum / total_power) * ones(1, num_segments);
            end
        end
    end

    % 拉格朗日乘子的对偶上升更新
    for t = 1:num_segments
        sum_modes = sum(u_hat_s(t, :, :), 3);
        lambda_hat(t, :) = lambda_hat(t, :) + tau * (sum_modes - f_hat_s(t, :));
    end

    % 检查收敛性
    u_diff = sum(sum(sum(abs(u_hat_s - u_hat_prev).^2))) / (sum(sum(sum(abs(u_hat_prev).^2))) + eps);
    omega_diff = sum(sum(abs(omega_s - omega_prev).^2)) / (sum(sum(abs(omega_prev).^2)) + eps);

    if (u_diff < tol) && (omega_diff < tol)
        break;
    end
end

% --- 步骤5: 重建时域IMFs ---
u_s = zeros(num_segments, window_length, K);
for k = 1:K
    for t = 1:num_segments
        temp = ifft(u_hat_s(t, :, k), n_fft, 2);
        u_s(t, 1:window_length, k) = temp(1:window_length);
    end
end

% --- 步骤6: 重叠相加合成重建完整长度的IMFs ---
u_k_tm = zeros(K, signal_length);
normalization = zeros(1, signal_length);

for k = 1:K
    % 重建每个IMF
    for i = 1:num_segments
        start_idx = (i-1) * step + 1;
        end_idx = min(start_idx + window_length - 1, signal_length);
        segment_length = end_idx - start_idx + 1;

        % 重叠相加带窗
        u_k_tm(k, start_idx:end_idx) = u_k_tm(k, start_idx:end_idx) + u_s(i, 1:segment_length, k) .* window_func(1:segment_length);

        % 累积窗函数用于归一化
        if k == 1 % 只需要计算一次
            normalization(start_idx:end_idx) = normalization(start_idx:end_idx) + window_func(1:segment_length);
        end
    end
end

% 避免除以零
normalization(normalization < eps) = 1;

% 应用归一化
for k = 1:K
    u_k_tm(k, :) = u_k_tm(k, :) ./ normalization;
end

% 返回最终的中心频率（取平均值）
omega_k = mean(omega_s, 2);
end
