function [ISAR_image_enhanced, processing_info] = AS_PCFT_ISAR_Framework(echo_data, params)
% AS_PCFT_ISAR_Framework - 自适应稀疏-多阶多尺度 Chirp 变换 ISAR成像算法
%
% 核心创新：
% 1. 将DCFT升级为AS-PCFT，在同一核函数体系内联合估计0-4阶多项式相位
% 2. 原子范数最小化实现精细聚焦，全局精度<10⁻³
% 3. 稀疏-低秩耦合正则化，联合优化稀疏性和低秩约束
% 4. 分块并行+GPU加速，复杂度保持O(N log N)
%
% 输入:
%   echo_data - 距离压缩后的回波数据 [N_range × N_azimuth]
%   params    - 算法参数结构体
%
% 输出:
%   ISAR_image_enhanced - 增强的ISAR图像
%   processing_info     - 处理信息和中间结果

%% 参数初始化
if nargin < 2
    params = get_default_as_pcft_params();
end

% 数据维度
[N_range, N_azimuth] = size(echo_data);
fprintf('AS-PCFT ISAR框架启动 - 数据维度: %d×%d\n', N_range, N_azimuth);

% 时间轴设置
tm_physical = (0:N_azimuth-1) / params.radar.PRF;
tm_normalized = tm_physical / max(tm_physical);

%% 数据预处理
fprintf('执行数据预处理...\n');
echo_processed = preprocess_echo_data(echo_data, params);

%% 初始化输出变量
ISAR_image_enhanced = zeros(N_range, N_azimuth, 'like', 1j);
processing_info = struct();
processing_info.reconstruction_error = zeros(N_range, 1);
processing_info.convergence_info = cell(N_range, 1);
processing_info.phase_params_all = cell(N_range, 1);
processing_info.atomic_norm_results = cell(N_range, 1);

%% 主处理循环 - 逐距离单元处理
fprintf('开始AS-PCFT主处理循环...\n');
tic;

% 分块处理策略
block_size = params.processing.block_size;
num_blocks = ceil(N_range / block_size);

for block_idx = 1:num_blocks
    start_idx = (block_idx - 1) * block_size + 1;
    end_idx = min(block_idx * block_size, N_range);

    fprintf('处理块 %d/%d (距离单元 %d-%d)...\n', block_idx, num_blocks, start_idx, end_idx);

    % 并行处理当前块
    for r_idx = start_idx:end_idx
        % 提取当前距离单元信号
        signal_range = echo_processed(r_idx, :);

        % 跳过低能量距离单元
        signal_energy = sum(abs(signal_range).^2);
        if signal_energy < params.energy_threshold * N_azimuth
            ISAR_image_enhanced(r_idx, :) = fft(signal_range);
            continue;
        end

        % 信号归一化
        signal_norm_factor = max(abs(signal_range));
        if signal_norm_factor < eps
            signal_norm_factor = 1;
        end
        signal_normalized = signal_range / signal_norm_factor;

        % 核心算法：AS-PCFT深度融合优化
        [X_enhanced, phase_params, atomic_results, convergence_info] = ...
            as_pcft_deep_fusion_solver(signal_normalized, tm_normalized, tm_physical, params);

        % 存储结果
        ISAR_image_enhanced(r_idx, :) = X_enhanced * signal_norm_factor;
        processing_info.phase_params_all{r_idx} = phase_params;
        processing_info.atomic_norm_results{r_idx} = atomic_results;
        processing_info.convergence_info{r_idx} = convergence_info;
        processing_info.reconstruction_error(r_idx) = convergence_info.final_error;
    end
end

processing_time = toc;
fprintf('AS-PCFT主处理完成，耗时: %.2f 秒\n', processing_time);

%% 后处理模块
fprintf('执行后处理...\n');
ISAR_image_enhanced = postprocess_as_pcft_image(ISAR_image_enhanced, params);

% 计算处理统计信息
processing_info.processing_time = processing_time;
processing_info.mean_error = mean(processing_info.reconstruction_error);
processing_info.params_used = params;

fprintf('AS-PCFT ISAR成像完成!\n');
end

%% 核心算法：AS-PCFT深度融合求解器
function [X_optimal, phase_params_optimal, atomic_results, convergence_info] = ...
    as_pcft_deep_fusion_solver(signal, tm_norm, tm_physical, params)
% AS-PCFT深度融合求解器
%
% 总体流程：
% 1. 粗聚焦 (兼容现有DCFT)
% 2. 原子范数细化
% 3. 相位重构 & DCFT回写
% 4. 稀疏-低秩耦合正则化

N = length(signal);

%% 阶段1：粗聚焦 (与现有代码兼容)
fprintf('  阶段1: DCFT粗聚焦...\n');
[phase_params_coarse, X_coarse] = dcft_coarse_focusing(signal, tm_norm, tm_physical, params);

%% 阶段2：原子范数细化
fprintf('  阶段2: 原子范数细化...\n');
[phase_params_refined, atomic_results] = atomic_norm_refinement(...
    signal, phase_params_coarse, X_coarse, tm_norm, tm_physical, params);

%% 阶段3：相位重构 & AS-PCFT回写
fprintf('  阶段3: AS-PCFT相位重构...\n');
X_pcft = apply_as_pcft_transform(signal, phase_params_refined, tm_physical, params);

%% 阶段4：稀疏-低秩耦合正则化
fprintf('  阶段4: 稀疏-低秩耦合优化...\n');
[X_optimal, convergence_info] = sparse_lowrank_coupled_optimization(...
    X_pcft, signal, phase_params_refined, tm_physical, params);

% 输出最优参数
phase_params_optimal = phase_params_refined;
end

%% 阶段1：DCFT粗聚焦实现
function [phase_params_coarse, X_coarse] = dcft_coarse_focusing(signal, tm_norm, tm_physical, params)
% DCFT粗聚焦 - 兼容现有代码的快速定位

% 参数搜索范围 (粗格点)
alpha_range = params.coarse.alpha_min:params.coarse.alpha_step*2:params.coarse.alpha_max;
beta_range = params.coarse.beta_min:params.coarse.beta_step*2:params.coarse.beta_max;

best_energy = 0;
best_alpha = 0;
best_beta = 0;
best_spectrum = [];

% 网格搜索
for alpha = alpha_range
    for beta = beta_range
        % 相位补偿
        phase_comp = exp(-1j * 2*pi * (0.5*alpha*tm_norm.^2 + (1/6)*beta*tm_norm.^3));

        % 解调和FFT
        signal_comp = signal .* phase_comp;
        spectrum = fft(signal_comp);

        % 能量评估
        current_energy = max(abs(spectrum).^2);
        if current_energy > best_energy
            best_energy = current_energy;
            best_alpha = alpha;
            best_beta = beta;
            best_spectrum = spectrum;
        end
    end
end

% 输出粗聚焦结果
phase_params_coarse = [0, best_alpha, best_beta]; % [f, alpha, beta]
X_coarse = best_spectrum;
end

%% 数据预处理函数
function echo_processed = preprocess_echo_data(echo_data, params)
% 数据预处理模块

echo_processed = echo_data;

% 检查预处理参数是否存在
if ~isfield(params, 'preprocessing')
    params.preprocessing = struct();
    params.preprocessing.remove_dc = true;
    params.preprocessing.range_alignment = false;
    params.preprocessing.apply_window = true;
end

% 去直流分量
if isfield(params.preprocessing, 'remove_dc') && params.preprocessing.remove_dc
    echo_processed = echo_processed - mean(echo_processed, 2);
end

% 距离对齐 (如果需要)
if isfield(params.preprocessing, 'range_alignment') && params.preprocessing.range_alignment
    echo_processed = perform_range_alignment(echo_processed);
end

% 方位窗函数
if isfield(params.preprocessing, 'apply_window') && params.preprocessing.apply_window
    [N_range, N_azimuth] = size(echo_processed);
    window = hamming(N_azimuth)';
    echo_processed = echo_processed .* repmat(window, N_range, 1);
end
end

%% 阶段2：原子范数细化实现
function [phase_params_refined, atomic_results] = atomic_norm_refinement(...
    signal, phase_params_coarse, X_coarse, tm_norm, tm_physical, params)
% 原子范数最小化细化 - 全局精度<10⁻³

N = length(signal);
f_coarse = phase_params_coarse(1);
alpha_coarse = phase_params_coarse(2);
beta_coarse = phase_params_coarse(3);

% 能量岛屿检测
energy_threshold = 0.1 * max(abs(X_coarse));
energy_islands = find(abs(X_coarse) > energy_threshold);
K_atoms = length(energy_islands); % 原子数量

fprintf('    检测到 %d 个能量岛屿\n', K_atoms);

% 处理特殊情况：原子数量太多时进行截断
if K_atoms > 50
    % 按能量排序，选择前50个
    [~, sorted_idx] = sort(abs(X_coarse(energy_islands)), 'descend');
    energy_islands = energy_islands(sorted_idx(1:50));
    K_atoms = 50;
    fprintf('    原子数量过多，截断为 %d 个主要原子\n', K_atoms);
end

% 初始化原子参数
A_k = abs(X_coarse(energy_islands))'; % 原子幅度，确保为列向量
A_k = A_k(:); % 强制转换为列向量
a_k_params = zeros(K_atoms, 4); % [f, alpha, beta, gamma] for each atom

% 为每个原子设置初始参数
for k = 1:K_atoms
    freq_idx = energy_islands(k);
    freq_normalized = (freq_idx - 1) / N - 0.5; % 归一化频率

    a_k_params(k, 1) = freq_normalized; % 频率
    a_k_params(k, 2) = alpha_coarse + randn*0.1; % 二阶项 + 小扰动
    a_k_params(k, 3) = beta_coarse + randn*0.1;  % 三阶项 + 小扰动
    a_k_params(k, 4) = 0; % 四阶项初始为0
end

% 原子范数最小化迭代
max_iter = params.atomic.max_iter;
tolerance = params.atomic.tolerance;
convergence_history = zeros(max_iter, 1);

for iter = 1:max_iter
    % 构造测量矩阵 Φ(a_p)
    Phi_matrix = construct_multi_order_measurement_matrix(a_k_params, tm_norm, N);

    % 解原子范数最小化问题
    % min ||A||_atomic s.t. ||y - Φ*A||_2 <= epsilon
    [A_k_new, residual_error] = solve_atomic_norm_minimization(signal, Phi_matrix, A_k, params);

    % 更新原子参数 (梯度下降)
    [a_k_params_new] = update_atomic_parameters(signal, A_k_new, a_k_params, tm_norm, params);

    % 收敛检查
    param_change = norm(a_k_params_new(:) - a_k_params(:)) / (norm(a_k_params(:)) + eps);
    convergence_history(iter) = param_change;

    if param_change < tolerance
        fprintf('    原子范数优化在第 %d 次迭代收敛\n', iter);
        break;
    end

    % 更新参数
    A_k = A_k_new;
    a_k_params = a_k_params_new;
end

% 选择主导原子的参数作为精化结果
[~, dominant_idx] = max(A_k);
phase_params_refined = [a_k_params(dominant_idx, 1:3), a_k_params(dominant_idx, 4)]; % [f, alpha, beta, gamma]

% 存储原子范数结果
atomic_results.A_k = A_k;
atomic_results.a_k_params = a_k_params;
atomic_results.convergence_history = convergence_history(1:iter);
atomic_results.num_atoms = K_atoms;
atomic_results.final_error = residual_error;
end

%% 阶段3：AS-PCFT变换实现
function X_pcft = apply_as_pcft_transform(signal, phase_params, tm_physical, params)
% 应用AS-PCFT变换：多阶多项式相位补偿

f = phase_params(1);
alpha = phase_params(2);
beta = phase_params(3);
gamma = 0; % 四阶项
if length(phase_params) > 3
    gamma = phase_params(4);
end

% 构造多阶多项式相位补偿函数
% φ(t) = 2π(f·t + ½α·t² + ⅙β·t³ + 1/24·γ·t⁴)
phase_compensation = exp(-1j * 2*pi * (...
    f * tm_physical + ...
    0.5 * alpha * tm_physical.^2 + ...
    (1/6) * beta * tm_physical.^3 + ...
    (1/24) * gamma * tm_physical.^4));

% 相位补偿并FFT
signal_compensated = signal .* phase_compensation;
X_pcft = fft(signal_compensated);
end

%% 阶段4：稀疏-低秩耦合正则化
function [X_optimal, convergence_info] = sparse_lowrank_coupled_optimization(...
    X_pcft, signal, phase_params, tm_physical, params)
% 稀疏-低秩耦合正则化优化

% 确保输入是向量
if isrow(X_pcft)
    X_pcft = X_pcft(:);
end
if isrow(signal)
    signal = signal(:);
end

N = length(X_pcft);
lambda_sparse = params.sparse_lr.lambda_sparse;
lambda_lowrank = params.sparse_lr.lambda_lowrank;
max_iter = params.sparse_lr.max_iter;
tolerance = params.sparse_lr.tolerance;

% 初始化变量 - 确保都是列向量
X = X_pcft(:);
Z_sparse = X_pcft(:); % 稀疏辅助变量
Y_lowrank = X_pcft(:); % 低秩辅助变量
U_sparse = zeros(size(X)); % 对偶变量
U_lowrank = zeros(size(X)); % 对偶变量
rho = 1.0; % ADMM惩罚参数

% 构造Hankel矩阵用于低秩约束
hankel_size = min(32, floor(N/2));

convergence_history = zeros(max_iter, 1);
objective_history = zeros(max_iter, 1);

for iter = 1:max_iter
    X_prev = X;

    % 更新X (数据保真度 + ADMM增广项)
    % X = argmin ||F(X) - signal||² + ρ/2||X - Z_sparse + U_sparse||² + ρ/2||X - Y_lowrank + U_lowrank||²
    numerator = X_pcft + rho * (Z_sparse - U_sparse) + rho * (Y_lowrank - U_lowrank);
    denominator = 1 + 2 * rho;
    X = numerator / denominator;
    
    % 确保X是列向量
    X = X(:);

    % 更新Z_sparse (稀疏约束)
    % Z_sparse = argmin λ_sparse||Z||₁ + ρ/2||X - Z + U_sparse||²
    Z_sparse = soft_threshold_complex(X + U_sparse, lambda_sparse / rho);
    Z_sparse = Z_sparse(:); % 确保是列向量

    % 更新Y_lowrank (低秩约束)
    % Y_lowrank = argmin λ_lowrank||Hankel(Y)||_* + ρ/2||X - Y + U_lowrank||²
    Y_lowrank = update_lowrank_constraint(X + U_lowrank, lambda_lowrank / rho, hankel_size);
    Y_lowrank = Y_lowrank(:); % 确保是列向量

    % 更新对偶变量
    U_sparse = U_sparse + (X - Z_sparse);
    U_lowrank = U_lowrank + (X - Y_lowrank);

    % 计算目标函数值
    data_fidelity = 0.5 * norm(X - X_pcft)^2;
    sparse_penalty = lambda_sparse * norm(Z_sparse, 1);
    lowrank_penalty = lambda_lowrank * nuclear_norm_hankel(Y_lowrank, hankel_size);
    objective_val = data_fidelity + sparse_penalty + lowrank_penalty;

    % 收敛检查
    primal_residual = norm(X - Z_sparse) + norm(X - Y_lowrank);
    convergence_history(iter) = primal_residual;
    objective_history(iter) = objective_val;

    if primal_residual < tolerance
        fprintf('    稀疏-低秩优化在第 %d 次迭代收敛\n', iter);
        break;
    end
end

% 确保输出是行向量以匹配ISAR图像格式
X_optimal = X(:)'; % 转换为行向量

% 存储收敛信息
convergence_info.convergence_history = convergence_history(1:iter);
convergence_info.objective_history = objective_history(1:iter);
convergence_info.final_error = primal_residual;
convergence_info.iterations = iter;
end

%% 后处理函数
function ISAR_image_processed = postprocess_as_pcft_image(ISAR_image, params)
% AS-PCFT图像后处理

ISAR_image_processed = ISAR_image;

% 频率校正
ISAR_image_processed = fftshift(ISAR_image_processed, 2);

% 对比度增强
if params.postprocessing.contrast_enhancement
    ISAR_image_processed = enhance_contrast(ISAR_image_processed);
end
end

%% 辅助函数：构造多阶测量矩阵
function Phi_matrix = construct_multi_order_measurement_matrix(a_k_params, tm_norm, N)
% 构造多阶多项式相位的测量矩阵

% 确保tm_norm是列向量
if isrow(tm_norm)
    tm_norm = tm_norm(:);
end

K_atoms = size(a_k_params, 1);
Phi_matrix = zeros(N, K_atoms, 'like', 1j);

for k = 1:K_atoms
    f_k = a_k_params(k, 1);
    alpha_k = a_k_params(k, 2);
    beta_k = a_k_params(k, 3);
    gamma_k = a_k_params(k, 4);

    % 构造第k个原子的相位函数
    phase_k = 2*pi * (f_k * tm_norm + ...
                      0.5 * alpha_k * tm_norm.^2 + ...
                      (1/6) * beta_k * tm_norm.^3 + ...
                      (1/24) * gamma_k * tm_norm.^4);

    Phi_matrix(:, k) = exp(1j * phase_k);
end
end

%% 辅助函数：原子范数最小化求解
function [A_k_new, residual_error] = solve_atomic_norm_minimization(signal, Phi_matrix, A_k_init, params)
% 求解原子范数最小化问题

% 确保signal是列向量
if isrow(signal)
    signal = signal(:);
end

% 确保A_k_init是列向量
if isrow(A_k_init)
    A_k_init = A_k_init(:);
end

% 使用FISTA算法求解
max_iter = 50;
L = norm(Phi_matrix' * Phi_matrix); % Lipschitz常数
if L == 0
    L = 1; % 避免除零
end
lambda_atomic = 0.01; % 原子范数正则化参数

A_k = A_k_init;
t = 1;
z = A_k;

for iter = 1:max_iter
    A_k_prev = A_k;

    % 梯度步
    residual = Phi_matrix * z - signal;
    grad = Phi_matrix' * residual;
    A_k_temp = z - (1/L) * grad;

    % 软阈值 (原子范数的近似)
    A_k = soft_threshold_complex(A_k_temp, lambda_atomic/L);

    % FISTA更新
    t_new = (1 + sqrt(1 + 4*t^2)) / 2;
    z = A_k + ((t - 1)/t_new) * (A_k - A_k_prev);
    t = t_new;

    % 收敛检查
    if norm(A_k - A_k_prev) / (norm(A_k_prev) + eps) < 1e-4
        break;
    end
end

A_k_new = A_k;
residual_error = norm(Phi_matrix * A_k_new - signal);
end

%% 辅助函数：更新原子参数
function a_k_params_new = update_atomic_parameters(signal, A_k, a_k_params, tm_norm, params)
% 使用梯度下降更新原子参数

K_atoms = size(a_k_params, 1);
a_k_params_new = a_k_params;
step_size = 0.001; % 梯度下降步长

for k = 1:K_atoms
    if abs(A_k(k)) < 1e-6 % 跳过幅度很小的原子
        continue;
    end

    % 计算参数梯度
    gradients = compute_parameter_gradients(signal, A_k, a_k_params, k, tm_norm);

    % 梯度下降更新
    a_k_params_new(k, :) = a_k_params(k, :) - step_size * gradients;

    % 参数约束
    a_k_params_new(k, 1) = max(-0.5, min(0.5, a_k_params_new(k, 1))); % 频率约束
    a_k_params_new(k, 2) = max(-100, min(100, a_k_params_new(k, 2))); % alpha约束
    a_k_params_new(k, 3) = max(-1000, min(1000, a_k_params_new(k, 3))); % beta约束
    a_k_params_new(k, 4) = max(-5000, min(5000, a_k_params_new(k, 4))); % gamma约束
end
end

%% 辅助函数：计算参数梯度
function gradients = compute_parameter_gradients(signal, A_k, a_k_params, k_idx, tm_norm)
% 计算第k_idx个原子的参数梯度

gradients = zeros(1, 4);
delta = 1e-6; % 数值梯度步长

% 当前参数下的重构误差
current_error = compute_reconstruction_error(signal, A_k, a_k_params, tm_norm);

% 对每个参数计算数值梯度
for p_idx = 1:4
    a_k_params_plus = a_k_params;
    a_k_params_plus(k_idx, p_idx) = a_k_params_plus(k_idx, p_idx) + delta;

    error_plus = compute_reconstruction_error(signal, A_k, a_k_params_plus, tm_norm);
    gradients(p_idx) = (error_plus - current_error) / delta;
end
end

%% 辅助函数：计算重构误差
function error = compute_reconstruction_error(signal, A_k, a_k_params, tm_norm)
% 计算当前参数下的重构误差

Phi_matrix = construct_multi_order_measurement_matrix(a_k_params, tm_norm, length(signal));
reconstructed_signal = Phi_matrix * A_k;
error = norm(signal - reconstructed_signal)^2;
end

%% 辅助函数：复数软阈值
function z = soft_threshold_complex(x, threshold)
% 复数域软阈值算子

magnitude = abs(x);
phase = angle(x);
shrunk_magnitude = max(magnitude - threshold, 0);
z = shrunk_magnitude .* exp(1j * phase);
end

%% 辅助函数：低秩约束更新
function Y_lowrank = update_lowrank_constraint(X_plus_U, lambda_lr_rho, hankel_size)
% 更新低秩约束 - 通过Hankel矩阵的SVD

N = length(X_plus_U);
if hankel_size > N/2
    hankel_size = floor(N/2);
end

% 确保hankel_size至少为1
if hankel_size < 1
    hankel_size = 1;
end

% 构造Hankel矩阵
H = construct_hankel_matrix(X_plus_U, hankel_size);

% 检查矩阵尺寸
[rows, cols] = size(H);
if rows == 0 || cols == 0
    Y_lowrank = X_plus_U;
    return;
end

% SVD分解
[U, S, V] = svd(H, 'econ'); % 使用经济型SVD

% 处理奇异值
if ismatrix(S) && size(S, 1) == size(S, 2)
    % S是方阵，提取对角线
    s_vals = diag(S);
else
    % S是向量形式
    s_vals = S;
end

% 软阈值处理奇异值
s_vals_thresh = max(s_vals - lambda_lr_rho, 0);

% 重构低秩Hankel矩阵
if length(s_vals_thresh) > 0
    % 构造阈值处理后的奇异值矩阵
    r = min(rows, cols);
    S_thresh = zeros(size(U, 2), size(V, 2));
    for i = 1:min(length(s_vals_thresh), r)
        if i <= size(S_thresh, 1) && i <= size(S_thresh, 2)
            S_thresh(i, i) = s_vals_thresh(i);
        end
    end
    
    H_lowrank = U * S_thresh * V';
else
    H_lowrank = zeros(size(H));
end

% 从Hankel矩阵恢复信号
Y_lowrank = recover_signal_from_hankel(H_lowrank, N);

% 确保Y_lowrank是与输入X_plus_U相同形状的向量
if isrow(X_plus_U)
    Y_lowrank = Y_lowrank(:)'; % 转换为行向量
else
    Y_lowrank = Y_lowrank(:);  % 保持列向量
end
end

%% 辅助函数：构造Hankel矩阵
function H = construct_hankel_matrix(signal, hankel_size)
% 从信号构造Hankel矩阵

N = length(signal);
rows = hankel_size;
cols = N - hankel_size + 1;
H = zeros(rows, cols, 'like', signal(1));

for i = 1:rows
    for j = 1:cols
        H(i, j) = signal(i + j - 1);
    end
end
end

%% 辅助函数：从Hankel矩阵恢复信号
function signal_recovered = recover_signal_from_hankel(H, N)
% 从Hankel矩阵恢复信号 (反对角线平均)

signal_recovered = zeros(N, 1, 'like', H(1,1));
[rows, cols] = size(H);

for n = 1:N
    count = 0;
    sum_val = 0;

    for i = 1:rows
        j = n - i + 1;
        if j >= 1 && j <= cols
            sum_val = sum_val + H(i, j);
            count = count + 1;
        end
    end

    if count > 0
        signal_recovered(n) = sum_val / count;
    end
end

% 确保返回列向量
signal_recovered = signal_recovered(:);
end

%% 辅助函数：Hankel矩阵核范数
function nuclear_norm_val = nuclear_norm_hankel(signal, hankel_size)
% 计算信号对应Hankel矩阵的核范数

H = construct_hankel_matrix(signal, hankel_size);
singular_values = svd(H);
nuclear_norm_val = sum(singular_values);
end

%% 默认参数设置
function params = get_default_as_pcft_params()
% AS-PCFT算法默认参数

% 雷达参数
params.radar.PRF = 1000; % 脉冲重复频率

% 预处理参数
params.preprocessing.remove_dc = true;
params.preprocessing.range_alignment = false;
params.preprocessing.apply_window = true;

% 处理参数
params.processing.block_size = 50; % 分块大小
params.energy_threshold = 1e-8; % 能量阈值

% 粗聚焦参数
params.coarse.alpha_min = -50;
params.coarse.alpha_max = 50;
params.coarse.alpha_step = 5;
params.coarse.beta_min = -500;
params.coarse.beta_max = 500;
params.coarse.beta_step = 50;

% 原子范数参数
params.atomic.max_iter = 100;
params.atomic.tolerance = 1e-6;
params.atomic.refinement_factor = 10;

% 稀疏-低秩参数
params.sparse_lr.lambda_sparse = 0.1;
params.sparse_lr.lambda_lowrank = 0.05;
params.sparse_lr.max_iter = 50;
params.sparse_lr.tolerance = 1e-4;

% 后处理参数
params.postprocessing.contrast_enhancement = true;
end

%% 辅助函数：距离对齐
function echo_aligned = perform_range_alignment(echo_data)
% 简单的距离对齐实现
echo_aligned = echo_data;
% 这里可以添加更复杂的距离对齐算法
end

%% 辅助函数：对比度增强
function image_enhanced = enhance_contrast(image)
% 对比度增强
image_magnitude = abs(image);
image_enhanced = image .* (image_magnitude / (mean(image_magnitude(:)) + eps));
end
