%% ISAR回波信号仿真 - 复杂径向运动和最小旋转
% 基于movebackwave_low.m的结构，但添加ISAR_complex_motion_with_rotation.m中的复杂径向运动
% 专注于研究径向速度对ISAR成像的影响，保持最小必要的旋转
clc;
clear all;
close all;

j = sqrt(-1);
pi2 = 2*pi;
c = 3e8;

%% 雷达参数
fc = 10e9;               % 载波频率 (Hz)
lamda = c / fc;          % 波长 (m)
Tp = 2e-6;               % 脉冲宽度 (s)
B = 800e6;               % 带宽 (Hz)
K = B / Tp;              % 调频率 (Hz/s)
PRF = 1000;              % 脉冲重复频率 (Hz)
PRI = 1/PRF;             % 脉冲重复间隔 (s)
rr = c/2/B;              % 距离分辨率 (m)

%% 目标参数 - 保持较简单的散射点模型
L = 13;
x = [0, 5, 10, 15,... 
    -5, -10, -15,...
    5, 10, 15,...
    -5, -10, -15];
y = [0, 5, 10, 15,...
    5, 10, 15,...
    -5, -10, -15,...
    -5, -10, -15];

scatters = x + j*y;  % 散射点初始位置（相对于质心）

%% 观测和运动参数
M = 256;                % 慢时间采样点数（脉冲数）
T = (M-1)/PRF;          % 总观测时间 (s)
t = (0:M-1)/PRF;        % 慢时间序列 (s)

%% 目标复杂径向运动模型（来自ISAR_complex_motion_with_rotation.m）
% 基础运动参数
y0 = 100e3;             % 初始质心距离 (m)
fai0 = 3/180*pi;        % 初始方位角
v0 = 120;               % 初始径向速度 (m/s)
a0 = 5;                 % 初始加速度 (m/s^2)

% 高阶多项式系数
c3 = 0.8;               % 三阶项系数
c4 = 0.1;               % 四阶项系数
c5 = 0.02;              % 五阶项系数

% 突变事件参数
% 1. 速度阶跃 - 在t1时刻速度突然增加
t1 = T/4;
A1 = 5;
g1 = @(t) (t >= 0);     % 阶跃函数

% 2. 加速度脉冲 - 在t2时刻短暂加速
t2 = T/2;
A2 = 5;
g2 = @(t) (t >= 0) .* exp(-50*t); % 衰减脉冲

% 3. 抖动/振荡 - 在t3时刻开始振荡
t3 = 3*T/4;
A3 = 3;
g3 = @(t) (t >= 0) .* sin(20*pi*t); % 正弦振荡

% 添加最小旋转 - 低速使成像可行但不产生模糊
omega = 0.02;           % 角速度 (rad/s)，保持较低的值
theta = omega * t;      % 每个时刻的旋转角度 (rad)

%% 距离历程计算 R(t) - 复杂径向运动模型
% 基本运动项 (二阶多项式)
R_basic = y0 + v0*t + 0.5*a0*t.^2;

% 高阶多项式项
R_high = c3*t.^3 + c4*t.^4 + c5*t.^5;

% 突变项
R_event1 = A1 * g1(t - t1) .* (t - t1);  % 速度阶跃 (积分后为斜坡)
R_event2 = A2 * arrayfun(@(x) integral(@(t) g2(t-t2), 0, x), t);  % 加速度脉冲积分
R_event3 = A3 * arrayfun(g3, t - t3) .* (t >= t3);  % 振荡

% 总距离历程（质心）
R_t = R_basic + R_high + R_event1 + R_event2 + R_event3;

% 质心x方向位置 (方位向平动轨迹)
x0 = y0*tan(fai0) + linspace(-500, 500, M);  % 使用linspace确保有M个点
bulk_pos = x0 + j*R_t;  % 质心复数位置

%% 接收设计
% 参考距离（用于去斜处理），添加少量随机误差增加真实性
sigma = 1;
Rrn = sqrt(sigma)*randn(1, M);
Rref = abs(bulk_pos) + Rrn;
taoref = 2*Rref/c;

% 接收门设置
ts_min = -2*15*sqrt(2)/c - Tp/2;
ts_max = 2*15*sqrt(2)/c + Tp/2;
osv = 1.2;               
fs = (ts_max - ts_min - Tp)*K*osv;
ts = ts_min:1/fs:ts_max;
N = length(ts);
range_axis = linspace(-N/2, N/2-1, N) * c/(2*B);
%% 回波仿真 - 结合旋转与复杂径向运动
raw = zeros(N, M);
for i = 1:M
    % 计算当前脉冲的旋转矩阵（简单的角度旋转）
    rot_matrix = exp(j*theta(i));  % 旋转算子
    
    % 获取旋转后的散射点
    rotated_scatters = scatters * rot_matrix;  % 绕质心旋转theta(i)
    
    % 处理每个散射点的回波
    for l = 1:L
        % 计算散射点的瞬时距离
        R = abs(bulk_pos(i) + rotated_scatters(l));
        t_fast = ts + taoref(i);
        tao = 2*R/c;
        
        % 生成回波
        win = (t_fast - tao >= -Tp/2 & t_fast - tao < Tp/2);
        raw(:, i) = raw(:, i) + ...
            win.' .* exp(j*pi2*(fc*(t_fast-tao) + 0.5*K*(t_fast-tao).^2)).';
    end
    
    % 去斜处理
    sref = exp(j*2*pi*fc*(t_fast-taoref(i)) + j*pi*K*(t_fast-taoref(i)).^2);
    raw(:, i) = raw(:, i) .* sref';
end

%% 保存回波数据
save('ISAR_radial_velocity_study.mat', 'raw', 'R_t', 'x0', 'omega', ...
     'PRF', 'fc', 'lamda', 'B', 'N', 'M', 'ts', 't');

%% 可视化结果
% 绘制距离历程
figure('Position', [100, 100, 1000, 300]);
plot(t, R_basic, 'b--', 'LineWidth', 1.5); hold on;
plot(t, R_basic + R_high, 'r--', 'LineWidth', 1.5);
plot(t, R_t, 'k-', 'LineWidth', 2);
legend('基本运动 (二阶多项式)', '加高阶项', '总距离历程');
xlabel('时间 (s)'); ylabel('距离 (m)');
title('目标距离历程 R(t)');
grid on;

% 标记突变事件
xline(t1, 'g--', '速度阶跃');
xline(t2, 'm--', '加速度脉冲');
xline(t3, 'c--', '振荡开始');

% 计算并绘制瞬时径向速度
% 一阶导数 - 瞬时速度/多普勒
v_basic = v0 + a0*t;
v_high = 3*c3*t.^2 + 4*c4*t.^3 + 5*c5*t.^4;
v_event1 = A1 * g1(t - t1);
v_event2 = A2 * arrayfun(g2, t - t2) .* (t >= t2);
v_event3 = A3 * arrayfun(@(x) 20*pi*cos(20*pi*(x-t3)), t) .* (t >= t3);

v_total = v_basic + v_high + v_event1 + v_event2 + v_event3;
fd = 2 * v_total / lamda;  % 瞬时多普勒频率 (Hz)

figure('Position', [100, 100, 1000, 300]);
plot(t, v_total, 'k-', 'LineWidth', 2);
xlabel('时间 (s)'); ylabel('径向速度 (m/s)');
title('瞬时径向速度');
grid on;
xline(t1, 'g--', '速度阶跃');
xline(t2, 'm--', '加速度脉冲');
xline(t3, 'c--', '振荡开始');

% 绘制原始回波数据
figure('Position', [100, 100, 800, 600]);
imagesc(t, [1:N], 20*log10(abs(raw)/max(abs(raw(:)))));
xlabel('慢时间 (s)'); ylabel('快时间采样');
title('原始回波数据');
colorbar;
colormap('jet');
caxis([-40, 0]);

% 绘制散射点分布
figure('Position', [100, 100, 500, 500]);
plot(x, y, 'ro', 'MarkerSize', 6);
grid on;
xlabel('X (m)'); ylabel('Y (m)');
title('目标散射点分布');
axis equal;



%% ISAR成像处理


%%%%%%%%%%%%%%%%%%%%%%%%%%%%%包络对齐%%%%%%%%%%%%%%%%%%%%%%%%
%x1=fftshift(fft(fftshift(x1)));
%s3=fftshift(ifft(fftshift(TmpData)));
x1=RangeAlign_Corr(raw ,8,30);                 %以两包络相关系数最大为包络对齐标准（数据，插值，窗）
%x1=RangeAlign_MinEnt(s3,8,300,300);        %最小熵包络对齐（数据，插值，对齐窗，搜索窗）
% figure;
% plot(abs(x1));
% title('包络对齐后的一维距离像');
% 
% figure();
% imagesc(abs(x1));
% title('包络对齐的一维距离像');
%x1=InterPoleCumulateCorrelateAlignXcorrFast2(x1,8,30);
% figure('name','包络对齐后');
% imagesc(20*log10(abs(x1)./max(abs(x1(:)))));
% caxis([-30,0]);
% grid on;axis xy;colorbar;
% 
 %x1=InterPoleCumulateCorrelateAlignXcorrFast2(hrrp,8,30)
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%x1=hrrp(180:490,1350:1700);
x2=PhaseComp_MidTr(x1);  
miu = 1e-3;
P=150;

% 
 [ret_data fai]=PhaseComp_MinEntropy_NI(x2,miu,P);
% 
x3 = ret_data;
% 
%x3=CompensateByMultiProNo(x1,30);

Y=RDimaging(x3); %直接FFT
G1=20*log10(abs(Y)./max(abs(Y(:))));
figure('name','DCT成像结果');
imagesc(G1);caxis([-30,0]);
grid on;axis xy;colorbar;%axis equal;
% axis([-0.6 0.6 y(select_row(1)) y(select_row(end))]);%set(gca,'xtick',[-0.2 0 0.2]);
xlabel('azimuth');ylabel('range (m)');colormap jet;



%% 可视化结果
fprintf('生成可视化图形...\n');

% Figure 1: 散射点模型和运动参数
figure('Position', [100, 100, 1000, 800]);
subplot(3,1,1);
plot(t, R_basic, 'b--', 'LineWidth', 1.5); hold on;
plot(t, R_basic + R_high, 'r--', 'LineWidth', 1.5);
plot(t, R_t, 'k-', 'LineWidth', 2);
legend('基本运动 (二阶多项式)', '加高阶项', '总距离历程');
xlabel('时间 (s)'); ylabel('距离 (m)');
title('目标距离历程 R(t)');
grid on;

% 标记突变事件
xline(t1, 'g--', '速度阶跃');
xline(t2, 'm--', '加速度脉冲');
xline(t3, 'c--', '振荡开始');

% Figure 1 (continued): 瞬时速度
subplot(3,1,2);
plot(t, v_total, 'k-', 'LineWidth', 2);
xlabel('时间 (s)'); ylabel('速度 (m/s)');
title('瞬时速度');
grid on;
xline(t1, 'g--');
xline(t2, 'm--');
xline(t3, 'c--');

% Figure 1 (continued): 散射点轨迹在xz平面上的投影（显示旋转）



% Figure 3: 距离对齐和相位补偿效果

doppler_axis = linspace(-PRF/2, PRF/2, M);
cross_range_axis = doppler_axis * lamda / (2 * omega);  % 转换为距离

% Figure 5: 原始散射点和ISAR成像结果对比
figure('Position', [100, 100, 1000, 800]);

plot(x, y, 'o', 'MarkerSize', 4, 'MarkerFaceColor', 'b');
xlabel('X (m)'); ylabel('Y (m)');
title('原始散射点分布');
axis equal;
grid on;

figure;
imagesc(cross_range_axis, range_axis, 20*log10(abs(Y)/max(max(abs(Y)))));
xlabel('横向距离 (m)'); ylabel('距离 (m)');
title('ISAR成像结果');
colorbar;
colormap('jet');
caxis([-20, 0]);


fprintf('完成！\n'); 
