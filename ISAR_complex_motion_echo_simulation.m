%% ISAR复杂运动回波模型仿真
% 基于2015-01-30.md中的复杂运动模型，包含多项式项和突变事件
% 目标：多散射点飞机模型
% 回波矩阵大小：256x256
clear all; close all; clc;

%% 雷达参数设置
c = 3e8;                  % 光速 (m/s)
fc = 10e9;                % 载波频率 (Hz)
lambda = c/fc;            % 波长 (m)
Tp = 2e-6;                % 脉冲宽度 (s)
B = 800e6;                % 带宽 (Hz)
K = B/Tp;                 % 调频率 (Hz/s)
PRF = 500;                % 脉冲重复频率 (Hz)
PRI = 1/PRF;              % 脉冲重复间隔 (s)

%% 观测参数
M = 256;                  % 慢时间采样点数（脉冲数）
T = (M-1)*PRI;            % 总观测时间 (s)
t = (0:M-1)*PRI;          % 慢时间序列 (s)

%% 目标散射点设置 - 飞机模型（21个散射点）
% 散射点布局，模拟飞机形状
% 机身
x_body = [0, 2, 4, 6, 8, -2, -4, -6, -8];
y_body = [0, 0, 0, 0, 0, 0, 0, 0, 0];

% 机翼
x_wings = [3, 6, 9, 3, 6, 9, -3, -6, -9, -3, -6, -9];
y_wings = [3, 3, 3, -3, -3, -3, 3, 3, 3, -3, -3, -3];

% 合并所有散射点
x = [x_body, x_wings];
y = [y_body, y_wings];

L = length(x);            % 散射点数量
scatters = x + 1j*y;      % 复数表示的散射点位置
sigma = ones(1, L);       % 散射系数（简化为1）

%% 复杂运动模型参数设置
% 基础运动参数
R0 = 100e3;               % 初始距离 (m)
v0 = 120;                 % 初始径向速度 (m/s)
a0 = 5;                   % 初始加速度 (m/s^2)

% 高阶多项式系数
c3 = 0.8;                 % 三阶项系数
c4 = 0.1;                 % 四阶项系数
c5 = 0.02;                % 五阶项系数

% 突变事件设置 - 包含3个突变事件
% 1. 速度阶跃 - 在t1时刻速度突然增加
t1 = T/4;
A1 = 20;
g1 = @(t) (t >= 0);       % 阶跃函数

% 2. 加速度脉冲 - 在t2时刻短暂加速
t2 = T/2;
A2 = 30;
g2 = @(t) (t >= 0) .* exp(-50*t);  % 衰减脉冲

% 3. 抖动/振荡 - 在t3时刻开始振荡
t3 = 3*T/4;
A3 = 10;
g3 = @(t) (t >= 0) .* sin(20*pi*t);  % 正弦振荡

%% 距离历程计算 R(t)
% 基本运动项 (二阶多项式)
R_basic = R0 + v0*t + 0.5*a0*t.^2;

% 高阶多项式项
R_high = c3*t.^3 + c4*t.^4 + c5*t.^5;

% 突变项
R_event1 = A1 * g1(t - t1) .* (t - t1);  % 速度阶跃 (积分后为斜坡)
R_event2 = A2 * arrayfun(@(x) integral(@(t) g2(t-t2), 0, x), t);  % 加速度脉冲积分
R_event3 = A3 * arrayfun(g3, t - t3) .* (t >= t3);  % 振荡

% 总距离历程
R_t = R_basic + R_high + R_event1 + R_event2 + R_event3;

%% 瞬时多普勒频率及其导数计算（用于分析）
% 一阶导数 - 瞬时速度/多普勒
v_basic = v0 + a0*t;
v_high = 3*c3*t.^2 + 4*c4*t.^3 + 5*c5*t.^4;
v_event1 = A1 * g1(t - t1);
v_event2 = A2 * arrayfun(g2, t - t2) .* (t >= t2);
v_event3 = A3 * arrayfun(@(x) 20*pi*cos(20*pi*(x-t3)), t) .* (t >= t3);

v_total = v_basic + v_high + v_event1 + v_event2 + v_event3;
fd = 2 * v_total / lambda;  % 瞬时多普勒频率 (Hz)

% 二阶导数 - 瞬时加速度/多普勒率
a_basic = a0 * ones(size(t));
a_high = 6*c3*t + 12*c4*t.^2 + 20*c5*t.^3;
a_event1 = zeros(size(t));  % 阶跃函数的导数是冲激，这里简化为0
a_event2 = A2 * arrayfun(@(x) -50*exp(-50*(x-t2)), t) .* (t >= t2);
a_event3 = A3 * arrayfun(@(x) -(20*pi)^2*sin(20*pi*(x-t3)), t) .* (t >= t3);

a_total = a_basic + a_high + a_event1 + a_event2 + a_event3;
fd_rate = 2 * a_total / lambda;  % 多普勒率 (Hz/s)

%% 接收信号参数设置
% 快时间采样
max_extent = max(abs([x, y])) * sqrt(2);
ts_min = -2*max_extent/c - Tp/2;
ts_max = 2*max_extent/c + Tp/2;
Ns = 256;                 % 快时间采样点数（设置为256以获得256x256矩阵）
ts = linspace(ts_min, ts_max, Ns);  % 快时间序列

%% 回波生成
% 预分配内存
raw = zeros(Ns, M);

% 参考距离（用于去斜处理）
Rref = R_t;
tauref = 2*Rref/c;

% 生成回波信号
fprintf('生成回波信号...\n');
for m = 1:M
    if mod(m, 50) == 0
        fprintf('处理脉冲 %d/%d\n', m, M);
    end
    
    % 当前时刻的参考延时
    t_fast = ts + tauref(m);
    
    % 对每个散射点生成回波
    for l = 1:L
        % 散射点的绝对距离
        % 注意：这里考虑了散射点y坐标（虚部）作为径向分量
        R_scatter = R_t(m) + imag(scatters(l));
        tau = 2*R_scatter/c;
        
        % 时间窗
        win = (t_fast - tau >= -Tp/2) & (t_fast - tau < Tp/2);
        
        % 生成回波信号
        raw(:,m) = raw(:,m) + sigma(l) * win.' .* ...
            exp(1j*2*pi*fc*(t_fast-tau) + 1j*pi*K*(t_fast-tau).^2).';
    end
    
    % 去斜处理
    sref = exp(1j*2*pi*fc*(t_fast-tauref(m)) + 1j*pi*K*(t_fast-tauref(m)).^2);
    raw(:,m) = raw(:,m) .* sref';
end

%% 保存数据
save('ISAR_aircraft_complex_motion.mat', 'raw', 'R_t', 'fd', 'fd_rate', 'v_total', 'a_total', ...
     'scatters', 'PRF', 't', 'ts', 'fc', 'c', 'lambda', 'B');

%% 可视化
fprintf('生成可视化图形...\n');

% Figure 1: 距离历程及其组成部分
figure('Position', [100, 100, 1000, 800]);
subplot(3,1,1);
plot(t, R_basic, 'b--', 'LineWidth', 1.5); hold on;
plot(t, R_basic + R_high, 'r--', 'LineWidth', 1.5);
plot(t, R_t, 'k-', 'LineWidth', 2);
legend('基本运动 (二阶多项式)', '加高阶项', '总距离历程');
xlabel('时间 (s)'); ylabel('距离 (m)');
title('目标距离历程 R(t)');
grid on;

% 标记突变事件
xline(t1, 'g--', '速度阶跃');
xline(t2, 'm--', '加速度脉冲');
xline(t3, 'c--', '振荡开始');

% Figure 1 (continued): 分解各突变事件的贡献
subplot(3,1,2);
plot(t, R_event1, 'g-', 'LineWidth', 1.5); hold on;
plot(t, R_event2, 'm-', 'LineWidth', 1.5);
plot(t, R_event3, 'c-', 'LineWidth', 1.5);
legend('速度阶跃', '加速度脉冲', '振荡');
xlabel('时间 (s)'); ylabel('距离分量 (m)');
title('各突变事件的距离贡献');
grid on;

% Figure 1 (continued): 瞬时多普勒频率
subplot(3,1,3);
plot(t, fd, 'k-', 'LineWidth', 2);
xlabel('时间 (s)'); ylabel('频率 (Hz)');
title('瞬时多普勒频率');
grid on;
xline(t1, 'g--');
xline(t2, 'm--');
xline(t3, 'c--');

% Figure 2: 加速度/多普勒率
figure('Position', [100, 100, 800, 400]);
plot(t, a_total, 'k-', 'LineWidth', 2);
xlabel('时间 (s)'); ylabel('加速度 (m/s^2)');
title('瞬时加速度');
grid on;
xline(t1, 'g--', '速度阶跃');
xline(t2, 'm--', '加速度脉冲');
xline(t3, 'c--', '振荡开始');

% Figure 3: 原始回波数据
figure('Position', [100, 100, 800, 600]);
subplot(2,1,1);
imagesc(t, ts*c/2, abs(raw));
xlabel('慢时间 (s)'); ylabel('距离 (m)');
title('回波数据幅度');
colorbar;
colormap('jet');

% 距离剖面（第一个脉冲）
subplot(2,1,2);
plot(ts*c/2, abs(raw(:,1)), 'LineWidth', 1.5);
xlabel('距离 (m)'); ylabel('幅度');
title('第一个脉冲的距离剖面');
grid on;

% Figure 4: 时频分析 - 使用短时傅里叶变换 (STFT)
figure('Position', [100, 100, 800, 600]);
% 选取一个距离单元进行时频分析
[~, idx] = max(sum(abs(raw), 2));  % 选择能量最强的距离单元
range_bin_data = raw(idx, :);

% 应用STFT
window_length = 32;
overlap = 30;
nfft = 256;
[S, F, T] = spectrogram(range_bin_data, hamming(window_length), overlap, nfft, PRF, 'yaxis');

% 绘制时频图
subplot(2,1,1);
imagesc(T, F-PRF/2, 20*log10(abs(S)/max(max(abs(S)))));
xlabel('时间 (s)'); ylabel('频率 (Hz)');
title('距离单元的时频分析 (STFT)');
colorbar;
colormap('jet');
caxis([-40, 0]);

% 绘制理论多普勒频率曲线进行对比
subplot(2,1,2);
pcolor(t, (-PRF/2:PRF/(nfft-1):PRF/2-PRF/nfft), repmat(20*log10(abs(fftshift(fft(range_bin_data, nfft)))/max(abs(fft(range_bin_data, nfft)))), length(t), 1)');
shading interp;
hold on;
plot(t, fd-mean(fd), 'r-', 'LineWidth', 2);
xlabel('时间 (s)'); ylabel('频率 (Hz)');
title('瞬时多普勒频率与频谱对比');
colorbar;
colormap('jet');
caxis([-40, 0]);

fprintf('完成！\n'); 