% ISAR回波信号模型 - 修正版本（含突变修正与坐标投影）
clear all; close all; clc;

%% 基本参数
c = 3e8;               
fc = 10e9;             
lambda = c/fc;         
Tp = 2e-6;             
B = 800e6;             
K = B/Tp;              
PRF = 1000;            
PRI = 1/PRF;          

%% 目标散射点（假设雷达视线沿y轴）
x = [0, 5, -5, 10, -10, -8, 3, -3, 0];  
y = [10, 5, 5, 5, 5, -8, 0, 0, 0];       % y坐标为距离向分量
scatters = x + 1j*y;                     
sigma = ones(1, length(x));              

%% 观测时间
T = 1;                 
M = T * PRF;           
t = (0:M-1) * PRI;     

%% 目标运动模型修正
R0 = 100e3;            
v0 = 100;              
a0 = 5;                
c3 = 0.1;              
c4 = 0.05;             

% 修正突变项：阶跃函数
A1 = 10;               
t1 = T/2;              
g1 = @(t) (t >= 0);                   % 阶跃函数定义
R_mutation = A1 * g1(t - t1);         % 突变时刻增加固定偏移

% 距离历程
R_t = R0 + v0*t + 0.5*a0*t.^2 + c3*t.^3 + c4*t.^4 + R_mutation;

%% 快时间采样（考虑散射点最大距离变化）
max_y = max(abs(y));                   % 最大距离向分量
ts_min = -2*(max_y)/c - Tp/2;          % 修正采样范围
ts_max = 2*(max_y)/c + Tp/2;           
fs = 2*B;                              
ts = ts_min:1/fs:ts_max;               
N = length(ts);

%% 回波生成（修正散射点投影）
raw = zeros(N, M);
Rref = R_t;
taoref = 2*Rref/c;

for m = 1:M
    t_fast = ts + taoref(m);
    for l = 1:length(scatters)
        % 修正：使用散射点y坐标（虚部）作为距离向分量
        R_scatter = R_t(m) + imag(scatters(l));  
        tao = 2*R_scatter / c;
        win = (t_fast - tao >= -Tp/2) & (t_fast - tao < Tp/2);
        raw(:,m) = raw(:,m) + sigma(l) * win.' .* ...
            exp(1j*2*pi*fc*(t_fast-tao) + 1j*pi*K*(t_fast-tao).^2).';
    end
    sref = exp(1j*2*pi*fc*(t_fast-taoref(m)) + 1j*pi*K*(t_fast-taoref(m)).^2);
    raw(:,m) = raw(:,m) .* sref';
end

%% 保存与可视化
save('ISAR_aircraft_corrected.mat', 'raw', 'R_t', 'scatters', 'PRF', 'fs', 'fc');
figure; plot(t, R_t); xlabel('时间 (s)'); ylabel('距离 (m)'); title('目标距离历程');
figure; imagesc(abs(raw)); xlabel('慢时间'); ylabel('快时间'); title('回波数据幅度');