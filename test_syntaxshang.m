% 主脚本，用于运行ISAR回波仿真和深度融合的VMD-ADMM-DCFT成像处理

clear; close all; clc;

fprintf('开始ISAR成像仿真与深度融合处理...\n');

% -------------------- 1. 数据加载与仿真参数 -------------------- %

fprintf('加载/生成雷达回波数据...\n');

tic;

% 尝试加载实际数据，如果不存在则生成仿真数据

try

load shipx2.mat; % 加载实际数据 shipx2_1000

load s_r_tm2.mat

echo_data = shipx2;

fprintf('实际数据 shipx2_1000.mat 加载成功。\n');

% 为实际数据设置一个基础的sim_params结构体 (部分参数可能需要根据实际数据特性调整)

sim_params = struct();

sim_params.Num_r = size(echo_data, 1);

sim_params.Num_tm = size(echo_data, 2);

sim_params.PRF = 1400; % 假设值, PRF for shipx2_1000 if known, else adjust

sim_params.fc = 5.2e9; % 假设值, Carrier frequency

sim_params.c = 3e8;

sim_params.B = 80e6; % 假设值, Bandwidth

delta_r_res_actual = sim_params.c / (2*sim_params.B);

% 假设目标中心在0米距离，根据距离单元数量和分辨率设定距离轴

% 这部分需要根据实际数据的距离信息进行精确设置

r_center_actual = 0; % 假设目标中心距离

sim_params.r_axis = linspace(r_center_actual - (sim_params.Num_r/2)*delta_r_res_actual, r_center_actual + (sim_params.Num_r/2-1)*delta_r_res_actual, sim_params.Num_r);

sim_params.tm = linspace(0, (sim_params.Num_tm-1)/sim_params.PRF, sim_params.Num_tm);

catch

fprintf('未找到 shipx2_1000.mat，生成仿真数据...\n');

[echo_data, sim_params] = generate_simulated_echo(); % 保留仿真数据生成作为备选

end

fprintf('数据加载/生成完毕。耗时: %.2f 秒\n', toc);

fprintf('回波数据尺寸: %d (距离单元) x %d (方位单元)\n', size(echo_data, 1), size(echo_data, 2));

% -------------------- 2. 设置处理参数 -------------------- %

params_proc = struct();

% VMD 参数 (用于ADMM子问题)

params_proc.vmd.K = 3; % 模态数量 (经验值, 可调整)

params_proc.vmd.alpha_vmd = 4500; % 带宽约束强度 (VMD内部的alpha)

params_proc.vmd.tau_vmd = 0; % 拉格朗日乘子更新率 (0表示无噪声，经典VMD中)

params_proc.vmd.tol_vmd_inner = 1e-4; % VMD内部迭代收敛容限

params_proc.vmd.max_iter_vmd_inner = 2000; % 5 %% VMD内部最大迭代次数 (在ADMM每一步中)

params_proc.vmd.init_omega_method = 'peaks_robust'; % 'peaks' 或 'linear'

params_proc.vmd.alpha_phase_guidance =7.5; % 0.5; VMD引导中相位先验的权重 (用于VMD更新)

% 相位估算 (DCFT-like, 用于ADMM子问题) 参数

params_proc.phase_est.poly_order = 3; % 3 估计的相位多项式阶数 (fd, ka, kb)

params_proc.phase_est.fd_search_range_factor = 0.5; % 0.5 fd 搜索范围相对于PRF/2

params_proc.phase_est.ka_search_pts = 31; % ka 搜索点数 (1D搜索) - 减少以提高效率

params_proc.phase_est.kb_search_pts = 31; % kb 搜索点数 (1D搜索) - 减少以提高效率

params_proc.phase_est.sharpness_weight = 2; % 新增：相位估计中尖锐度的权重

params_proc.phase_est.num_refinement_passes = 500; % 新增：相位估计算法的迭代优化次数

% 全局 ADMM 参数 (用于整体优化框架)

params_proc.admm_global.rho_X = 1.0; % 1.0增广拉格朗日参数 (针对 X=FFT(S_comp) 的约束)

params_proc.admm_global.rho_U = 0.5; % 0.5增广拉格朗日参数 (针对 u_k 分解的约束, s = sum(u_k))

params_proc.admm_global.rho_P = 0.5; % 0.5增广拉格朗日参数 (针对相位平滑或先验的约束, 可选)

params_proc.admm_global.lambda_sparsity = 7; % 0.05稀疏正则化权重 (针对 X)

params_proc.admm_global.max_iter = 25; % 5ADMM最大全局迭代次数 - 保持较低以提高效率

params_proc.admm_global.tol = 1e-4; % 1e-3 ADMM全局收敛容限

params_proc.admm_global.alpha_data_fidelity = 3.0; % 1.0数据保真项 ||s - sum(u_k)||^2 的权重

params_proc.admm_global.alpha_phase_sharpness = 0.9; % 0.1相位聚焦项权重

% 其他处理参数

params_proc.apply_azimuth_window = true; % 新增: 是否应用方位向窗函数

params_proc.num_azimuth = sim_params.Num_tm; % 方位单元数

params_proc.num_range_bins = sim_params.Num_r; % 距离单元数

params_proc.PRF = sim_params.PRF;

params_proc.fc = sim_params.fc;

params_proc.c = sim_params.c;

params_proc.tm_azimuth = sim_params.tm; % 慢时间轴

params_proc.normalized_tm = (0:params_proc.num_azimuth-1) / params_proc.num_azimuth; % 归一化慢时间

% -------------------- 3. 执行深度融合ISAR成像算法 -------------------- %

fprintf('开始执行深度融合VMD-ADMM-DCFT ISAR成像算法...\n');

tic;

[ISAR_image_fused, dominant_mode_compensated_fft, vmd_modes_all_bins, phase_coeffs_all_bins, admm_convergence_all_bins] = perform_isar_imaging_fused_admm(echo_data, params_proc, sim_params);

fprintf('深度融合ISAR成像处理完毕。耗时: %.2f 秒\n', toc);

% -------------------- 4. 显示结果 -------------------- %

fprintf('显示成像结果...\n');

% --- 原始数据和直接FFT ---

figure('Name', '原始数据和直接FFT');

subplot(1,2,1);

imagesc(sim_params.tm, sim_params.r_axis, abs(echo_data));

xlabel('慢时间 (秒)'); ylabel('距离 (米)'); title('距离压缩后的原始回波'); colorbar; axis xy;

raw_fft = fftshift(fft(echo_data, [], 2), 2);

doppler_axis = linspace(-params_proc.PRF/2, params_proc.PRF/2, params_proc.num_azimuth);

subplot(1,2,2);

imagesc(doppler_axis, sim_params.r_axis, abs(raw_fft));

xlabel('多普勒频率 (Hz)'); ylabel('距离 (米)'); title('原始数据直接FFT'); colorbar; axis xy;

% --- 融合算法成像结果 ---

ISAR_image_fused_shifted = fftshift(ISAR_image_fused,2);

% ISAR_image_fused_shifted = fftshift(ISAR_image_fused_shifted,1);
figure('Name', '深度融合ADMM成像结果');

imagesc(doppler_axis, sim_params.r_axis, abs(ISAR_image_fused_shifted));

xlabel('多普勒频率 (Hz)'); ylabel('距离 (米)'); title('深度融合VMD-ADMM-DCFT ISAR结果'); colorbar; axis xy;

% --- 对数尺度显示 ---

figure('Name', '对数尺度对比');

G_raw = 20*log10(abs(raw_fft)./max(abs(raw_fft(:))));

imagesc(doppler_axis, sim_params.r_axis, G_raw); caxis([-30,0]);

xlabel('多普勒频率 (Hz)'); ylabel('距离 (米)'); title('直接FFT (dB)'); colorbar; axis xy; colormap('jet');

figure('Name', '对数尺度对比');

G_fused = 20*log10(abs(ISAR_image_fused_shifted)./max(abs(ISAR_image_fused_shifted(:))));

imagesc(doppler_axis, sim_params.r_axis, G_fused); caxis([-30,0]);

xlabel('多普勒频率 (Hz)'); ylabel('距离 (米)'); title('增强型融合ADMM (dB)'); colorbar; axis xy; colormap('jet');

% --- (可选) 主导模态补偿 + FFT 结果 (如果算法输出) ---

if exist('dominant_mode_compensated_fft', 'var') && ~isempty(dominant_mode_compensated_fft)

ISAR_image_fft_dominant_shifted = fftshift(dominant_mode_compensated_fft, 2);

figure('Name', '对数尺度对比');

G_dominant = 20*log10(abs(ISAR_image_fft_dominant_shifted)./max(abs(ISAR_image_fft_dominant_shifted(:))+eps));

imagesc(doppler_axis, sim_params.r_axis, G_dominant); caxis([-40,0]);

xlabel('多普勒频率 (Hz)'); ylabel('距离 (米)'); title('主导模态补偿+FFT (dB)'); colorbar; axis xy; colormap('jet');

end

% --- 图像质量指标 ---

contrast_fused = calculate_image_contrast(abs(ISAR_image_fused_shifted));

entropy_fused = calculate_image_entropy(abs(ISAR_image_fused_shifted));

contrast_raw = calculate_image_contrast(abs(raw_fft));

entropy_raw = calculate_image_entropy(abs(raw_fft));

fprintf('直接FFT图像质量指标:\n');

fprintf(' - 对比度: %.4f\n', contrast_raw);

fprintf(' - 熵: %.4f\n', entropy_raw);

fprintf('深度融合ADMM图像质量指标:\n');

fprintf(' - 对比度: %.4f\n', contrast_fused);

fprintf(' - 熵: %.4f\n', entropy_fused);

% --- 中间过程可视化 ---

% 选择一个有代表性的距离单元进行可视化 (例如，能量最大的或者中间的)

[~, r_idx_vis] = max(sum(abs(echo_data).^2, 2));

if isempty(r_idx_vis), r_idx_vis = round(params_proc.num_range_bins/2); end

% VMD分解结果

if ~isempty(vmd_modes_all_bins) && r_idx_vis <= length(vmd_modes_all_bins) && ~isempty(vmd_modes_all_bins{r_idx_vis})

u_k_vis = vmd_modes_all_bins{r_idx_vis};

figure('Name', sprintf('VMD模态 (距离单元 %d)', r_idx_vis));

for k_plot = 1:size(u_k_vis,1)

subplot(size(u_k_vis,1), 1, k_plot);

plot(params_proc.tm_azimuth, real(u_k_vis(k_plot,:)));

title(sprintf('模态 %d (实部)', k_plot)); xlabel('慢时间 (s)');

end

end

% DCFT相位估计过程 (示例：绘制补偿前后的一个模态的频谱)

if ~isempty(phase_coeffs_all_bins) && r_idx_vis <= length(phase_coeffs_all_bins) && ~isempty(phase_coeffs_all_bins{r_idx_vis})

coeffs_vis = phase_coeffs_all_bins{r_idx_vis}; % K x poly_order

u_k_for_phase_vis = vmd_modes_all_bins{r_idx_vis}; % 使用最终的模态


if ~isempty(u_k_for_phase_vis)

k_mode_to_plot = 1; % 选择第一个模态进行演示

mode_signal = u_k_for_phase_vis(k_mode_to_plot, :);

phase_poly_est = construct_phase_poly(params_proc.normalized_tm, coeffs_vis(k_mode_to_plot,:));

mode_compensated = mode_signal .* exp(-1j * phase_poly_est);

figure('Name', sprintf('DCFT相位补偿效果 (距离单元 %d, 模态 %d)', r_idx_vis, k_mode_to_plot));

subplot(2,1,1);

plot(doppler_axis, abs(fftshift(fft(mode_signal))));

title('补偿前模态频谱'); xlabel('多普勒频率 (Hz)');

subplot(2,1,2);

plot(doppler_axis, abs(fftshift(fft(mode_compensated))));

title('补偿后模态频谱'); xlabel('多普勒频率 (Hz)');

end

end

% ADMM迭代收敛

if ~isempty(admm_convergence_all_bins) && r_idx_vis <= length(admm_convergence_all_bins) && ~isempty(admm_convergence_all_bins{r_idx_vis})

conv_data = admm_convergence_all_bins{r_idx_vis};

figure('Name', sprintf('ADMM收敛曲线 (距离单元 %d)', r_idx_vis));

semilogy(1:length(conv_data.primal_res), conv_data.primal_res, 'b-o', 'DisplayName', 'Primal Residual'); hold on;

semilogy(1:length(conv_data.dual_res), conv_data.dual_res, 'r-x', 'DisplayName', 'Dual Residual'); hold off;

xlabel('ADMM迭代次数'); ylabel('残差'); title('ADMM收敛性'); legend; grid on;

end

% 时频分析对比 (原始信号 vs. 最终补偿叠加信号)

signal_orig_vis = echo_data(r_idx_vis, :);

final_compensated_signal_vis = ifft(ISAR_image_fused(r_idx_vis,:)); % 从最终图像频谱反变换

figure('Name', sprintf('时频分析对比 (距离单元 %d)', r_idx_vis));

subplot(1,2,1);

spectrogram(signal_orig_vis, hamming(64), 32, 256, params_proc.PRF, 'yaxis'); % 参数可调整

title('原始信号时频图');

subplot(1,2,2);

spectrogram(final_compensated_signal_vis, hamming(64), 32, 256, params_proc.PRF, 'yaxis');

title('最终补偿后信号时频图');

fprintf('所有可视化完成。\n');

% perform_isar_imaging_fused_admm.m

% 实现深度融合的VMD-ADMM-DCFT ISAR成像算法

% ADMM作为核心优化框架，迭代更新VMD模态、相位系数和稀疏ISAR图像

function [ISAR_image_sparse, s_compensated_dominant_mode_fft, vmd_modes_all_bins, phase_coeffs_all_bins, admm_convergence_all_bins] = perform_isar_imaging_fused_admm(radar_data, params_proc, sim_params)

% 1. 参数初始化

[num_range_bins, num_azimuth] = size(radar_data);

fprintf(' 处理数据尺寸: %d x %d\n', num_range_bins, num_azimuth);

% 从params_proc中提取参数

K_vmd = params_proc.vmd.K;

poly_order_phase = params_proc.phase_est.poly_order;

tm_normalized = params_proc.normalized_tm; % VMD和相位估计内部使用的归一化时间

% ADMM全局参数

rho_X = params_proc.admm_global.rho_X; % ADMM rho for X update

rho_U = params_proc.admm_global.rho_U; % ADMM rho for U update (mode decomposition fidelity)

lambda_sparsity = params_proc.admm_global.lambda_sparsity;

max_admm_iter = params_proc.admm_global.max_iter;

admm_tol = params_proc.admm_global.tol;

sharpness_weight = params_proc.phase_est.sharpness_weight;

apply_azimuth_window_flag = params_proc.apply_azimuth_window;

% 初始化输出

ISAR_image_sparse = zeros(num_range_bins, num_azimuth, 'like', 1j*radar_data(1));

s_compensated_dominant_mode_fft = zeros(num_range_bins, num_azimuth, 'like', 1j*radar_data(1)); % 用于对比的非稀疏结果的FFT

% 初始化存储中间结果的cell数组 (用于可视化)

vmd_modes_all_bins = cell(num_range_bins, 1);

phase_coeffs_all_bins = cell(num_range_bins, 1);

admm_convergence_all_bins = cell(num_range_bins, 1);

% 方位窗函数
azimuth_window = ones(1, num_azimuth);
if apply_azimuth_window_flag
    azimuth_window = hamming(num_azimuth)'; 
end

% 2. 主循环: 处理每个距离单元 (使用parfor并行处理)

fprintf(' 开始逐距离单元处理 (共 %d 个)...\n', num_range_bins);

parfor r_idx = 1:num_range_bins

if mod(r_idx, round(num_range_bins/10)) == 0 && r_idx > 1

fprintf(' 正在处理距离单元: %d/%d\n', r_idx, num_range_bins);

end


signal_orig = radar_data(r_idx, :); % 当前距离单元的信号


% 跳过能量过低的距离单元

if sum(abs(signal_orig).^2) < 1e-12 * num_azimuth

ISAR_image_sparse(r_idx, :) = fft(signal_orig .* azimuth_window);

s_compensated_dominant_mode_fft(r_idx, :) = fft(signal_orig .* azimuth_window);

continue;

end


% 对当前信号进行归一化和去均值，避免数值问题

signal_norm_factor = max(abs(signal_orig));

if signal_norm_factor < eps, signal_norm_factor = 1; end

signal = signal_orig / signal_norm_factor;

signal = signal - mean(signal);

% -------- ADMM 迭代变量初始化 --------

% VMD模态 u_k(t)

u_k = zeros(K_vmd, num_azimuth, 'like', 1j*signal(1));

% VMD模态中心频率 omega_k (归一化)

omega_k = zeros(K_vmd, 1);

fft_signal_abs = abs(fft(signal));

valid_omega_idx = 0;

if strcmp(params_proc.vmd.init_omega_method, 'peaks_robust')
    [pks, locs] = findpeaks(fft_signal_abs, 'SortStr', 'descend');
    temp_omega_init_vals = zeros(K_vmd,1);
    min_freq_sep = 0.05; 
    last_added_omega_val = -inf;
    for i_pk = 1:length(locs)
        current_omega_val = (locs(i_pk)-1)/num_azimuth;
        is_dc_or_nyquist = abs(current_omega_val) < 0.01 || abs(current_omega_val - 0.5) < 0.01 || abs(current_omega_val - 1.0) < 0.01;
        if K_vmd == 1 || ~is_dc_or_nyquist || (K_vmd > 1 && length(pks) == 1)
           if abs(current_omega_val - last_added_omega_val) > min_freq_sep || valid_omega_idx == 0
                valid_omega_idx = valid_omega_idx + 1;
                temp_omega_init_vals(valid_omega_idx) = current_omega_val;
                last_added_omega_val = current_omega_val;
                if valid_omega_idx == K_vmd, break; end
           end
        end
    end
    if valid_omega_idx > 0
         omega_k(1:valid_omega_idx) = temp_omega_init_vals(1:valid_omega_idx);
    end
elseif strcmp(params_proc.vmd.init_omega_method, 'peaks')
    [~, locs] = findpeaks(fft_signal_abs, 'SortStr', 'descend', 'NPeaks', K_vmd);
    if ~isempty(locs)
        valid_locs_count = min(length(locs), K_vmd);
        omega_k(1:valid_locs_count) = (locs(1:valid_locs_count)-1)/num_azimuth;
        valid_omega_idx = valid_locs_count;
    end
else % linear
     for k_idx_init_loop = 1:K_vmd, omega_k(k_idx_init_loop) = (k_idx_init_loop-1)/(K_vmd); end
     valid_omega_idx = K_vmd;
end
if valid_omega_idx < K_vmd 
    unset_indices = (valid_omega_idx+1):K_vmd;
    if ~isempty(unset_indices)
         omega_k(unset_indices) = linspace(0.1, 0.4, length(unset_indices))';
    end
end

% 相位多项式系数 p_k 和相位本身
poly_coeffs_k = zeros(K_vmd, poly_order_phase); 
estimated_phases_k = zeros(K_vmd, num_azimuth, 'like', 1j*signal(1)); 
if poly_order_phase >= 1
    for k_init_phase = 1:K_vmd
        fd_norm_init = omega_k(k_init_phase);
        if fd_norm_init > 0.5, fd_norm_init = fd_norm_init - 1; end
        poly_coeffs_k(k_init_phase, 1) = fd_norm_init;
        estimated_phases_k(k_init_phase, :) = construct_phase_poly(tm_normalized, poly_coeffs_k(k_init_phase,:));
    end
end

% 稀疏ISAR图像频谱 X(f)
X_sparse_spectrum = fft(signal .* azimuth_window); % 初始为直接FFT

% ADMM辅助变量和拉格朗日乘子
Z_aux_X = X_sparse_spectrum;
Y_lagrange_X = zeros(size(X_sparse_spectrum), 'like', 1j*signal(1));
Y_lagrange_U = zeros(size(signal), 'like', 1j*signal(1));

admm_iter_data = struct('primal_res_X', zeros(1,max_admm_iter), 'dual_res_X', zeros(1,max_admm_iter), ...
                        'primal_res_U', zeros(1,max_admm_iter), 'dual_res_U', zeros(1,max_admm_iter));
u_k_prev_for_dual_U = u_k; 

% -------- ADMM 主迭代循环 --------
for iter_admm = 1:max_admm_iter

    Z_aux_X_prev = Z_aux_X;
    
    % 1. 更新 VMD 模态 u_k 和中心频率 omega_k (子问题)
    target_signal_for_vmd = signal + Y_lagrange_U / rho_U;
    target_signal_for_vmd = target_signal_for_vmd - mean(target_signal_for_vmd);
    
    current_phase_models_for_vmd = estimated_phases_k; 
    [u_k, omega_k] = update_modes_admm(target_signal_for_vmd, u_k, omega_k, current_phase_models_for_vmd, params_proc, rho_U);

    % 2. 迭代更新相位多项式系数 p_k (子问题)
    S_reconstructed_from_modes_new = sum(u_k, 1);
    X_target_global = X_sparse_spectrum + Y_lagrange_X / rho_X;
    
    % 计算除当前更新模态外的其他所有模态的补偿后频谱和
    temp_sum_compensated_others_fft = zeros(1, num_azimuth, 'like', 1j*signal(1));
    for k_other_init = 1:K_vmd 
        uk_other_processed = u_k(k_other_init,:) - mean(u_k(k_other_init,:));
        temp_sum_compensated_others_fft = temp_sum_compensated_others_fft + ...
            fft( uk_other_processed .* exp(-1j * estimated_phases_k(k_other_init,:)) .* azimuth_window );
    end

    new_poly_coeffs_k = poly_coeffs_k; 
    new_estimated_phases_k = estimated_phases_k;

    % 逐个模态更新相位
    for k_update_phase = 1:K_vmd
        uk_current_mode_processed = u_k(k_update_phase,:) - mean(u_k(k_update_phase,:));
        % 从总和中减去当前模态（使用旧相位）的贡献
        current_uk_compensated_fft_old_phase = fft(uk_current_mode_processed .* exp(-1j * estimated_phases_k(k_update_phase,:)) .* azimuth_window);
        Residual_Target_Spectrum_k = X_target_global - (temp_sum_compensated_others_fft - current_uk_compensated_fft_old_phase);

        % 使用增强方法更新当前模态的相位
        [coeffs_k, phase_val_k] = update_phase_coeffs_admm_enhanced(...
            uk_current_mode_processed, ...
            poly_coeffs_k(k_update_phase,:), ...
            Residual_Target_Spectrum_k, ...
            params_proc, tm_normalized, sharpness_weight, azimuth_window);
        
        new_poly_coeffs_k(k_update_phase,:) = coeffs_k;
        new_estimated_phases_k(k_update_phase,:) = phase_val_k;
        
        % 更新总和：减去旧相位贡献，加上新相位贡献
        temp_sum_compensated_others_fft = temp_sum_compensated_others_fft - current_uk_compensated_fft_old_phase ...
                                        + fft(uk_current_mode_processed .* exp(-1j*phase_val_k) .* azimuth_window);
    end
    poly_coeffs_k = new_poly_coeffs_k;
    estimated_phases_k = new_estimated_phases_k;

    % 3. 构造补偿后的叠加信号 S_comp(t)
    s_compensated_time = zeros(1, num_azimuth, 'like', signal(1));
    for k_idx = 1:K_vmd
        s_compensated_time = s_compensated_time + (u_k(k_idx,:) - mean(u_k(k_idx,:))) .* exp(-1j * estimated_phases_k(k_idx,:));
    end
    s_compensated_time = s_compensated_time - mean(s_compensated_time);
    S_compensated_fft = fft(s_compensated_time .* azimuth_window); 

    % 4. 更新稀疏ISAR图像频谱 X(f)
    X_sparse_spectrum = (S_compensated_fft + rho_X * (Z_aux_X - Y_lagrange_X/rho_X)) / (1 + rho_X);

    % 5. 更新辅助变量 Z_X (稀疏投影)
    Z_aux_X = soft_threshold(X_sparse_spectrum + Y_lagrange_X/rho_X, lambda_sparsity/rho_X);

    % 6. 更新拉格朗日乘子 Y_X 和 Y_U
    Y_lagrange_X = Y_lagrange_X + rho_X * (X_sparse_spectrum - Z_aux_X);
    Y_lagrange_U = Y_lagrange_U + rho_U * (signal - S_reconstructed_from_modes_new);

    % 收敛检查
    norm_X_prev = norm(X_sparse_spectrum); if norm_X_prev < eps, norm_X_prev = 1; end
    norm_Y_X_curr = norm(Y_lagrange_X); if norm_Y_X_curr < eps, norm_Y_X_curr = 1; end
    norm_signal_curr = norm(signal); if norm_signal_curr < eps, norm_signal_curr = 1; end
    norm_Y_U_curr = norm(Y_lagrange_U); if norm_Y_U_curr < eps, norm_Y_U_curr = 1; end

    primal_res_X = norm(X_sparse_spectrum - Z_aux_X) / norm_X_prev;
    dual_res_X = rho_X * norm(Z_aux_X - Z_aux_X_prev) / norm_Y_X_curr;
    primal_res_U = norm(signal - S_reconstructed_from_modes_new) / norm_signal_curr;
    dual_res_U = rho_U * norm(sum(u_k,1) - sum(u_k_prev_for_dual_U,1)) / norm_Y_U_curr; 
    
    admm_iter_data.primal_res_X(iter_admm) = primal_res_X;
    admm_iter_data.dual_res_X(iter_admm) = dual_res_X;
    admm_iter_data.primal_res_U(iter_admm) = primal_res_U;
    admm_iter_data.dual_res_U(iter_admm) = dual_res_U;
    u_k_prev_for_dual_U = u_k;

    if iter_admm > 1 && ((primal_res_X < admm_tol && dual_res_X < admm_tol) && (primal_res_U < admm_tol && dual_res_U < admm_tol) )
        break;
    end
end % end ADMM iteration


ISAR_image_sparse(r_idx, :) = X_sparse_spectrum * signal_norm_factor; % 恢复幅度


% 存储中间结果
vmd_modes_all_bins{r_idx} = u_k * signal_norm_factor;
phase_coeffs_all_bins{r_idx} = poly_coeffs_k;
admm_convergence_all_bins{r_idx} = admm_iter_data;

% (可选) 保存用主导模态相位补偿的原始信号，用于对比
mode_energies = sum(abs(u_k).^2, 2);
[~, dominant_idx_candidates] = sort(mode_energies, 'descend');
if ~isempty(dominant_idx_candidates)
    dominant_idx = dominant_idx_candidates(1);
    dominant_phase_compensation = construct_phase_poly(tm_normalized, poly_coeffs_k(dominant_idx, :));
    s_comp_dom_mode_time = (signal_orig - mean(signal_orig)) .* exp(-1j * dominant_phase_compensation); 
    s_comp_dom_mode_time = s_comp_dom_mode_time - mean(s_comp_dom_mode_time);
    s_compensated_dominant_mode_fft(r_idx, :) = fft(s_comp_dom_mode_time .* azimuth_window);
else
     s_comp_dom_mode_time_fallback = signal_orig - mean(signal_orig);
     s_compensated_dominant_mode_fft(r_idx, :) = fft(s_comp_dom_mode_time_fallback .* azimuth_window);
end
end % end parfor r_idx

fprintf(' 所有距离单元处理完毕。\n');

end

% 辅助函数: 软阈值 (与原代码一致)
function y = soft_threshold(x, threshold_val)

y = sign(x) .* max(abs(x) - threshold_val, 0);

end

% 辅助函数: 构造相位多项式
function phase_poly = construct_phase_poly(tm_normalized, coeffs)

poly_order = length(coeffs);

phase_poly = zeros(size(tm_normalized));

if poly_order >= 1 && ~isnan(coeffs(1))
    phase_poly = phase_poly + 2*pi * coeffs(1) * tm_normalized;
end
if poly_order >= 2 && ~isnan(coeffs(2))
    phase_poly = phase_poly + 2*pi * 0.5 * coeffs(2) * tm_normalized.^2;
end
if poly_order >= 3 && ~isnan(coeffs(3))
    phase_poly = phase_poly + 2*pi * (1/6) * coeffs(3) * tm_normalized.^3;
end
% 可以根据需要扩展到更高阶
end

% update_modes_admm.m
% ADMM子问题：更新VMD模态 u_k 和中心频率 omega_k (增强版)
function [u_k_updated, omega_k_updated] = update_modes_admm(target_signal_for_vmd, u_k_prev, omega_k_prev, phase_models_k, params_proc, rho_U)

% VMD内部参数
alpha_vmd = params_proc.vmd.alpha_vmd; 
K = params_proc.vmd.K;
tol_vmd_inner = params_proc.vmd.tol_vmd_inner;
max_iter_vmd_inner = params_proc.vmd.max_iter_vmd_inner;
alpha_phase_guidance = params_proc.vmd.alpha_phase_guidance; 

N = length(target_signal_for_vmd);
target_signal_fft = fft(target_signal_for_vmd); 
f_axis_normalized = params_proc.normalized_tm; % 归一化频率轴 (0 to N-1)/N

% 初始化
u_k = u_k_prev;
omega_k = omega_k_prev;
u_k_fft = zeros(K, N, 'like', 1j*target_signal_fft(1));
for k_idx = 1:K
    u_k_fft(k_idx,:) = fft(u_k(k_idx,:));
end

u_sum_fft_prev_iter_start_loop = sum(u_k_fft,1); 

% VMD 子迭代
for iter_inner = 1:max_iter_vmd_inner
    for k_idx = 1:K
        % 计算除当前模态外的所有模态之和的fft
        sum_other_modes_fft = sum(u_k_fft,1) - u_k_fft(k_idx,:); 
        
        % 在频域更新 u_k
        numerator_fft = target_signal_fft - sum_other_modes_fft;
        denominator = 1 + 2*alpha_vmd*(f_axis_normalized - omega_k(k_idx)).^2;
        
        % 加入相位引导 (如果提供)
        if alpha_phase_guidance > 0 && ~isempty(phase_models_k) && size(phase_models_k,1) >= k_idx && any(phase_models_k(k_idx,:)) && ~all(isnan(phase_models_k(k_idx,:)))
            phase_prior_signal_time = exp(1j * phase_models_k(k_idx,:));
            % --- 关键修改：去掉均值以抑制相位先验造成的直流泄漏，避免中心亮线 ---
            phase_prior_signal_time = phase_prior_signal_time - mean(phase_prior_signal_time);
            phase_prior_term_fft = fft(phase_prior_signal_time);
            % --- 关键修改：移除alpha_vmd的过度缩放 ---
            numerator_fft = numerator_fft + alpha_phase_guidance * phase_prior_term_fft; 
            denominator = denominator + alpha_phase_guidance;
        end
        
        u_k_fft(k_idx,:) = numerator_fft ./ denominator;
        
        % 更新中心频率 omega_k (重心法)
        power_spectrum_uk = abs(u_k_fft(k_idx,:)).^2;
        if sum(power_spectrum_uk) > 1e-12
            omega_k(k_idx) = sum(f_axis_normalized .* power_spectrum_uk) / sum(power_spectrum_uk);
            omega_k(k_idx) = mod(omega_k(k_idx), 1); % 确保频率在[0,1]
        end
    end % end k_idx loop
    
    u_k = ifft(u_k_fft, [], 2); % 更新所有时域模态
    
    % 检查VMD内部收敛性
    if iter_inner > 1
        current_sum_fft = sum(u_k_fft,1);
        change_sum_fft = norm(current_sum_fft - u_sum_fft_prev_iter_start_loop) / (norm(u_sum_fft_prev_iter_start_loop) + eps);
        if change_sum_fft < tol_vmd_inner
            break; 
        end
        u_sum_fft_prev_iter_start_loop = current_sum_fft; 
    else
         u_sum_fft_prev_iter_start_loop = sum(u_k_fft,1); 
    end
end % end VMD inner iteration

u_k_updated = u_k; 
omega_k_updated = omega_k;

end

% update_phase_coeffs_admm_enhanced.m
% ADMM子问题：更新相位多项式系数 p_k (增强版)
% 优化联合代价函数: min -sharpness + match_error
function [poly_coeffs_updated_k, estimated_phase_updated_k] = update_phase_coeffs_admm_enhanced(...
    signal_mode_k, poly_coeffs_prev_k, Residual_Target_Spectrum_k, ...
    params_proc, tm_normalized, sharpness_weight, azimuth_window)

    % 本函数基于梯度下降法，采用最小图像熵作为优化目标，其思想源于用户提供的PhaseComp_MinEntropy_NI.m示例
    
    max_gd_iter = 15;      % 梯度下降迭代次数
    learning_rate = 5e-4;  % 学习率 (这是一个关键参数，可能需要微调)
    
    current_coeffs = poly_coeffs_prev_k; % 从上一轮的系数开始迭代

    % 定义相位多项式的基函数，用于计算梯度
    N = length(tm_normalized);
    poly_order = params_proc.phase_est.poly_order;
    t_poly_basis = zeros(N, poly_order);
    if poly_order >= 1, t_poly_basis(:,1) = 2*pi * tm_normalized; end
    if poly_order >= 2, t_poly_basis(:,2) = 2*pi * 0.5 * tm_normalized.^2; end
    if poly_order >= 3, t_poly_basis(:,3) = 2*pi * (1/6) * tm_normalized.^3; end

    % --- 梯度下降迭代循环 ---
    for iter = 1:max_gd_iter
        
        % 1. 根据当前系数构造相位，并得到补偿后的信号及其频谱(1D图像)
        phase_current = construct_phase_poly(tm_normalized, current_coeffs);
        compensated_signal = signal_mode_k .* exp(-1j * phase_current);
        image_1d = fft(compensated_signal .* azimuth_window);

        % 2. 计算熵对每个相位样本的一阶导数 (dE/dphi)
        %    此处的计算逻辑完全参考 PhaseComp_MinEntropy_NI.m 中的 delt_E 函数
        term_in_ifft = (1 + log(abs(image_1d).^2 + eps)) .* conj(image_1d);
        hrrp_ret = ifft(term_in_ifft);
        
        % 在 PhaseComp_MinEntropy_NI.m 中, 导数是 2*imag(hrrp_ret .* compensated_hrrp)。
        % compensated_hrrp 是 hrrp .* exp(j*fai), 其中 fai 是待优化的相位。
        % 在我们的框架中，compensated_signal 就是这个角色。
        dE_dphi = 2 * imag(hrrp_ret .* compensated_signal);

        % 3. 利用链式法则，计算熵对多项式系数的梯度 (dE/dp)
        %    grad_E(j) = sum_i (dE/dphi_i * dphi_i/dp_j)
        grad_E_vec = (t_poly_basis' * dE_dphi')';
        
        % 4. 执行一步梯度下降
        current_coeffs = current_coeffs - learning_rate * grad_E_vec;
        
    end

    poly_coeffs_updated_k = current_coeffs;
    estimated_phase_updated_k = construct_phase_poly(tm_normalized, poly_coeffs_updated_k);
end

function wrapped_coeffs = wrap_phase_coeffs(coeffs, max_abs_val)
    wrapped_coeffs = mod(coeffs + max_abs_val, 2*max_abs_val) - max_abs_val;
end

% calculate_image_contrast.m
% 计算图像对比度 (与原代码一致)
function contrast = calculate_image_contrast(image_abs)

if isempty(image_abs) || numel(image_abs) < 2

contrast = 0;

return;

end

mean_val = mean(image_abs(:));

std_val = std(image_abs(:));

if mean_val == 0

contrast = 0;

else

contrast = std_val / mean_val;

end

end

% calculate_image_entropy.m
% 计算图像熵 (与原代码一致)
function entropy = calculate_image_entropy(image_abs)

if isempty(image_abs)

entropy = NaN;

return;

end

image_power = image_abs(:).^2;

sum_power = sum(image_power);

if sum_power == 0

entropy = 0;

return;

end

normalized_power = image_power / sum_power;

valid_indices = normalized_power > eps;

if ~any(valid_indices)

entropy = 0;

return;

end

entropy = -sum(normalized_power(valid_indices) .* log2(normalized_power(valid_indices)));

end