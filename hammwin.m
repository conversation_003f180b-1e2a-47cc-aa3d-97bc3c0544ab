function w = hammwin(N)
% HAMMWIN Generates a Hamming window of length N
%   w = HAMMWIN(N) returns an N-point Hamming window
%   Used in ISAR processing to provide good balance between 
%   frequency resolution and spectral leakage.

if N <= 0
    error('Window length must be positive');
end

% For even N
if mod(N, 2) == 0
    half = N/2;
    n = (-half:half-1)';
    w = 0.54 + 0.46*cos(pi*n/half);
else
    % For odd N
    half = (N-1)/2;
    n = (-half:half)';
    w = 0.54 + 0.46*cos(pi*n/half);
end

% Normalize
w = w / sum(w);
end 