function z=isar_hwtmapf(xf,w,len)

%% Function to compute Harmonic Wavelet Map.

%% Author : SHREYAMSHA KUMAR B.K.
%% Created on 26-02-2005. 
%% updated on 26-02-2005.

% k=length(w);
% for ii=1:len-k+1
%     y=zeros(1,len);
%     y(ii:k-1+ii)=w.*xf(ii:k-1+ii);
%     temp=ifft(y)';
%     z(:,ii)=abs(temp).^2;%% Magnitude Squared.
% end
% function z=isar_hwtmapf(xf,w,len)
%     k=length(w);
%     z = zeros(len, len); % Initialize output matrix with full length
% 
%     for ii=1:len
%         start_idx = max(1, ii - floor(k/2));
%         end_idx = min(len, ii + floor(k/2) - 1);
%         windowed_xf = w(1:end_idx-start_idx+1) .* xf(start_idx:end_idx);
% 
%         y=zeros(1,len);
%         y(start_idx:end_idx) = windowed_xf;
%         temp=ifft(y)';
%         z(:,ii)=abs(temp).^2; %% Magnitude Squared.
%     end
% end
% function z=isar_hwtmapf(xf,w,len)
%     k=length(w);
%     half_k = floor(k / 2);
%     z = zeros(len, len); % Initialize output matrix with full length
% 
%     for ii=1:len
%         start_idx = max(1, ii - half_k);
%         end_idx = min(len, ii + half_k - 1);
%         windowed_xf = w(1:end_idx-start_idx+1) .* xf(start_idx:end_idx);
% 
%         y=zeros(1,len);
%         y(start_idx:end_idx) = windowed_xf;
%         temp=ifft(y)';
%         z(:,ii)=abs(temp).^2; %% Magnitude Squared.
%     end
% end


