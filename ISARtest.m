%% 测试自适应ISAR聚焦方法

% 清空工作区
clear all; close all; clc;

% 运行ISARrot_trans.m以生成雷达数据
run('ISARrot_trans.m');

% 提取雷达数据
radar_data = s_r_tm2;
fprintf('雷达数据尺寸: %d x %d (距离单元 x 脉冲数)\n', size(radar_data));

% 1. 显示原始图像
Y=fft(s_r_tm2,Num_tm,2); %直接FFT
G1=20*log10(abs(Y)./max(abs(Y(:))));
figure('name','DCT成像结果');
imagesc(G1);caxis([-30,0]);
grid on;axis xy;colorbar;%axis equal;
% axis([-0.6 0.6 y(select_row(1)) y(select_row(end))]);%set(gca,'xtick',[-0.2 0 0.2]);
xlabel('azimuth');ylabel('range (m)');colormap jet;

% 3. 应用新的自适应聚焦方法
fprintf('应用自适应ISAR聚焦方法...\n');

% 设置参数
options = struct();
options.max_iter =2;
options.lambda = 0.01;
options.PRF = PRF;
options.fc = fc;
options.c = c;
options.omega = [x_oumiga, y_oumiga, z_oumiga];
options.zeta = [x_lamda, y_lamda, z_lamda];
options.kappa = [x_gamma, y_gamma, z_gamma];

% 运行算法
tic;
[focused_image, motion_params] = ISAR_Adaptive_Focus(radar_data, options);
toc;

% 4. 绘制比较结果
figure('Name', 'ISAR成像方法比较', 'Position', [100, 100, 1200, 800]);

% 原始图像
%subplot(2, 2, 1);
imagesc(20*log10(abs(Y)./max(abs(Y(:)))));
caxis([-30, 0]); colorbar; colormap jet; axis xy;
title('原始未聚焦图像');
xlabel('方位向'); ylabel('距离向');colormap jet;



% 自适应聚焦方法
figure('Name', '自适应聚焦方法');
imagesc(20*log10(abs(focused_image)./max(abs(focused_image(:)))));
caxis([-30, 0]); colorbar; axis xy;
title('自适应聚焦方法');
xlabel('方位向'); ylabel('距离向');colormap jet;


% 5. 输出结果摘要
fprintf('\n----- ISAR成像结果对比 -----\n');
fprintf('最终估计的运动参数:\n');
fprintf('角速度 (omega): [%.4f, %.4f, %.4f]\n', motion_params.omega);
fprintf('角加速度 (zeta): [%.4f, %.4f, %.4f]\n', motion_params.zeta);
fprintf('角加加速度 (kappa): [%.4f, %.4f, %.4f]\n', motion_params.kappa);

EntropyImage(focused_image+eps)
contrast(focused_image+eps)
EntropyImage(Y+eps)
contrast(Y)
