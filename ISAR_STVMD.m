%-------------------------------------------------------------------------%
%--------   ISAR Imaging based on Short-Time Variational Mode Decomposition   -------%
%-------------------------------------------------------------------------%

function [imf_stvmd, imag_stvmd] = ISAR_STVMD(radar_data, params)
% ISAR_STVMD - Apply Short-Time Variational Mode Decomposition to ISAR imaging
%
% This function applies STVMD to ISAR radar data for improved imaging of non-stationary
% targets like ships. It extends VMD by incorporating Short-Time Fourier Transform (STFT)
% concepts to better handle time-varying Doppler signatures.
%
% Inputs:
%   radar_data - Complex range-Doppler profiles (MxN, M range bins, N pulses)
%   params - Structure containing STVMD parameters:
%     .K - Number of modes to decompose the signal into (default: 3)
%     .alpha - Balancing parameter of data-fidelity constraint (default: 2000)
%     .tau - Time-step of dual ascent (default: 0.0001)
%     .tol - Convergence tolerance (default: 1e-7)
%     .n_fft - FFT size for time windows (default: 64)
%     .window_length - Length of sliding window (default: n_fft/4)
%     .overlap - Window overlap percentage (default: 0.5)
%     .dynamic - Boolean flag for dynamic STVMD (default: true)
%     .max_iter - Maximum iterations (default: 500)
%
% Outputs:
%   imf_stvmd - Intrinsic Mode Functions from STVMD
%   imag_stvmd - Enhanced ISAR image after STVMD processing

% Set default parameters if not provided
if nargin < 2
    params = struct();
end

if ~isfield(params, 'K'), params.K = 3; end
if ~isfield(params, 'alpha'), params.alpha = 2000; end
if ~isfield(params, 'tau'), params.tau = 0.0001; end
if ~isfield(params, 'tol'), params.tol = 1e-7; end
if ~isfield(params, 'n_fft'), params.n_fft = 64; end
if ~isfield(params, 'window_length'), params.window_length = params.n_fft; end
if ~isfield(params, 'overlap'), params.overlap = 0.5; end
if ~isfield(params, 'dynamic'), params.dynamic = true; end
if ~isfield(params, 'max_iter'), params.max_iter = 500; end

% Extract dimensions
[num_range_bins, num_pulses] = size(radar_data);

% Step 1: Range Alignment (optional, can be done before calling this function)
% Assuming radar_data is already range-aligned

% Step 2: Apply STVMD to each range bin
imf_stvmd = cell(num_range_bins, params.K);
for r = 1:num_range_bins
    % Get the complex signal for current range bin
    signal = radar_data(r, :);
    
    % Apply STVMD to decompose the signal
    imfs = stvmd_decompose(signal, params);
    
    % Store the decomposed IMFs
    for k = 1:params.K
        imf_stvmd{r, k} = imfs(:, k);
    end
end

% Step 3: Reconstruct enhanced ISAR image
% Here we use the dominant IMFs (based on energy) from each range bin
imag_stvmd = zeros(num_range_bins, num_pulses);

% Determine which IMF to use based on energy content
for r = 1:num_range_bins
    % Calculate energy of each IMF
    energy = zeros(1, params.K);
    for k = 1:params.K
        energy(k) = sum(abs(imf_stvmd{r, k}).^2);
    end
    
    % Find dominant IMF (highest energy)
    [~, dominant_idx] = max(energy);
    
    % Use the dominant IMF for reconstruction
    imag_stvmd(r, :) = imf_stvmd{r, dominant_idx};
end

% Fix mirror symmetry issue by comparing with traditional FFT-based image
% First compute traditional FFT image as reference
window = hamming(num_pulses)';
window_matrix = repmat(window, num_range_bins, 1);
traditional_image = fftshift(fft(radar_data .* window_matrix, [], 2), 2);

% Extract significant features from traditional image
threshold = 0.1 * max(abs(traditional_image(:)));
traditional_features = abs(traditional_image) > threshold;

% Create two possible results: original and phase-conjugated
imag_stvmd_original = imag_stvmd;
imag_stvmd_conjugate = conj(imag_stvmd);

% Apply windowing to both
windowed_original = imag_stvmd_original .* window_matrix;
windowed_conjugate = imag_stvmd_conjugate .* window_matrix;

% Compute FFT for both
image_original = fftshift(fft(windowed_original, [], 2), 2);
image_conjugate = fftshift(fft(windowed_conjugate, [], 2), 2);

% Extract features from both results
threshold_original = 0.1 * max(abs(image_original(:)));
threshold_conjugate = 0.1 * max(abs(image_conjugate(:)));
features_original = abs(image_original) > threshold_original;
features_conjugate = abs(image_conjugate) > threshold_conjugate;

% Compare with traditional image using structural similarity
similarity_original = sum(sum(features_original & traditional_features)) / sum(traditional_features(:));
similarity_conjugate = sum(sum(features_conjugate & traditional_features)) / sum(traditional_features(:));

% Select the result that better matches the traditional image
if similarity_conjugate > similarity_original
    fprintf('Detected incorrect mirror symmetry, applying phase conjugate correction...\n');
    imag_stvmd = imag_stvmd_conjugate;
    final_image = image_conjugate;
else
    final_image = image_original;
end

% Additional safety check: compare centroids
[row_trad, col_trad] = find(abs(traditional_image) > 0.3*max(abs(traditional_image(:))));
if ~isempty(row_trad) && ~isempty(col_trad)
    traditional_centroid = [mean(row_trad), mean(col_trad)];
    
    [row_enh, col_enh] = find(abs(final_image) > 0.3*max(abs(final_image(:))));
    if ~isempty(row_enh) && ~isempty(col_enh)
        enhanced_centroid = [mean(row_enh), mean(col_enh)];
        
        % If centroids differ significantly, there might still be a mirroring issue
        if abs(traditional_centroid(2) - enhanced_centroid(2)) > num_pulses/4
            fprintf('Detected centroid mismatch, applying additional correction...\n');
            final_image = fliplr(final_image);
        end
    end
end

% Use the selected image as the final result
imag_stvmd = final_image;

end

function imfs = stvmd_decompose(signal, params)
% STVMD_DECOMPOSE - Core STVMD algorithm implementation
%
% This function implements the Short-Time Variational Mode Decomposition algorithm
% which extends traditional VMD by incorporating time-frequency localization
% for better handling of non-stationary signals.
%
% Inputs:
%   signal - Complex 1D signal to decompose
%   params - Structure of STVMD parameters
%
% Outputs:
%   imfs - Matrix of Intrinsic Mode Functions (signal_length x K)

% Initialize parameters
K = params.K;
alpha = params.alpha;
tau = params.tau;
tol = params.tol;
n_fft = params.n_fft;
window_length = params.window_length;
overlap = params.overlap;
dynamic = params.dynamic;
max_iter = params.max_iter;

% Prepare signal for STVMD processing
signal_length = length(signal);
signal = signal(:); % Ensure column vector

% ---- Step 1: Segment signal into overlapping windows ----
step = floor(window_length * (1 - overlap));
num_segments = floor((signal_length - window_length) / step) + 1;

% Initialize windows
segments = zeros(window_length, num_segments);
for i = 1:num_segments
    start_idx = (i-1) * step + 1;
    end_idx = start_idx + window_length - 1;
    segments(:, i) = signal(start_idx:end_idx);
end

% ---- Step 2: Apply windowing function ----
window_func = hamming(window_length);
windowed_segments = segments .* repmat(window_func, 1, num_segments);

% ---- Step 3: Prepare frequency domain representation ----
f_hat_s = fft(windowed_segments, n_fft, 1);

% Preallocate
u_hat_s = zeros(n_fft, num_segments, K);
u_s = zeros(window_length, num_segments, K);
omega_s = zeros(K, num_segments);
sum_uk = zeros(n_fft, num_segments);
lambda_hat = zeros(n_fft, num_segments);

% Initialize central frequencies for each mode (evenly spaced)
for k = 1:K
    omega_s(k, :) = (0.5 / K) * (k - 1) * ones(1, num_segments);
end

% ---- Step 4: Iterative VMD optimization for each window ----
for iter = 1:max_iter
    % Save previous values for convergence check
    u_hat_prev = u_hat_s;
    omega_prev = omega_s;
    
    % Update each mode
    for k = 1:K
        % Compute accumulated modes except current one
        sum_uk = sum(u_hat_s, 3) - u_hat_s(:, :, k);
        
        % Update kth mode in Fourier domain for all windows
        for t = 1:num_segments
            % Construct frequency domain window around omega_k
            freqs = (0:n_fft-1)' / n_fft;
            freqs(freqs > 0.5) = freqs(freqs > 0.5) - 1; % Shift to [-0.5, 0.5]
            
            % Compute the filter (Wiener filter)
            filter = 1 ./ (1 + alpha * ((freqs - omega_s(k, t)).^2));
            
            % Update u_hat for window t, mode k
            u_hat_s(:, t, k) = (f_hat_s(:, t) - sum_uk(:, t) + (lambda_hat(:, t) / 2)) .* filter;
        end
        
        % Update omega_k (central frequency of mode k)
        if dynamic
            % Dynamic STVMD: allow center frequencies to vary with time
            for t = 1:num_segments
                freqs = (0:n_fft-1)' / n_fft;
                freqs(freqs > 0.5) = freqs(freqs > 0.5) - 1;
                
                % Find new central frequency that minimizes energy
                power_spectrum = abs(u_hat_s(:, t, k)).^2;
                weighted_sum = sum(freqs .* power_spectrum);
                total_power = sum(power_spectrum);
                
                if total_power > 0
                    omega_s(k, t) = weighted_sum / total_power;
                end
            end
        else
            % Non-dynamic STVMD: use same central frequency across all windows
            power_spectrum = sum(abs(u_hat_s(:, :, k)).^2, 2);
            freqs = (0:n_fft-1)' / n_fft;
            freqs(freqs > 0.5) = freqs(freqs > 0.5) - 1;
            
            weighted_sum = sum(freqs .* power_spectrum);
            total_power = sum(power_spectrum);
            
            if total_power > 0
                omega_s(k, :) = (weighted_sum / total_power) * ones(1, num_segments);
            end
        end
    end
    
    % Dual ascent update of Lagrangian multiplier
    for t = 1:num_segments
        sum_modes = sum(u_hat_s(:, t, :), 3);
        lambda_hat(:, t) = lambda_hat(:, t) + tau * (sum_modes - f_hat_s(:, t));
    end
    
    % Check for convergence
    u_diff = sum(sum(sum(abs(u_hat_s - u_hat_prev).^2))) / sum(sum(sum(abs(u_hat_prev).^2)));
    omega_diff = sum(sum(abs(omega_s - omega_prev).^2)) / sum(sum(abs(omega_prev).^2));
    
    if (u_diff < tol) && (omega_diff < tol)
        break;
    end
end

% ---- Step 5: Reconstruct time-domain IMFs ----
for k = 1:K
    for t = 1:num_segments
        temp = ifft(u_hat_s(:, t, k), n_fft, 1);
        u_s(:, t, k) = real(temp(1:window_length));
    end
end

% ---- Step 6: Overlap-add synthesis to reconstruct full-length IMFs ----
imfs = zeros(signal_length, K);

for k = 1:K
    % Reconstruct each IMF
    for i = 1:num_segments
        start_idx = (i-1) * step + 1;
        end_idx = start_idx + window_length - 1;
        
        % Overlap-add with windowing
        imfs(start_idx:end_idx, k) = imfs(start_idx:end_idx, k) + u_s(:, i, k) .* window_func;
    end
end

% Normalize to handle window overlap effects
normalization = zeros(signal_length, 1);
for i = 1:num_segments
    start_idx = (i-1) * step + 1;
    end_idx = start_idx + window_length - 1;
    normalization(start_idx:end_idx) = normalization(start_idx:end_idx) + window_func;
end

% Avoid division by zero
normalization(normalization < 1e-10) = 1;

% Apply normalization
for k = 1:K
    imfs(:, k) = imfs(:, k) ./ normalization;
end

end 