%-------------------------------------------------------------------------%
%--------     增强型DCFT-ADMM-TV ISAR成像算法 (Enhanced Version)    -------%
%--------     基于稀疏优化的三维转动舰船运动补偿与高分辨成像        -------%
%--------     结合DCFT、ADMM优化和总变分正则化                      -------%
%--------     创建时间: 2024年                                      -------%
%-------------------------------------------------------------------------%
clc; clear all; close all;

%% 1. 目标散射点模型与雷达参数 (与原脚本一致)
%-----------------------------离散点模型------------------------------%
Pos = [-10 -1 0;-9 -1 0;-8 -1 0;-7 -1 0;-6 -1 0;-5 -1 0;-3.75 -1 0;-3 -1 0;-2 -1 0;-1 -1 0;...
       0 -1 0; 1 -1 0;2 -1 0;3 -1 0;4 -1 0;5 -1 0;6 -1 0;7 -1 0;8 -1 0;9 -1 0;10 -1 0;...
       -9.5 0.2 0.5;-9.5 1.2 0.5;-9 1 0;-8 1 0;-7 1 0;-6 1 0;-5.2 1.2 0;-4.1 1 0;-3 1 0;-2 1 0;-1 1 0;...
       0 1 0; 1 1 0;2 1 0;3 1 0;4 1 0;5 1 0;6 1 0;7 1 0;8 1 0;9 1 0;10 1 0;...
       10.5 -0.75 0;10.5 0.75 0;11 -0.5 0;11 0.5 0;11.5 0 0;...
       9.5 0.5 0.5;9.5 -0.5 0.5;9 0 0.5;8.5 0.5 0.5;8.5 -0.5 0.5;...
       5 0 0.5;5 0 1;5 0 1.5;5 0 2;5 0 2.5;5 0 3;5 0 3.5;5 0 4;...
       5.5 0.5 0.5;5.5 -0.5 0.5;4.5 0.5 0.5;4.5 -0.5 0.5;...
       0.5 0.5 0.9;0.5 -0.5 0.9;-0.5 0.5 0.9;-0.5 -0.5 0.9;0 0 0.5;...
       -5 0 0.2;-5 0.2 0.8;-5 0.2 1.4;-5 0 2;...
       -5.5 -0.5 0.5;-5.5 0.5 0.5;-4.4 0.5 0.5;-4.5 -0.6 0.5;];

% 坐标变换
x_Pos = Pos(:,1)*5; y_Pos = Pos(:,2)*5; z_Pos = Pos(:,3)*5;
min_x_Pos = min(x_Pos); x_Pos = x_Pos + abs(min_x_Pos);
min_y_Pos = min(y_Pos); y_Pos = y_Pos + abs(min_y_Pos);
min_z_Pos = min(z_Pos); z_Pos = z_Pos + abs(min_z_Pos);

R_los = [cos(3*pi/8)*cos(0),cos(3*pi/8)*sin(0),sin(3*pi/8)]; % 雷达视线单位矢量
Num_point = length(x_Pos);

% 运动参数
x_oumiga = 0.05; y_oumiga = 0.2; z_oumiga = 0.05; % 初角速度
x_lamda = 0.05; y_lamda = 0.1; z_lamda = 0.05;   % 角加速度
x_gamma = 0.05; y_gamma = 0.4; z_gamma = 0.05;   % 角加加速度

x_r = y_Pos*R_los(3)-z_Pos*R_los(2);
y_r = z_Pos*R_los(1)-x_Pos*R_los(3);
z_r = x_Pos*R_los(2)-y_Pos*R_los(1);

f = x_r'*x_oumiga+y_r'*y_oumiga+z_r'*z_oumiga;
alpha = x_r'*x_lamda+y_r'*y_lamda+z_r'*z_lamda;
beita = x_r'*x_gamma+y_r'*y_gamma+z_r'*z_gamma;

% 雷达参数
B = 80e6;       % 带宽
c = 3e8;        % 光速
PRF = 1400;     % 脉冲重复频率
fc = 5.2e9;     % 载频
delta_r = c/(2*B);
r_bins = -50*delta_r:delta_r:50*delta_r;
tm = 0:(1/PRF):0.501;
Num_r = length(r_bins);
Num_tm = length(tm);

%% 2. 生成回波信号 (与原脚本一致)
s_r_tm = zeros(Num_r, Num_tm);
for n_point = 1:Num_point
    Delta_R0 = x_Pos(n_point)*R_los(1)+y_Pos(n_point)*R_los(2)+z_Pos(n_point)*R_los(3);
    Delta_R = Delta_R0 + f(n_point)*tm + (1/2)*alpha(n_point)*tm.^2 + (1/6)*beita(n_point)*tm.^3;
    phase_term = 4*pi/c * fc * Delta_R;
    s_r_tm = s_r_tm + sinc((2*B/c)*(r_bins' - Delta_R)).*exp(1j*phase_term);
end

% 添加高斯白噪声
SNR_dB = 5; % 设置信噪比(dB)
signal_power = norm(s_r_tm(:))^2 / numel(s_r_tm);
noise_power = signal_power / (10^(SNR_dB/10));
noise = (randn(size(s_r_tm)) + 1j*randn(size(s_r_tm))) * sqrt(noise_power/2);
s_r_tm_noisy = s_r_tm + noise;

%% 3. 传统RD成像作为对比
figure('Name', '传统RD成像');
imagesc(abs(fftshift(fft(s_r_tm_noisy, [], 2),2)));
title('传统RD成像结果 (散焦严重)');
xlabel('方位单元'); ylabel('距离单元');
colormap('jet'); colorbar;

%% 4. 增强型DCFT-ADMM-TV算法
% ------------------- 参数设置 ------------------- %
% 运动参数搜索范围
alpha_search = linspace(-200, 400, 61); % 减小搜索点数以加速
beita_search = linspace(-500, 2400, 59); % 减小搜索点数以加速
% ADMM-TV 参数
lambda = 0.05;       % TV正则化系数
rho = 10;            % ADMM罚参数
max_iter = 50;       % ADMM最大迭代次数
range_ind_start = 30;  % 处理的距离单元范围
range_ind_end = 70;

ISAR_image_enhanced = zeros(Num_r, Num_tm);
tic;
fprintf('开始执行增强型DCFT-ADMM-TV算法...\n');
for r_idx = range_ind_start:range_ind_end
    fprintf('处理距离单元: %d / %d\n', r_idx, range_ind_end);
    y_r = s_r_tm_noisy(r_idx, :); % 当前距离单元的回波

    % -------- 步骤 1: 使用DCFT估计主导运动参数 -------- %
    [alpha_est, beta_est] = estimate_motion_params(y_r, tm, alpha_search, beita_search);
    
    % -------- 步骤 2: 使用ADMM-TV求解器重构方位像 -------- %
    x_r = solve_admm_tv(y_r, tm, alpha_est, beta_est, lambda, rho, max_iter);
    
    ISAR_image_enhanced(r_idx, :) = x_r;
end
toc;

%% 5. 显示增强型算法成像结果
figure('Name', '增强型DCFT-ADMM-TV成像');
G_enhanced = 20*log10(abs(ISAR_image_enhanced)./max(abs(ISAR_image_enhanced(:))));
imagesc(G_enhanced);
caxis([-40, 0]);
axis xy;
title('增强型DCFT-ADMM-TV成像结果');
xlabel('方位单元'); ylabel('距离单元');
colormap('jet'); colorbar;

% 计算图像质量指标
C = contrast(ISAR_image_enhanced);
E = EntropyImage(ISAR_image_enhanced);
fprintf('增强型图像对比度: %.4f\n', C);
fprintf('增强型图像熵: %.4f\n', E);

%% 6. 辅助函数

% ------------------ 函数1: 运动参数估计 ------------------ %
function [alpha_est, beta_est] = estimate_motion_params(y, t, alpha_range, beta_range)
    % 使用简化的DCFT能量累积来估计主导运动参数
    max_energy = -1;
    alpha_est = 0;
    beta_est = 0;
    
    for alpha = alpha_range
        for beta = beta_range
            % 构建补偿信号
            comp_phase = exp(-1j * (pi * alpha * t.^2 + (pi/3) * beta * t.^3));
            % 解调并FFT
            s_dechirp = y .* comp_phase;
            S_dechirp = fft(s_dechirp);
            
            % 找到能量峰值
            current_energy = max(abs(S_dechirp).^2);
            if current_energy > max_energy
                max_energy = current_energy;
                alpha_est = alpha;
                beta_est = beta;
            end
        end
    end
end

% ------------------ 函数2: ADMM-TV 求解器 ------------------ %
function x = solve_admm_tv(y, t, alpha, beta, lambda, rho, max_iter)
    % 求解 min_x (1/2)||y - C*F'*x||_2^2 + lambda*||Dx||_1
    N = length(y);
    y = y(:); % 确保是列向量

    % 算子定义
    C = spdiags(exp(1j * (pi * alpha * t.^2 + (pi/3) * beta * t.^3)).', 0, N, N);
    A = @(x) C * (ifft(x) * N); % 傅里叶逆变换带归一化
    At = @(y) fft(C' * y) / N; % 傅里叶变换带归一化

    % 差分算子 (用于TV范数)
    D = @(x) [diff(x); x(1)-x(end)];
    Dt = @(x) [-x(end)+x(1); -diff(x)];
    
    % ADMM 初始化
    x = zeros(N, 1);
    z = zeros(N, 1);
    u = zeros(N, 1);
    
    % 预计算 (A'*A + rho*D'*D)^-1
    Aty = At(y);
    H = @(x) At(A(x)) + rho * Dt(D(x));
    
    for k = 1:max_iter
        % x-subproblem (使用共轭梯度法求解线性方程)
        q = Aty + rho * Dt(z - u);
        [x, ~] = pcg(H, q, 1e-5, 20, [], [], x);
        
        % z-subproblem (软阈值收缩)
        Dx = D(x);
        z = shrink(Dx + u, lambda / rho);
        
        % u-update (对偶变量更新)
        u = u + (Dx - z);
    end
end

% 软阈值函数
function z = shrink(x, t)
    z = sign(x) .* max(abs(x) - t, 0);
end

% 图像质量指标计算函数
function C = contrast(image)
    image = abs(image);
    image = image(image > 0); % 只考虑非零像素
    u = mean(image(:));
    sigma = std(image(:));
    C = sigma / u;
end

function E = EntropyImage(image)
    image = abs(image);
    image = image / sum(image(:));
    idx = find(image > 0);
    E = -sum(image(idx) .* log2(image(idx)));
end
