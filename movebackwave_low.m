% clc
% clear all
% close all
% 
% j = sqrt(-1);
% pi2 = 2*pi;
% c = 3e8;
% 
% %% radar parameters
% fc = 10e9;
% lamda = c / fc;
% Tp = 2e-6;
% B = 800e6;
% K = B / Tp;
% PRF = 1000;
% rr = c/2/B;
% 
% %% target parameters
% L = 13;
% x = [0, 5, 10, 15,... 
% 	-5, -10, -15,...
% 	5, 10, 15,...
% 	-5, -10, -15];
% y = [0, 5, 10, 15,...
% 	5, 10, 15,...
% 	-5, -10, -15,...
% 	-5, -10, -15];
% % L = 1;
% % x = [15];
% % y = [15];
% 
% scatters = x + j*y;
% y0 = 100e3;
% fai0 = 3/180*pi;
% v = 7.5e3;
% x0 = y0*tan(fai0) + (-500:v/PRF:500);
% % bulk location
% bulk_pos = x0 + j*y0;
% M = length(bulk_pos);
% 
% %% receiving design
% % reference distance with random errors;
% sigma = 1;
% Rrn = sqrt(sigma)*randn(1, M);
% Rref = abs(bulk_pos)+ Rrn;
% taoref = 2*Rref/c;
% 
% % receiving gate
% ts_min = -2*15*sqrt(2)/c - Tp/2;
% ts_max = 2*15*sqrt(2)/c + Tp/2;
% osv = 1.2;				
% fs = (ts_max - ts_min - Tp)*K*osv;
% ts = ts_min:1/fs:ts_max;
% N = length(ts);
% 
% %% backwave simulation
% raw = zeros(N, M);
% for i = 1:M
% 	for l = 1:L
% 		R = abs(bulk_pos(i)+scatters(l));
% 		t = ts + taoref(i);
% 		tao = 2*R/c;
% 		win = (t - tao >= -Tp/2 & t - tao < Tp/2);
% 		raw(:, i) = raw(:, i) + ...
% 			win.' .* exp(j*pi2*(fc*(t-tao)+1/2*K*(t-tao).^2)).';
% 	end
% 	% dechirp
% 	sref = exp(j*2*pi*fc*(t-taoref(i))+j*pi*K*(t-taoref(i)).^2);
% 	raw(:, i) = raw(:, i) .* sref';		%'
% end
% 
% save data2
% 
% 
% %% 
% 
% 
% 
% 
% 
% 
clc
clear all
close all

j = sqrt(-1);
pi2 = 2*pi;
c = 3e8;

%% Radar parameters (unchanged)
fc = 10e9;
lamda = c / fc;
Tp = 2e-6;
B = 800e6;
K = B / Tp;
PRF = 1000;
rr = c/2/B;

%% Target parameters (新增旋转角速度omega)
L = 13;
x = [0, 5, 10, 15, -5, -10, -15, 5, 10, 15, -5, -10, -15];
y = [0, 5, 10, 15, 5, 10, 15, -5, -10, -15, -5, -10, -15];
scatters = x + j*y;  % 散射点初始位置（相对于质心）

% 目标平动参数
y0 = 100e3;         % 初始斜距
fai0 = 3/180*pi;    % 初始方位角
v = 7.5e3;          % 平动速度 (m/s)
x0 = y0*tan(fai0) + (-500:v/PRF:500); % 方位向平动轨迹
bulk_pos = x0 + j*y0;  % 质心位置序列
M = length(bulk_pos);

% ====== 新增旋转参数 ====== 
omega = 0.08;        % 角速度 (rad/s)，控制旋转快慢
theta = omega * (0:M-1)/PRF;  % 每个脉冲对应的旋转角度

%% Receiving design (unchanged)
sigma = 1;
Rrn = sqrt(sigma)*randn(1, M);
Rref = abs(bulk_pos) + Rrn;
taoref = 2*Rref/c;

ts_min = -2*15*sqrt(2)/c - Tp/2;
ts_max = 2*15*sqrt(2)/c + Tp/2;
osv = 1.2;				
fs = (ts_max - ts_min - Tp)*K*osv;
ts = ts_min:1/fs:ts_max;
N = length(ts);

%% Backscatter simulation (关键修改：散射点绕质心旋转)
raw = zeros(N, M);
for i = 1:M
    % ====== 新增：计算旋转后的散射点坐标 ======
    rot_matrix = exp(j*theta(i));     % 旋转算子
    rotated_scatters = scatters * rot_matrix;  % 绕质心旋转theta(i)
    
    for l = 1:L
        % 散射点绝对位置 = 质心位置 + 旋转后的相对位置
        R = abs(bulk_pos(i) + rotated_scatters(l));
        t = ts + taoref(i);
        tao = 2*R/c;
        win = (t - tao >= -Tp/2 & t - tao < Tp/2);
        raw(:, i) = raw(:, i) + ...
            win.' .* exp(j*pi2*(fc*(t-tao) + 1/2*K*(t-tao).^2)).';
    end
    
    % Dechirp参考信号（保持原逻辑）
    sref = exp(j*2*pi*fc*(t-taoref(i)) + j*pi*K*(t-taoref(i)).^2);
    raw(:, i) = raw(:, i) .* sref';
end

save data2