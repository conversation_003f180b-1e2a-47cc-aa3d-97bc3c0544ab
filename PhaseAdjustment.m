function [Y,phi,ref] = PhaseAdjustment(X,nref)

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%
% Performs phase adjustment of the profiles contained in the rows of
% array X.  The algorithm adjusts the phases in each profile so as 
% to align the phase values in a number of reference range cells (nref).
% A minimum amplitude variance criterion of the dominant scatterers 
% is used to choose the reference range cells, the phases differences 
% of which are averaged(after unwrapping) to provide the phase correction.  
%   
% Input: 
%     Complex data: X
%     number of reference range cells: nref
% Output:
%     Range profile: Y
%     Phase correction vector: phi
%     Reference cells: ref
%
% Ref: Haywood, B., Evens, R.,"Motion compensation for ISAR imaging," 
%      Proc. of ASSPA, Adelaide, Australia, pp. 113-117, 1989.
%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
j = sqrt(-1);
[m,n] = size(X);
mean = sum(abs(X))/m;	% mean amplitude
meansq = sum(abs(X).^2)/m;	% mean square

for k=1:n
    if meansq(k)>0
      V(k) = (mean(k)^2)/meansq(k);  % variance
    else
      V(k) = 0;
    end
end
rmsi = sqrt(sum(meansq)/n);	 % rms of image
rms = sqrt(meansq);	% rms in each range cell
ref = zeros(1,nref);
for k = 1:nref
  [vmax,ref(k)] = max(V.*(rms > rmsi));	
  vmin = 1-vmax;	% minimum variance
  fprintf('Minimum variance %g is %g in range cell #%g\n',k,vmin,ref(k))
  V(ref(k)) = 0;
end
phi0n = angle(X(:,ref(1)));
psi = zeros(m,1);
for k = 2:nref
  psi = psi+unwrap(angle(X(:,ref(k)))-phi0n); % sum of phase differences
end
phin = psi/nref + angle(X(:,ref(1))); % add phase diff to ref cell no.1
phi = exp(-j*phin);	% phase correction vector

figure
plot(phin,'-','linewidth', 2)
xlabel('pulses')
ylabel('phase function')
title('Phase function averaged from three scatterers')
axis tight
drawnow

P = phi(:,ones(1,n));	% phase matrix
Y = X.*P;	% apply phase correction

figure
subplot(3,1,1)
plot(unwrap(angle(Y(:,ref(3)))),'-','linewidth', 2)
xlabel('pulses')
ylabel('phase function')
title(sprintf('Adjusted phase function at range cell %g',ref(3)));
axis tight
drawnow
subplot(3,1,2)
plot(unwrap(angle(Y(:,ref(1)))),'-','linewidth', 2)
xlabel('pulses')
ylabel('phase function')
title(sprintf('Adjusted phase function at range cell %g',ref(1)));
axis tight
drawnow
subplot(3,1,3)
plot(unwrap(angle(Y(:,ref(2)))),'-','linewidth', 2)
xlabel('pulses')
ylabel('phase function')
title(sprintf('Adjusted phase function at range cell %g',ref(2)));
axis tight
drawnow

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%