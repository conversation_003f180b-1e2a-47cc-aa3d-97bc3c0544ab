%% 380模型ISAR实验数据，“飞过波束”
%% 参数
clear all;clc;close all;
flag1=0;   %为1时滤波，为0时不滤波
flag2=0;   %为1时筛选数据，为0时不筛选
c=3e8;
nx=1024*8;     %触发次数，在采集软件中设置，成像一般用1k。
nf=4096;     %每次触发采集4096个点
fs=10e6;     %采样率，成像一般用40M，微动用4M
N=2;         %画出的周期数目
dt=1/fs;
T=500e-6;    %脉冲周期
To=400e-6;   %扫频时间
f=linspace(13449,14250,nf)*16*1e6;         %频率采样，单位Hz
df=f(2)-f(1);                              %频率采样间隔
fc=mean(f);                                %载频
B=f(end)-f(1);                             %带宽
lamda=c/fc;                                %波长
K=B/To;                                    %调频率
rr=c/2/B;                                  %距离分辨
y=linspace(-c/df/4,c/df/4,nf);             %距离，单位m

%% 数据读取
%打开背景文件
fid1=fopen('beijing_10M_8K.bin','rb');  
stmp=fread(fid1,inf,'integer*2'); 
fclose(fid1); 
CH1=stmp(1:2:end);
CH2=stmp(2:2:end);
data_bg=CH1+1j*CH2;
data_bg=data_bg-mean(data_bg);       %减去直流分量
% data_bg=data_bg/max(abs(data_bg));   %归一化
t=[0:length(data_bg)-1]*dt;          %单位s
%打开参考文件
fid1=fopen('cankao_10M_8K.bin','rb');    
stmp=fread(fid1,inf,'integer*2'); 
fclose(fid1); 
CH1=stmp(1:2:end);
CH2=stmp(2:2:end);
data_ref=CH1+1j*CH2;
data_ref=data_ref-mean(data_ref);       %减去直流分量
% data_ref=data_ref/max(abs(data_ref));   %归一化
t=[0:length(data_ref)-1]*dt;            %单位s
% 打开目标文件
fid1=fopen('plane_2_10M_8K.bin','rb');    
stmp=fread(fid1,inf,'integer*2'); 
fclose(fid1); 
CH1=stmp(1:2:end);
CH2=stmp(2:2:end);
data_tar=CH1+1j*CH2;
data_tar=data_tar-mean(data_tar);       %减去直流分量
% data_tar=data_tar/max(abs(data_tar));

%% 数据校正
if flag2==1
    data_bg=reshape(data_bg,nf,nx);data_ref=reshape(data_ref,nf,nx);data_tar=reshape(data_tar,nf,nx);%取中间功率较平坦的一部分
    data_bg=data_bg(nf*0.15:nf*0.75,:);data_ref=data_ref(nf*0.15:nf*0.75,:);data_tar=data_tar(nf*0.15:nf*0.75,:);
    nf=size(data_bg,1);B=B*0.6;K=B/To;rr=c/2/B;f=f(nf*0.15:nf*0.75);
    data_bg=reshape(data_bg,nf*nx,1);data_ref=reshape(data_ref,nf*nx,1);data_tar=reshape(data_tar,nf*nx,1);
end

% data_cal=(data_tar-data_bg).*conj((data_ref-data_bg));
data_cal=(data_tar-data_bg)./((data_ref-data_bg));
% data_cal=data_tar.*conj(data_ref);
% data_cal=data_tar./(data_ref);
% data_cal=data_tar;
data=reshape(data_cal,nf,nx);

%% 数据校正
if flag1==1
    spectrum=fft(data.');
    spectrum(1,:)=mean(spectrum);
    data=(ifft(spectrum)).';
end

% 根据转台中心的位移 对信号进行校正（参考信号对应的距离不是转台中心） %%%
% R_compensate=0.04; % 补偿的距离 
% f_compensate=-2*K*R_compensate/c;
% phase_comp=exp(1j*2*pi*f_compensate*linspace(0,1,nf)'*To); % linspace(-0.5,0.5,N_k_sample)
% data=data.*(phase_comp*ones(1,nx));

wina=taylorwin(nx,4,-30);
winr=taylorwin(nf,4,-30);
Win=winr*wina.';
data=data.*Win;                %数据加了一个二维窗

%% 数据分析
figure('name','背景回波');
plot(t(1:N*nf),real(data_bg(1:N*nf)));hold on;
plot(t(1:N*nf),imag(data_bg(1:N*nf)),'r');hold off;
xlabel('t(s)');ylabel('Amplitude');legend('real','imag');
figure('name','参考回波');
plot(t(1:N*nf),real(data_ref(1:N*nf)));hold on;
plot(t(1:N*nf),imag(data_ref(1:N*nf)),'r');hold off;
xlabel('t(s)');ylabel('Amplitude');legend('real','imag');
figure('name','目标回波');
plot(t(1:N*nf),real(data_tar(1:N*nf)));hold on;
plot(t(1:N*nf),imag(data_tar(1:N*nf)),'r');hold off;
xlabel('t(s)');ylabel('Amplitude');legend('real','imag');
figure('name','校正后回波');
plot(t(1:N*nf),real(data_cal(1:N*nf)),'b');hold on;
plot(t(1:N*nf),imag(data_cal(1:N*nf)),'r');hold off;
xlabel('t(s)');ylabel('Amplitude');legend('real','imag');

% y=linspace(-c/df/4,c/df/4,nf);             %距离，单位m
% ximg_bg=iftx(data_bg((nx/2+1):(nx/2+nf)));
% ximg_ref=iftx(data_ref((nx/2+1):(nx/2+nf)));
% ximg_tar=iftx(data_tar((nx/2+1):(nx/2+nf)));
% figure('name','背景一维距离像');plot(y,abs(ximg_bg));xlabel('range(m)');ylabel('Amplitude');
% figure('name','参考一维距离像');plot(y,abs(ximg_ref));xlabel('range(m)');ylabel('Amplitude');
% figure('name','目标一维距离像');plot(y,abs(ximg_tar));xlabel('range(m)');ylabel('Amplitude');

% % 画信号频谱
% tempFspec=ftx(data_ref(1:2048),4096*4);%!默认该信号u的中心在0s，与真实之差常数相位项，不影响图
% figure('name','信号频谱');
% plot(linspace(-0.5,0.5,4096*4)*fs*c/K/2,abs(tempFspec));
% grid on;xlabel('range(m)');ylabel('Amplitude');
% figure('name','信号频谱（dB）');
% plot(linspace(-0.5,0.5,4096*4)*fs*c/K/2,20*log10(abs(tempFspec)));
% grid on;xlabel('range(m)');ylabel('Amplitude(dB)');
% 
% fl=linspace(-0.5,0.5,4096*4)*fs/1e6;%单位MHz
% [tfr,tt,ff]=tfrstft(data_ref(1:nf*N),1:nf*N,nf*N,tftb_window(55,'hamming'));
% tfr=circshift(tfr,floor(nf*N/2));
% figure('name','参考信号若干周期stft时频分析');
% imagesc(dt.*(1:nf*N),fl,abs(tfr));
% axis xy;grid on;xlabel('time(s)');ylabel('Frequency(MHz)');
% fr=2*3.8/c*K;          %频偏

% [tfr,tt,ff]=tfrstft(data_cal(1:nf*N),1:nf*N,nf*N,tftb_window(55,'hamming'));
% tfr=circshift(tfr,floor(nf*N/2));
% figure('name','信号补偿后若干周期stft时频分析');
% imagesc(t,fl,abs(tfr));
% axis xy;grid on;xlabel('time(s)');ylabel('Frequency(MHz)');

% data_tf=data_ref(1:nf*N);
% data_tf=interp1(1:length(data_tf),data_tf,1:0.5:length(data_tf),'linear').';%插值
% [tfr_PWVD,tt,ff]=tfrpwv(data_tf,1:length(data_tf),length(data_tf),window(@hamming,55));
% tfr_PWVD=circshift(tfr_PWVD,round(size(tfr_PWVD,1)/2));%图像平移
% figure('name','信号补偿后若干周期PWVD时频分析');
% imagesc(t,fl,abs(tfr_PWVD));
% axis xy;grid on;xlabel('time(s)');ylabel('Frequency(MHz)');

% data_cal2=(data_ref).*conj((data_ref));%吸波材料性能分析
% % data_cal2=(data_ref-data_bg).*conj((data_ref-data_bg));
% data2=reshape(data_cal2,nf,nx);
% ximg_1=iftx(data_cal((nx/2+1):(nx/2+nf)));
% ximg_2=iftx(data_cal2((nx/2+1):(nx/2+nf)));
% figure('name','吸波材料');
% plot(y,pow2db(abs(ximg_1)),'r--');hold on;
% plot(y,pow2db(abs(ximg_2)));hold off;
% xlabel('range(m)');ylabel('Amplitude(dB)');
% legend('加吸波材料','未加吸波材料');grid on;
% axis([-3 3 8 52]);

%% 成像
ximg=iftx(data);%对每个脉冲进行fft    %一维FFT 距离像 
figure('name','一维距离像');
t2=t(1:nf:end); %时间抽样，单位s。 
imagesc(t2,y,abs(ximg));
axis([t2(1) t2(end) -1.50 1.50]);
xlabel('t/ s');ylabel('range/ m');
figure,imagesc(abs(ximg));
% figure('name','平均一维距离像');
% plot(y,pow2db(mean(abs(ximg.'))/max(mean(abs(ximg.')))));
% grid on; %axis([-100 100 -30 0]);
% xlabel('range(m)');ylabel('Amplitude(dB)');

%% 调用Turnimaging进行CBP成像
% r_desample=2;a_desample=1;       %准备CBP成像
% theta2=theta(1:a_desample:2000);f2=f(1:r_desample:end);
% data2=data(1:r_desample:end,1:a_desample:2000);
% [Im2,y1,x1]=TurnImaging(theta2*180/pi,f2,data2,2,30,0*512);
% figure('name','CBP成像结果（dB）');
% G=20*log10(abs(Im2)+1e-6);
% gm=max(max(G));
% gn=gm-25;%显示动态范围,dB
% G=255/(gm-gn)*(G-gn).*(G>gn);
% imagesc(y1,x1,G);
% axis([-0.50 0.50 -0.50 0.50]);
% xlabel('Azimuth(m)');ylabel('Range(m)');
% grid on;axis xy;set(gca,'XDir','reverse');

%% 2:小角度成像
select_col=1:nf;
select_row=2500+1:2500+2048;                %选取脉冲
ximg_select=ximg(select_col,select_row);   
figure('name','筛选数据段距离像'); imagesc(t2(select_row),y(select_col),abs(ximg_select));
axis([t2(select_row(1)) t2(select_row(end)) -1.50 1.50]);
xlabel('t/ s');ylabel('range/ m');grid on;axis xy;
% data=ftx(ximg_select);

x1=RangeAlign_Corr(data(:,select_row),8,60);                 %以两包络相关系数最大为包络对齐标准（数据，插值，窗）ok
% x1=RangeAlign_MinEnt(data,1,30,30);        %最小熵包络对齐（数据，插值，对齐窗，搜索窗）ok
% x1=RangeAlign_MinEnt_Refine(data,1,30,30); %改进的最小熵法（数据，插值，对齐窗，搜索窗）ok
% x1=RangeAlign_All(data,1,30,30,2);         %基于模1、模2-距离最小准则（数据，插值，对齐窗,搜索窗,1/2）ok
% x1=linej(data,8);                              %相关精对齐方法,(数据，插值)ok
figure('name','包络对齐'),imagesc(t2(select_row),y(select_col),abs(x1)*5);
title('一维距离像——包络对齐后')
axis([t2(select_row(1)) t2(select_row(end)) -1.50 1.50]);
xlabel('t/ s');ylabel('range/ m');grid on;axis xy;

figure('name','校正前初相');imagesc(t2(select_row),y(select_col),angle(x1));
axis([t2(select_row(1)) t2(select_row(end)) -1.50 1.50]);
xlabel('t/ s');ylabel('range/ m');grid on;axis xy;

% x2=PhaseComp_MidTr(x1);         %中心跟踪方法,ok
% x2=PhaseComp_SingP(x1);         %单特显点,ok
% x2=CompensateByMultiProNo(x1,30);  %多特显点(初相差估计)(x1,特显点),ok
% x2=wmsax(x1,0.2);               %加权多特显点方法(x1,门限)
% x2=Compensate_corr(x1);              %互相关相位对准(即相位精补偿),ok
% x2=Comp_MinEntropy(x1,1e-3);    %基于最小熵原则的相位精补偿,ok,但是方位位置不对
% x2=IcsaAutofocus(x1,2000,3);     %高精度迭代相干积累自聚焦函数,ok
% x2=PhaseComp_PGA(x1,10);           %PGA法(x1,迭代次数),ok,但是方位位置不对

% x2 = KeystoneTrans(x2,200e6,35e9);   %(原始数据，带宽，载频)
% figure('name','校正后初相'); imagesc(angle(x2(1900:2200,:)));
hrrp = x1;
%%--------------------牛顿最小熵------------------
%function [ret_data fai]=PhaseComp_MinEntropy_NI(hrrp,miu,P)
% 牛顿迭代
% hrrp: 对齐后包络，快时间*慢时间
% miu: 迭代终止条件
% P：迭代步数

h1 = hrrp;
[N,M] = size(hrrp);
P = 6;
% hrrp = hrrp.*(ones(N,1)*gausswin(M,5).');
fai_temp = zeros(1,M);
fai_new = ones(1,M);
fai_old = fai_temp;
for m = 2:M
    fai_temp(m) = angle(sum(hrrp(:,m).*conj(hrrp(:,m-1))));
    fai_new(m) = -sum(fai_temp(1:m));
end
% data_uf= x1;
% data_kf(:,1)=data_uf(:,1);
% for (ii=2:M)
%     fai=angle(sum(conj(data_kf(:,ii-1)).*data_uf(:,ii)));
%     data_kf(:,ii)=data_uf(:,ii).*exp(-j*fai);
% end
% %fai_new = ones(1,M);
%  fai_new=fai;
 %fai_new=fai0_dct;
count=0;
miu = 1e-3;
e=[];
c= [];
fai1=[];
dk = [];
timee = [];
%hrrp = awgn(hrrp,-1); 
tic
while   count<80%(max(abs(exp(j*fai_new)-exp(j*fai_old)))>miu)
    %count<P%(max(abs(exp(j*fai_new)-exp(j*fai_old))) > miu) % && count<50%
   
     count=count+1;
    fai_old = fai_new;
    im = fty(hrrp.*(ones(N,1)*exp(j.*fai_old)));%ISAR图像
    dC = delt_C(im,hrrp,fai_old);
    dC_2 = delt_C2(im,hrrp,fai_old);
    dC_2= -abs(dC_2);
    fai_new = angle(exp(j*(fai_old-dC./(dC_2+eps))));
%     dk=-dC./(dC_2+eps);
%     [a,b] = minJTC(hrrp,fai_old,dk,beta0,h0);
%     [beta2,minf,minf2,Nstep] = minHJC(hrrp,fai_old,dk,a,b);
%     fai_new = angle(exp(j*(fai_old+beta2*dk)));
    e=[e EntropyImage(im)];
    c=[c contrast(im)];
    fai1=[fai1 fai_new'];
    dk1 = dC./(dC_2+eps);
    dk = [dk dk1];
   

end
 figure;plot(e,'LineWidth',2);
% figure;imagesc(abs(im));
toc
ret_data = h1.*(ones(N,1)*exp(j.*fai_new));
fai = -fai_new;
x2 = ret_data;
figure('name','校正后初相'); imagesc(t2(select_row),y(select_col),angle(x2));
% axis([t2(select_row(1)) t2(select_row(end)) -1.50 1.50]);
xlabel('t/ s');ylabel('range/ m');grid on;axis xy;
%% 二维成像
Y=RDimaging(x2); %直接FFT
% figure,imagesc(abs(Y));axis xy;
figure('name','ISAR成像结果dB');
G=20*log10(abs(Y)+1e-6);
gm=max(max(G));
gn=gm-60;%显示动态范围,dB
G=255/(gm-gn)*(G-gn).*(G>gn);
% imagesc(G(990:1210,3850:4300));
imagesc(G);
xlabel('方位向');ylabel('距离向');grid on;axis xy;
G2=20*log10(abs(Y)./max(abs(Y(:))));
figure('name','nufftISAR成像结果dB');
imagesc(G2);caxis([-50,0]);
grid on;axis xy;colorbar;ftresize(13);%axis equal;
% axis([-0.6 0.6 y(select_row(1)) y(select_row(end))]);%set(gca,'xtick',[-0.2 0 0.2]);
xlabel('azimuth');ylabel('range (m)');

% Mpad=4096;
% yimg_select=ifty(ximg_select,Mpad);
% figure('name','二维FFT成像结果（dB）');
% G=20*log10(abs(yimg_select)+1e-6);
% gm=max(max(G));
% gn=gm-30;%显示动态范围,dB
% G=255/(gm-gn)*(G-gn).*(G>gn);
% imagesc(1:Mpad,y,G);
% xlabel('Azimuth/ m');ylabel('Range/ m');
% grid on;axis xy;

%% CBP成像
% r_desample=2;a_desample=1;       %准备CBP成像
% data2=data(1:r_desample:end,1:a_desample:end);
% Fs=fs/r_desample;%相当于降低采样率，快时间
% k=4*pi*f/c; 
% k=k(1:r_desample:end);
% theta=theta(1:a_desample:end);%慢时间降采样
% N_theta=length(theta);
% N_k=length(k);
% f_delta=B/(N_k-1);
% k_delta=4*pi*f_delta/c;  %  波数采样间隔
% theta_delta=dph*a_desample;
% 
% k_middle=mean(k);
% RCS_temp=k.'*ones(1,N_theta).*data2;
% C_theta=cos(theta);
% S_theta=sin(theta);
% N_r=210*2;              %距离向成像点数
% N_a=180*2;              %方位向成像点数
% range_r=0.7; % 距离向成像范围
% range_a=0.6; % 方位向成像范围
% x=linspace(-range_r/2,range_r/2,N_r); % 目标坐标系下的距离向坐标位置
% y=linspace(-range_a/2,range_a/2,N_a); % 目标坐标系下的方位向坐标位置
% [Y,X]=meshgrid(y,x);
% g_ra=zeros(N_r,N_a);  % 初始化图像为零
% % 卷积逆投影成像
% h= waitbar(0,'成像中……');
% N_f=2048;
% g_temp=iftx(RCS_temp,N_f)*sqrt(N_f);
% % g_temp=sqrt(N_f)*fftshift(ifft(ifftshift(RCS_temp,1),N_f),1); % 效果差 补零的位置 影响了方位向的相位 使方位向散焦
% 
% for c_t=1:N_theta   % 远场
%     temp=interp1(linspace(-Fs*c/4/K,Fs*c/4/K,N_f),g_temp(:,c_t),X*C_theta(c_t)+Y*S_theta(c_t),'nearest').*exp(1j*k_middle*(X*C_theta(c_t)+Y*S_theta(c_t)));
%     g_ra=g_ra+temp;
%     waitbar(c_t/N_theta);
% end
% g_ra=g_ra*k_delta*theta_delta;
% close(h);
% figure('name','CBP成像结果（dB）');
% G=20*log10(abs(g_ra)+1e-6);
% gm=max(max(G));
% gn=gm-25;%显示动态范围,dB
% G=255/(gm-gn)*(G-gn).*(G>gn);
% imagesc(y,x,G);
% xlabel('Azimuth(m)');ylabel('Range(m)');
% grid on;axis square;


%% 分辨率分析
% AziPro=abs((Im(4114,:)));
% AziPro=AziPro./max(AziPro);
% % AziPro_dB=pow2db(AziPro);
% figure('name','方位向剖面图');
% plot(x,AziPro(Npad+1:Npad+nx));xlabel('Azimuth(m)');ylabel('Amplitude(dB)');grid on;
% 
% RanPro=abs((Im(:,2579).'));
% RanPro=RanPro./max(RanPro);
% RanPro_dB=pow2db(RanPro);
% figure('name','距离向剖面图');
% plot(y,RanPro_dB(Npad+1:Npad+nf));xlabel('Range(m)');ylabel('Amplitude(dB)');grid on;


