function [xc] = centro1d(MI)

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%
% centroid_1d
% -  finds the centroid of a mass distribution with wrap-around.
% The horizontal mass vector must have an even number of samples.
%
% By V.C. Chen
%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

M = sum(MI);
s = max(size(MI));
sh = s/2;
x = 1:s;
MS = [MI(1,sh+1:s) MI(1,1:sh)];
xcI = sum(x.*MI)/M;
xcS = sum(x.*MS)/M;
IntI = sum(((x-xcI).^2).*MI);
IntS = sum(((x-xcS).^2).*MS);
if IntI <= IntS; xc = xcI;      end
if IntI >  IntS; xc = xcS - sh; end
if xc <= 0; xc = xc + 2*sh; end
if xc >  s; xc = xc - 2*sh; end

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
