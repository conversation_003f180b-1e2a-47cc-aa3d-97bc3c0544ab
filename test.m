%% ISAR Imaging Algorithm for Maneuvering Target based on GSOTST
% This code implements the algorithm described in the paper
% "A Novel ISAR Imaging Algorithm for a Maneuvering Target Based on 
% Generalized Second-Order Time-Scaled Transform"

clear all;
close all;
clc;

%% Parameters Setup
% Radar system parameters
c = 3e8;                  % Speed of light (m/s)
fc = 10e9;                % Carrier frequency (Hz)
lambda = c/fc;            % Wavelength (m)
B = 200e6;                % Signal bandwidth (Hz)
PRF = 500;                % Pulse repetition frequency (Hz)
T_p = 40e-6;              % Pulse duration (s)
fs = 300e6;               % Range sampling frequency (Hz)
N_rg = 256;               % Range sampling points
N_az = 256;               % Number of pulses
SNR = 3;                  % SNR in dB

% Target motion parameters
r_0 = 20e3;               % Initial slant range (m)
v_r = 20;                 % Radial velocity (m/s)
a_r = 3;                  % Radial acceleration (m/s^2)
b_r = 2;                  % Radial acceleration rate (m/s^3)
omega_0 = 0.018;          % Angular velocity (rad/s)
omega_1 = 0.012;          % Angular acceleration (rad/s^2)
omega_2 = 0.015;          % Angular acceleration rate (rad/s^3)

% Modify initial slant range to make target visible in range window
r_0 = 100;                % Initial slant range (m) - decreased to fit in range window

%% Create aircraft scatter model (similar to Fig. 9(a))
% Generate scattering points for the aircraft
num_points = 130;         % Number of scattering points
% Random positions for scattering points to form an aircraft-like shape
% This is just a simple model - you can create a more accurate model if needed
x_min = -40; x_max = 40;
y_min = -30; y_max = 30;

% Create a more structured aircraft-like scattering model
% Define main body
body_x = linspace(-20, 20, 40);
body_y = zeros(size(body_x));
body_amp = ones(size(body_x));

% Define wings
wing_left_x = linspace(-15, 0, 20);
wing_left_y = linspace(-20, 0, 20);
wing_left_amp = ones(size(wing_left_x));

wing_right_x = linspace(0, 15, 20);
wing_right_y = linspace(0, -20, 20);
wing_right_amp = ones(size(wing_right_x));

% Define tail
tail_x = linspace(-20, -15, 10);
tail_y = linspace(0, 15, 10);
tail_amp = ones(size(tail_x));

% Combine all parts
x_p = [body_x, wing_left_x, wing_right_x, tail_x, linspace(-15, 15, num_points-length(body_x)-length(wing_left_x)-length(wing_right_x)-length(tail_x))];
y_p = [body_y, wing_left_y, wing_right_y, tail_y, 5*randn(1, num_points-length(body_y)-length(wing_left_y)-length(wing_right_y)-length(tail_y))];
amp = [body_amp, wing_left_amp, wing_right_amp, tail_amp, ones(1, num_points-length(body_amp)-length(wing_left_amp)-length(wing_right_amp)-length(tail_amp))];

% Visualize the scattering model
figure;
scatter(x_p, y_p, 20, 'r', 'filled');
xlabel('X (m)');
ylabel('Y (m)');
title('Aircraft Scatter Model');
axis equal;
grid on;

%% Simulate ISAR echo signal
% Time and frequency vectors
t_m = (0:N_az-1)/PRF;                     % Slow-time (s)
t_r = (-N_rg/2:N_rg/2-1)/fs;              % Fast-time (s)
f_r = (-N_rg/2:N_rg/2-1)*fs/N_rg;         % Range frequency (Hz)

% Range resolution and swath
range_res = c/(2*B);                      % Range resolution (m)
range_swath = c*N_rg/(2*fs);              % Range swath (m)
disp(['Range resolution: ', num2str(range_res), ' m']);
disp(['Range swath: ', num2str(range_swath), ' m']);
disp(['Maximum range index corresponds to: ', num2str(range_swath), ' m']);

% Initialize echo signal
echo_signal = zeros(N_rg, N_az);

% Generate echo signal for each scattering point
for i = 1:length(x_p)
    % Calculate initial angle
    theta_p = atan2(y_p(i), x_p(i));
    
    % Calculate effective rotation for each slow-time
    omega_e = omega_0 + omega_1*t_m + omega_2*t_m.^2;
    
    % Calculate slant range history (Equation 5)
    R_p = zeros(1, N_az);
    for n = 1:N_az
        % Range calculation based on translational and rotational motion
        R_p(n) = r_0 + y_p(i) + (v_r + x_p(i)*omega_0)*t_m(n) + ...
                (0.5*a_r + x_p(i)*omega_1)*t_m(n)^2 + ...
                (1/6*b_r + x_p(i)*omega_2)*t_m(n)^3;
    end
    
    % Generate signal for this scattering point (Equation 7)
    for n = 1:N_az
        % Range delay in samples
        tau = 2*R_p(n)/c;
        
        % Calculate range_idx differently to ensure it falls within range window
        % Map the actual range to the range window
        relative_range = R_p(n);  % Range relative to starting range
        range_idx = round((relative_range / range_swath) * N_rg);
        
        % Ensure range_idx is within bounds and create the signal
        if range_idx >= 1 && range_idx <= N_rg
            % Phase term
            phase = -4*pi*R_p(n)/lambda;
            echo_signal(range_idx, n) = echo_signal(range_idx, n) + amp(i)*exp(1j*phase);
        end
    end
end

% Add noise
if SNR < inf
    signal_power = mean(abs(echo_signal(:)).^2);
    noise_power = signal_power / (10^(SNR/10));
    noise = sqrt(noise_power/2) * (randn(size(echo_signal)) + 1j*randn(size(echo_signal)));
    echo_signal = echo_signal + noise;
end

%% Display range-compressed signal
figure;
imagesc(1:N_az, 1:N_rg, 20*log10(abs(echo_signal)/max(abs(echo_signal(:)))));
colorbar;
xlabel('Slow-time (pulse number)');
ylabel('Range gate (samples)');
title('Range-compressed 2D time domain');
colormap('jet');
caxis([-30, 0]);

%% Perform translational motion compensation (TMC)
% Envelope alignment (traditional method based on entropy)
aligned_signal = echo_signal;
for n = 2:N_az
    max_shift = 20;  % Maximum shift to search
    min_entropy = inf;
    best_shift = 0;
    
    % Find shift that minimizes entropy
    for shift = -max_shift:max_shift
        % Shift signal
        shifted_profile = circshift(abs(aligned_signal(:, n)), shift);
        
        % Calculate entropy
        entropy_val = -sum(shifted_profile .* log(shifted_profile + eps));
        
        % Update best shift
        if entropy_val < min_entropy
            min_entropy = entropy_val;
            best_shift = shift;
        end
    end
    
    % Apply best shift
    aligned_signal(:, n) = circshift(aligned_signal(:, n), best_shift);
end

% Display aligned signal
figure;
imagesc(1:N_az, 1:N_rg, 20*log10(abs(aligned_signal)/max(abs(aligned_signal(:)))));
colorbar;
xlabel('Slow-time (pulse number)');
ylabel('Range gate (samples)');
title('After Envelope Alignment');
colormap('jet');
caxis([-30, 0]);

%% Range-Doppler (RD) imaging
% Perform FFT along the azimuth direction
rd_image = fftshift(fft(aligned_signal, [], 2), 2);

% Display RD image
figure;
imagesc((-N_az/2:N_az/2-1)*PRF/N_az, 1:N_rg, 20*log10(abs(rd_image)/max(abs(rd_image(:)))));
colorbar;
xlabel('Doppler (Hz)');
ylabel('Range gate');
title('Range-Doppler (RD) Imaging Result');
colormap('jet');
caxis([-30, 0]);

%% Implementation of the proposed GSOTST algorithm

% Step 1: Initialize processing for each range cell
final_image = zeros(N_rg, N_az);

% Process each range cell
for n_rg = 50:200  % Process only meaningful range cells to save time
    % Extract signal for this range cell
    range_signal = aligned_signal(n_rg, :).';
    
    % Skip processing if signal energy is too low
    if sum(abs(range_signal).^2) < 0.01*max(sum(abs(aligned_signal).^2, 2))
        continue;
    end
    
    % Step 2: Parameter estimation using the proposed algorithm
    % Implement the algorithm from Section III (simplified version)
    
    % Step 3: Compute correlation kernel function (CKF) - Eq. (11)
    tau_values = (-N_az/2:N_az/2-1)'/N_az;  % Delay-time variable
    ckf_result = zeros(length(tau_values), N_az);
    
    for m = 1:N_az
        for tau_idx = 1:length(tau_values)
            tau = tau_values(tau_idx);
            t1 = t_m(m) + tau/2;
            t2 = t_m(m) - tau/2;
            
            % Find closest indices
            idx1 = round((t1 - t_m(1))/(t_m(2) - t_m(1))) + 1;
            idx2 = round((t2 - t_m(1))/(t_m(2) - t_m(1))) + 1;
            
            % Ensure indices are within bounds
            if idx1 >= 1 && idx1 <= N_az && idx2 >= 1 && idx2 <= N_az
                ckf_result(tau_idx, m) = range_signal(idx1) * conj(range_signal(idx2));
            end
        end
    end
    
    % Step 4: Perform GFFT along the delay-time direction - Eq. (12)
    gfft_result = zeros(size(ckf_result));
    for m = 1:N_az
        gfft_result(:, m) = fftshift(fft(ckf_result(:, m)));
    end
    
    % Step 5: Compensate the non-stationary phase - Eq. (13)
    compensated_signal = abs(gfft_result).^2;
    
    % Step 6: Implement the GSOTST transform - Eq. (15)
    tau_0 = (t_m(end) - t_m(1))/4;  % Fixed time constant (empirical)
    gsotst_result = zeros(size(compensated_signal));
    
    for tau_idx = 1:length(tau_values)
        for m = 1:N_az
            % New slow-time variable
            eta_m = t_m(m) * tau_values(tau_idx)^2 / tau_0^2;
            
            % Find closest index in original time
            idx = round((eta_m - t_m(1))/(t_m(2) - t_m(1))) + 1;
            
            % Ensure index is within bounds
            if idx >= 1 && idx <= N_az
                gsotst_result(tau_idx, m) = compensated_signal(tau_idx, idx);
            end
        end
    end
    
    % Step 7: Perform GFFT along the new slow-time direction - Eq. (16)
    doppler_result = zeros(size(gsotst_result));
    for tau_idx = 1:length(tau_values)
        doppler_result(tau_idx, :) = fftshift(fft(gsotst_result(tau_idx, :)));
    end
    
    % Step 8: Perform GFFT along the delay-time direction - Eq. (17)
    gdtf_df_result = zeros(size(doppler_result));
    for m = 1:N_az
        gdtf_df_result(:, m) = fftshift(fft(doppler_result(:, m)));
    end
    
    % Find peak in 2-D GDTF-DF domain and estimate coefficients
    [~, max_idx] = max(abs(gdtf_df_result(:)));
    [row_idx, col_idx] = ind2sub(size(gdtf_df_result), max_idx);
    
    % Map indices to frequency domain
    f_2 = (row_idx - length(tau_values)/2) / length(tau_values);
    f_m = (col_idx - N_az/2) / N_az * PRF;
    
    % Estimate c2 and c3 (Section III.A)
    c2_est = -lambda * f_2;
    c3_est = -lambda * f_m / (3 * tau_0^2);
    
    % Compensate signal phase using estimated parameters
    compensated_signal = zeros(1, N_az);
    for m = 1:N_az
        phase_comp = exp(-1j * 4*pi/lambda * (c2_est * t_m(m)^2 + c3_est * t_m(m)^3));
        compensated_signal(m) = range_signal(m) * phase_comp;
    end
    
    % Perform FFT to get focused result
    focused_result = fftshift(fft(compensated_signal));
    
    % Store the focused result in the final image
    final_image(n_rg, :) = focused_result;
end

%% Display the final ISAR image
figure;
imagesc((-N_az/2:N_az/2-1)*PRF/N_az, 1:N_rg, 20*log10(abs(final_image)/max(abs(final_image(:)))));
colorbar;
xlabel('Doppler (Hz)');
ylabel('Range gate');
title('ISAR Imaging Result using Proposed Algorithm');
colormap('jet');
caxis([-60, 0]);

%% Implement other algorithms for comparison (HAF, SGHAF, etc.)
% Here we'll implement a simplified version of the HAF algorithm for comparison

% HAF algorithm
haf_image = zeros(N_rg, N_az);
for n_rg = 50:200
    range_signal = aligned_signal(n_rg, :).';
    
    % Skip processing if signal energy is too low
    if sum(abs(range_signal).^2) < 0.01*max(sum(abs(aligned_signal).^2, 2))
        continue;
    end
    
    % Compute HAF (simplified)
    lag = round(N_az/8);
    haf_result = zeros(1, N_az-lag);
    for m = 1:N_az-lag
        haf_result(m) = range_signal(m) * conj(range_signal(m+lag));
    end
    
    % Perform FFT to estimate second-order coefficient
    haf_spectrum = fftshift(fft(haf_result, N_az));
    [~, max_idx] = max(abs(haf_spectrum));
    freq_idx = max_idx - N_az/2;
    c2_est = -lambda * freq_idx / (2*pi*lag) / N_az * PRF;
    
    % Compensate second-order phase
    comp_signal = zeros(1, N_az);
    for m = 1:N_az
        comp_signal(m) = range_signal(m) * exp(-1j * 4*pi/lambda * c2_est * t_m(m)^2);
    end
    
    % Perform FFT for final image
    haf_image(n_rg, :) = fftshift(fft(comp_signal));
end

% Display HAF result
figure;
imagesc((-N_az/2:N_az/2-1)*PRF/N_az, 1:N_rg, 20*log10(abs(haf_image)/max(abs(haf_image(:)))));
colorbar;
xlabel('Doppler (Hz)');
ylabel('Range gate');
title('HAF Algorithm Imaging Result');
colormap('jet');
caxis([-60, 0]);

%% Create helper functions for multicomponent CPS parameter estimation

% Function for estimating parameters of mono-component CPS
function [c2_est, c3_est, c1_est, amp_est] = estimate_cps_parameters(signal, t_m, lambda, PRF)
    N = length(signal);
    tau_0 = (t_m(end) - t_m(1))/4;  % Fixed time constant for GSOTST
    
    % Step 1: Compute correlation kernel function (CKF)
    tau_values = (-N/2:N/2-1)'/N;  % Delay-time variable
    ckf_result = zeros(length(tau_values), N);
    
    for m = 1:N
        for tau_idx = 1:length(tau_values)
            tau = tau_values(tau_idx);
            t1 = t_m(m) + tau/2;
            t2 = t_m(m) - tau/2;
            
            % Find closest indices
            idx1 = round((t1 - t_m(1))/(t_m(2) - t_m(1))) + 1;
            idx2 = round((t2 - t_m(1))/(t_m(2) - t_m(1))) + 1;
            
            % Ensure indices are within bounds
            if idx1 >= 1 && idx1 <= N && idx2 >= 1 && idx2 <= N
                ckf_result(tau_idx, m) = signal(idx1) * conj(signal(idx2));
            end
        end
    end
    
    % Step 2: Perform GFFT along the delay-time direction
    gfft_result = zeros(size(ckf_result));
    for m = 1:N
        gfft_result(:, m) = fftshift(fft(ckf_result(:, m)));
    end
    
    % Step 3: Compensate the non-stationary phase
    compensated_signal = abs(gfft_result).^2;
    
    % Step 4: Implement the GSOTST transform
    gsotst_result = zeros(size(compensated_signal));
    
    for tau_idx = 1:length(tau_values)
        for m = 1:N
            % New slow-time variable
            eta_m = t_m(m) * tau_values(tau_idx)^2 / tau_0^2;
            
            % Find closest index in original time
            idx = round((eta_m - t_m(1))/(t_m(2) - t_m(1))) + 1;
            
            % Ensure index is within bounds
            if idx >= 1 && idx <= N
                gsotst_result(tau_idx, m) = compensated_signal(tau_idx, idx);
            end
        end
    end
    
    % Step 5: Perform GFFT along the new slow-time direction
    doppler_result = zeros(size(gsotst_result));
    for tau_idx = 1:length(tau_values)
        doppler_result(tau_idx, :) = fftshift(fft(gsotst_result(tau_idx, :)));
    end
    
    % Step 6: Perform GFFT along the delay-time direction
    gdtf_df_result = zeros(size(doppler_result));
    for m = 1:N
        gdtf_df_result(:, m) = fftshift(fft(doppler_result(:, m)));
    end
    
    % Find peak in 2-D GDTF-DF domain and estimate coefficients
    [~, max_idx] = max(abs(gdtf_df_result(:)));
    [row_idx, col_idx] = ind2sub(size(gdtf_df_result), max_idx);
    
    % Map indices to frequency domain
    f_2 = (row_idx - length(tau_values)/2) / length(tau_values);
    f_m = (col_idx - N/2) / N * PRF;
    
    % Estimate c2 and c3 (Equation 18)
    c2_est = -lambda * f_2;
    c3_est = -lambda * f_m / (3 * tau_0^2);
    
    % Construct compensation function (Equation 28)
    H_k = zeros(1, N);
    for m = 1:N
        H_k(m) = exp(1j * 4*pi/lambda * (c2_est * t_m(m)^2 + c3_est * t_m(m)^3));
    end
    
    % Estimate c1 (Equation 29)
    comp_signal = signal .* H_k;
    comp_spectrum = fftshift(fft(comp_signal));
    [~, max_idx] = max(abs(comp_spectrum));
    f_1 = (max_idx - N/2) / N * PRF;
    c1_est = lambda * f_1 / 2;
    
    % Estimate amplitude (Equation 30)
    full_comp_signal = zeros(1, N);
    for m = 1:N
        full_comp_signal(m) = signal(m) * exp(1j * 4*pi/lambda * (c1_est * t_m(m) + c2_est * t_m(m)^2 + c3_est * t_m(m)^3));
    end
    amp_est = mean(abs(full_comp_signal));
end

% Function to implement CLEAN algorithm for multicomponent CPS
function [components] = clean_multicomponent_cps(signal, t_m, lambda, PRF, max_components, threshold)
    % CLEAN algorithm for multicomponent CPS
    % Inputs:
    %   signal - input signal (vector)
    %   t_m - slow-time vector
    %   lambda - wavelength
    %   PRF - pulse repetition frequency
    %   max_components - maximum number of components to extract
    %   threshold - stop when residual energy falls below this threshold
    % Outputs:
    %   components - struct array with fields c1, c2, c3, amp for each component
    
    % Initialize
    residual = signal;
    components = struct('c1', {}, 'c2', {}, 'c3', {}, 'amp', {});
    
    % Initial energy
    initial_energy = sum(abs(signal).^2);
    
    % Extract components using CLEAN algorithm
    for k = 1:max_components
        % Estimate parameters of strongest component
        [c2_est, c3_est, c1_est, amp_est] = estimate_cps_parameters(residual, t_m, lambda, PRF);
        
        % Store component
        components(k).c1 = c1_est;
        components(k).c2 = c2_est;
        components(k).c3 = c3_est;
        components(k).amp = amp_est;
        
        % Reconstruct component
        reconstructed = zeros(size(signal));
        for m = 1:length(t_m)
            phase = -4*pi/lambda * (c1_est * t_m(m) + c2_est * t_m(m)^2 + c3_est * t_m(m)^3);
            reconstructed(m) = amp_est * exp(1j * phase);
        end
        
        % Subtract from residual
        residual = residual - reconstructed;
        
        % Check if residual energy is below threshold
        residual_energy = sum(abs(residual).^2);
        if residual_energy < threshold * initial_energy
            break;
        end
    end
end

%% Additional comparison with SGHAF (simplified)
% This is a simplified implementation of SGHAF algorithm

% SGHAF algorithm
sghaf_image = zeros(N_rg, N_az);
for n_rg = 50:200
    range_signal = aligned_signal(n_rg, :).';
    
    % Skip processing if signal energy is too low
    if sum(abs(range_signal).^2) < 0.01*max(sum(abs(aligned_signal).^2, 2))
        continue;
    end
    
    % Compute HAF as in previous step
    lag = round(N_az/8);
    haf_result = zeros(1, N_az-lag);
    for m = 1:N_az-lag
        haf_result(m) = range_signal(m) * conj(range_signal(m+lag));
    end
    
    % Perform FFT to estimate second-order coefficient
    haf_spectrum = fftshift(fft(haf_result, N_az));
    [~, max_idx] = max(abs(haf_spectrum));
    freq_idx = max_idx - N_az/2;
    c2_est = -lambda * freq_idx / (2*pi*lag) / N_az * PRF;
    
    % Compute GHAF with scaling (simplified)
    ghaf_result = zeros(size(ckf_result));
    for m = 1:N_az
        for tau_idx = 1:length(tau_values)
            tau = tau_values(tau_idx);
            t_scaled = t_m(m) * sqrt(1 + tau);
            
            % Find closest index
            idx = round((t_scaled - t_m(1))/(t_m(2) - t_m(1))) + 1;
            
            % Ensure index is within bounds
            if idx >= 1 && idx <= N_az
                ghaf_result(tau_idx, m) = range_signal(m) * conj(range_signal(min(idx, N_az)));
            end
        end
    end
    
    % Perform 2D FFT
    sghaf_result = fftshift(fft2(ghaf_result));
    
    % Find peak and estimate both c2 and c3
    [~, max_idx] = max(abs(sghaf_result(:)));
    [row_idx, col_idx] = ind2sub(size(sghaf_result), max_idx);
    
    % Map indices to frequency domain (simplified estimation)
    f_2 = (row_idx - length(tau_values)/2) / length(tau_values);
    f_m = (col_idx - N_az/2) / N_az * PRF;
    
    c2_est_refined = -lambda * f_2;
    c3_est = -lambda * f_m / 3;
    
    % Compensate signal phase
    compensated_signal = zeros(1, N_az);
    for m = 1:N_az
        phase_comp = exp(-1j * 4*pi/lambda * (c2_est_refined * t_m(m)^2 + c3_est * t_m(m)^3));
        compensated_signal(m) = range_signal(m) * phase_comp;
    end
    
    % Perform FFT to get focused result
    sghaf_image(n_rg, :) = fftshift(fft(compensated_signal));
end

% Display SGHAF result
figure;
imagesc((-N_az/2:N_az/2-1)*PRF/N_az, 1:N_rg, 20*log10(abs(sghaf_image)/max(abs(sghaf_image(:)))));
colorbar;
xlabel('Doppler (Hz)');
ylabel('Range gate');
title('SGHAF Algorithm Imaging Result');
colormap('jet');
caxis([-60, 0]);

%% Compare all methods in one figure for visualization
figure;
subplot(2,2,1);
imagesc((-N_az/2:N_az/2-1)*PRF/N_az, 1:N_rg, 20*log10(abs(rd_image)/max(abs(rd_image(:)))));
colorbar;
xlabel('Doppler (Hz)');
ylabel('Range gate');
title('RD Algorithm');
colormap('jet');
caxis([-60, 0]);

subplot(2,2,2);
imagesc((-N_az/2:N_az/2-1)*PRF/N_az, 1:N_rg, 20*log10(abs(haf_image)/max(abs(haf_image(:)))));
colorbar;
xlabel('Doppler (Hz)');
ylabel('Range gate');
title('HAF Algorithm');
colormap('jet');
caxis([-60, 0]);

subplot(2,2,3);
imagesc((-N_az/2:N_az/2-1)*PRF/N_az, 1:N_rg, 20*log10(abs(sghaf_image)/max(abs(sghaf_image(:)))));
colorbar;
xlabel('Doppler (Hz)');
ylabel('Range gate');
title('SGHAF Algorithm');
colormap('jet');
caxis([-60, 0]);

subplot(2,2,4);
imagesc((-N_az/2:N_az/2-1)*PRF/N_az, 1:N_rg, 20*log10(abs(final_image)/max(abs(final_image(:)))));
colorbar;
xlabel('Doppler (Hz)');
ylabel('Range gate');
title('Proposed GSOTST Algorithm');
colormap('jet');
caxis([-60, 0]);

% Add overall title
sgtitle('Comparison of Different ISAR Imaging Algorithms');