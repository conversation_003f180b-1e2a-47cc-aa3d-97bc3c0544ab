%-------------------------------------------------------------------------%
%--------        基于DCFT的ISAR成像算法（适用于实测数据）         -------%
%--------                      修改于2024年                      -------%
%-------------------------------------------------------------------------%
function [R_D_DCFT] = ISAR_DCFT_RealData(hrrp, fs, PRF)
% 输入参数:
%   hrrp - 高分辨距离像数据 (复数矩阵)
%   fs   - 采样率 (Hz)
%   PRF  - 脉冲重复频率 (Hz)
% 输出参数:
%   R_D_DCFT - DCFT成像结果

% 获取数据尺寸
[Num_r, Num_tm2] = size(hrrp);

% 计算基本参数
c = 3e8;                % 光速
fc = 5.2e9;             % 载频 (可根据实际雷达参数调整)
B = fs;                 % 假设带宽等于采样率，可根据实际调整
delta_r = c/(2*B);      % 距离分辨率
tm2 = (0:Num_tm2-1)/PRF; % 时间轴

% 创建用于矩阵运算的常量向量
ones_r = ones(1, Num_r);
ones_tm2 = ones(1, Num_tm2);

% 直接使用输入的回波数据，不需要生成模拟数据
s_r_tm2 = hrrp;

% 添加平移多普勒补偿（如果需要）
% 这部分可以根据实际数据特性调整或删除
% s_phase_comp1 = exp(1j*ones_r.'*(2*pi*((1/2)*40.*tm2.*tm2+(1/6)*400.*tm2.*tm2.*tm2)));  %二阶和三阶位相项，防止相干积分时的实际参数
% s_phase_comp = exp(1j*ones_r.'*(2*pi*(190.*tm2))); %只有常数Doppler补偿项
% s_r_tm2 = s_r_tm2.*s_phase_comp.*s_phase_comp1;

% 显示距离-多普勒图像
R_D = abs((fft(s_r_tm2, Num_tm2, 2)));
R_D_max = max(max(R_D));
R_D = R_D_max - R_D;

figure;
imagesc(flipud(R_D./R_D_max));
title('距离-多普勒图像');
xlabel('多普勒单元');
ylabel('距离单元');
colorbar;

% 执行DCFT处理
FactorDop = 1;
R_D_DCFT = zeros(Num_r, FactorDop*Num_tm2);

tic
disp('开始DCFT处理...');

% 选择需要处理的距离单元范围
% 可以根据实际数据特性调整
start_range = 1;
end_range = Num_r;

for n_delta_r = start_range:end_range
    S_f = zeros(1, FactorDop*Num_tm2);   %频率分布
    s_tm_temp = s_r_tm2(n_delta_r, :);

    % 二阶参数搜索范围
    delta_alpha = 8;
    Min_alpha_sea = -2*delta_alpha;
    Max_alpha_sea = 40*delta_alpha;
    alpha_sea = Min_alpha_sea:(delta_alpha):Max_alpha_sea;
    Num_alpha_sea = length(alpha_sea);
    
    % 三阶参数搜索范围
    delta_beita = 100;
    Min_beita_sea = -5*delta_beita;
    Max_beita_sea = 24*delta_beita;
    beita_sea = Min_beita_sea:(delta_beita):Max_beita_sea;
    Num_beita_sea = length(beita_sea);

    % 查找最佳匹配值
    ISAR_f_a_b_max = 0;
    for n_alpha = 1:Num_alpha_sea
        for n_beita = 1:Num_beita_sea
            s_comp = exp(-1j*2*pi*((1/2)*alpha_sea(n_alpha)*tm2.*tm2 + (1/6)*beita_sea(n_beita)*tm2.*tm2.*tm2));
            s_dechirp = s_tm_temp .* s_comp;
            S_dechirp = fft(s_dechirp);

            if max(abs(S_dechirp)) > ISAR_f_a_b_max
                ISAR_f_a_b_max = max(abs(S_dechirp));
            end
        end
    end

    % 生成Doppler分布
    for n_alpha = 1:Num_alpha_sea
        for n_beita = 1:Num_beita_sea
            s_comp = exp(-1j*2*pi*((1/2)*alpha_sea(n_alpha)*tm2.*tm2 + (1/6)*beita_sea(n_beita)*tm2.*tm2.*tm2));
            s_dechirp = s_tm_temp .* s_comp;
            S_dechirp = fft(s_dechirp, FactorDop*Num_tm2);
            
            for n_tm = 1:FactorDop*Num_tm2
                if abs(S_dechirp(n_tm)) > (ISAR_f_a_b_max*0.0)
                    S_f(n_tm) = S_f(n_tm) + (S_dechirp(n_tm));
                end
            end
        end
    end
    
    R_D_DCFT(n_delta_r, :) = abs(S_f);
    
    % 显示处理进度
    if mod(n_delta_r, 10) == 0
        disp(['已处理 ', num2str(n_delta_r), '/', num2str(end_range), ' 距离单元...']);
    end
end

process_time = toc;
disp(['DCFT处理完成，耗时: ', num2str(process_time), ' 秒']);

% 显示DCFT成像结果
figure('name', 'DCFT成像结果');
G1 = 20*log10(abs(R_D_DCFT)./max(abs(R_D_DCFT(:))));
imagesc(G1);
caxis([-30, 0]);
grid on;
axis xy;
colorbar;
xlabel('方位向');
ylabel('距离向 (m)');
colormap jet;
title('DCFT ISAR成像结果');

% 计算对比度和熵值
contrast_value = contrast(R_D_DCFT+eps);
entropy_value = EntropyImage(R_D_DCFT+eps);

disp(['图像对比度: ', num2str(contrast_value)]);
disp(['图像熵值: ', num2str(entropy_value)]);

end

% 对比度计算函数
function C = contrast(image)
image = abs(image);
u = mean(image(:));
sigma = std(image(:));
C = sigma/u;
disp(['对比度: ', num2str(C)]);
end

% 熵值计算函数
function E = EntropyImage(image)
image = abs(image);
image = image/sum(image(:));
idx = find(image>0);
E = -sum(image(idx).*log2(image(idx)));
disp(['熵值: ', num2str(E)]);
end 