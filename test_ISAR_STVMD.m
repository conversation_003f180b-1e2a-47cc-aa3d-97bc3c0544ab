%-------------------------------------------------------------------------%
%--------        Test <PERSON>t for STVMD-based ISAR Ship Imaging        -------%
%-------------------------------------------------------------------------%
clc; clear all;

% Step 1: Load or generate ISAR radar data
% Option 1: Load existing radar data
load('shipx2.mat'); % You can replace with your own data

% Option 2: Generate synthetic data using ISARrot_trans.m
% Run ISARrot_trans first to generate s_r_tm2 data
%run('ISARrot_trans.m');
radar_data = shipx2;

% Get dimensions of radar data
[num_range_bins, num_pulses] = size(radar_data);

% Step 2: Display conventional ISAR image (FFT-based)
% Apply Doppler processing with windowing
window = hamming(num_pulses)';
window_matrix = repmat(window, num_range_bins, 1);
conventional_isar = fftshift(fft(radar_data .* window_matrix, [], 2), 2);

% Create range and Doppler axes
PRF = 1400; % Pulse Repetition Frequency from ISARrot_trans.m
c = 3e8; % Speed of light
B = 80e6; % Bandwidth from ISARrot_trans.m
range_resolution = c/(2*B);
range_axis = (-num_range_bins/2:num_range_bins/2-1) * range_resolution;
doppler_axis = (-num_pulses/2:num_pulses/2-1) * PRF/num_pulses;

% Display conventional ISAR image
figure('Name', 'Conventional ISAR Image (FFT)');
imagesc(doppler_axis, range_axis, 20*log10(abs(conventional_isar)/max(abs(conventional_isar(:)))));
colormap('jet'); colorbar;
caxis([-30 0]); % Display dynamic range of 30 dB
xlabel('Doppler Frequency (Hz)');
ylabel('Range (m)');
title('Conventional ISAR Image');
axis xy;

% Calculate conventional image contrast (for comparison)
conventional_contrast = image_contrast(conventional_isar);
fprintf('Conventional ISAR image contrast: %.4f\n', conventional_contrast);

% Step 3: Apply STVMD-based ISAR imaging
% Configure STVMD parameters
stvmd_params = struct();
stvmd_params.K = 3;              % Number of modes
stvmd_params.alpha = 2000;       % Balancing parameter
stvmd_params.tau = 1e-4;         % Time-step of dual ascent
stvmd_params.tol = 1e-7;         % Convergence tolerance
stvmd_params.n_fft = 64;         % FFT size for time windows
stvmd_params.window_length = 16; % Window length
stvmd_params.overlap = 0.5;      % Window overlap
stvmd_params.dynamic = true;     % Use dynamic STVMD
stvmd_params.max_iter = 500;     % Maximum iterations

% Apply STVMD to ISAR data
fprintf('Applying STVMD to ISAR data (this may take a few minutes)...\n');
[imf_stvmd, stvmd_isar] = ISAR_STVMD(radar_data, stvmd_params);
fprintf('STVMD processing completed.\n');

% Step 4: Compare original and direction-corrected ISAR images
% Check for mirror symmetry in conventional image
phase_sign_check = zeros(num_range_bins, 1);
for r = 1:num_range_bins
    if sum(abs(radar_data(r,:)).^2) > 1e-10
        phase_seq = unwrap(angle(radar_data(r, :)));
        phase_diff = diff(phase_seq);
        phase_sign_check(r) = mean(phase_diff);
    end
end

% Determine if conventional image needs correction
mean_phase_sign = mean(phase_sign_check(abs(phase_sign_check) > 0));
if mean_phase_sign < 0
    fprintf('Mirror symmetry detected in conventional image.\n');
    % Create a corrected conventional image
    corrected_radar = conj(radar_data);
    corrected_conventional = fftshift(fft(corrected_radar .* window_matrix, [], 2), 2);
    
    % Display both original and corrected conventional images
    figure('Name', 'Mirror Symmetry Correction (Conventional)');
    
    subplot(1,2,1);
    imagesc(doppler_axis, range_axis, 20*log10(abs(conventional_isar)/max(abs(conventional_isar(:)))));
    colormap('jet'); colorbar;
    caxis([-30 0]);
    xlabel('Doppler Frequency (Hz)');
    ylabel('Range (m)');
    title('Original (Mirror Symmetry Issue)');
    axis xy;
    
    subplot(1,2,2);
    imagesc(doppler_axis, range_axis, 20*log10(abs(corrected_conventional)/max(abs(corrected_conventional(:)))));
    colormap('jet'); colorbar;
    caxis([-30 0]);
    xlabel('Doppler Frequency (Hz)');
    ylabel('Range (m)');
    title('Corrected (Phase Conjugate Applied)');
    axis xy;
    
    % Update conventional image to corrected version
    conventional_isar = corrected_conventional;
    conventional_contrast = image_contrast(conventional_isar);
    fprintf('Conventional ISAR image corrected and contrast updated: %.4f\n', conventional_contrast);
end

% Display STVMD-enhanced ISAR image
figure('Name', 'STVMD-Enhanced ISAR Image');
imagesc(doppler_axis, range_axis, 20*log10(abs(stvmd_isar)/max(abs(stvmd_isar(:)))));
colormap('jet'); colorbar;
caxis([-30 0]); % Display dynamic range of 30 dB
xlabel('Doppler Frequency (Hz)');
ylabel('Range (m)');
title('STVMD-Enhanced ISAR Image (Direction Corrected)');
axis xy;

% Calculate STVMD-enhanced image contrast
stvmd_contrast = image_contrast(stvmd_isar);
fprintf('STVMD-enhanced ISAR image contrast: %.4f\n', stvmd_contrast);
fprintf('Contrast improvement: %.2f%%\n', 100*(stvmd_contrast-conventional_contrast)/conventional_contrast);

% Step 5: Analyze IMFs for a single range bin
% Select a range bin with strong scatterer
[~, max_bin] = max(sum(abs(radar_data).^2, 2));
range_bin = max_bin;

figure('Name', 'IMF Analysis for Single Range Bin');
subplot(3,1,1);
plot(abs(radar_data(range_bin,:)));
title(['Original Signal at Range Bin ' num2str(range_bin)]);
xlabel('Pulse Number'); ylabel('Amplitude');

% Plot IMFs for the selected range bin
colors = {'r', 'g', 'b'};
subplot(3,1,2);
hold on;
for k = 1:stvmd_params.K
    plot(abs(imf_stvmd{range_bin, k}), colors{k}, 'LineWidth', 1.5);
end
hold off;
title('STVMD Intrinsic Mode Functions (IMFs)');
xlabel('Pulse Number'); ylabel('Amplitude');
legend('IMF 1', 'IMF 2', 'IMF 3');

% Plot power spectrum of the IMFs
subplot(3,1,3);
hold on;
for k = 1:stvmd_params.K
    ps = abs(fft(imf_stvmd{range_bin, k})).^2;
    ps = ps/max(ps);
    plot(doppler_axis, fftshift(ps), colors{k}, 'LineWidth', 1.5);
end
hold off;
title('Power Spectrum of IMFs');
xlabel('Frequency (Hz)'); ylabel('Normalized Power');
legend('IMF 1', 'IMF 2', 'IMF 3');

% Add explanation about mirror symmetry correction
fprintf('\n--- Mirror Symmetry Correction Explanation ---\n');
fprintf('ISAR images can sometimes show mirror symmetry due to phase direction ambiguity.\n');
fprintf('This occurs because the Doppler frequency sign depends on the direction of motion.\n');
fprintf('The correction algorithm detects the phase trend direction and applies a\n');
fprintf('complex conjugate operation when necessary to ensure consistent imaging results.\n');
fprintf('This preserves the correct target geometry and motion direction.\n');

% Image contrast calculation function
function contrast = image_contrast(image_data)
    % Calculate the image contrast using the entropy-based measure
    abs_image = abs(image_data);
    normalized_image = abs_image / sum(abs_image(:));
    normalized_image(normalized_image < eps) = eps; % Avoid log(0)
    entropy = -sum(normalized_image(:) .* log(normalized_image(:)));
    contrast = exp(-entropy);
end 