# Project Source code
This folder contains all the MATLAB scripts and functions used to develop the final QLP in this project.

## /Quick-look Processor
This contains QLP MATALB script as well as a sub-folder containing the MATLAB app source code. 

## /Simulator
This contains pervious versions of the simulator as well as the final simulator used in the project report.

## /algorithms
This folder contains all four algorithms considered for the final QLP. Each algorithm is in a MATLAB function. 

>All previous versions of the algorithms are available and the comments in the MATLAB functions specify what each revision entailed.

## /helper functions
This folder contains the additional functions used in the QLP. 
- [imageContrast.m](https://github.com/tristynferreiro/QP4ISAR/blob/main/src/helper%20functions/imageContrast.m) is used to calculate the contrast values of the ISAR images.
- [Normalise_limitDynamicRange_ISAR_dB.m](https://github.com/tristynferreiro/QP4ISAR/blob/main/src/helper%20functions/Normalise_limitDynamicRange_ISAR_dB.m) is used during the ISAR image formation process.

