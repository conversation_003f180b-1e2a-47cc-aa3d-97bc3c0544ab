%-------------------------------------------------------------------------%
%--------        ISAR_DCFT_RealData算法演示脚本                  -------%
%--------                     2024年                             -------%
%-------------------------------------------------------------------------%
clc; clear; close all;

% 加载实测数据
disp('正在加载实测数据...');
try
    load('s_r_tm22.mat');  % 加载数据
    disp('数据加载成功！');
catch
    error('无法加载shipx2.mat文件，请确保文件存在于当前目录中');
end


hrrp=s_r_tm2;
% 设置处理参数
fs = 250000000;     % 采样率 (250MHz)
PRF = 1335.5;       % 脉冲重复频率 (1335.5Hz)
% 
% % 显示处理参数
% disp('处理参数:');
% disp(['采样率(fs): ', num2str(fs), ' Hz']);
% disp(['脉冲重复频率(PRF): ', num2str(PRF), ' Hz']);
% 
% 调用DCFT成像算法
% disp('开始ISAR DCFT成像...');
% [ISAR_image] = ISAR_DCFT_RealData(hrrp, fs, PRF);
% 
% % 保存结果
% disp('保存处理结果...');
% save('ISAR_DCFT_RealData_result.mat', 'ISAR_image');
% disp('处理完成！结果已保存到ISAR_DCFT_RealData_result.mat');

[R_D_MRADCFT, contrast_value, entropy_value, process_time] = MRADCFT_ISAR(hrrp, fs, PRF);
R_D_MRADCFT1=fftshift(R_D_MRADCFT,2);
figure('name', 'DCFT成像结果');
G1 = 20*log10(abs(R_D_MRADCFT1)./max(abs(R_D_MRADCFT1(:))));
imagesc(G1);
caxis([-30, 0]);
grid on;
axis xy;
colorbar;
xlabel('方位向');
ylabel('距离向 (m)');
colormap jet;
title('MRADCFT ISAR成像结果');

ISAR_image1=fftshift(ISAR_image,2);
figure('name', 'DCFT成像结果');
G1 = 20*log10(abs(ISAR_image1)./max(abs(ISAR_image1(:))));
imagesc(G1);
caxis([-30, 0]);
grid on;
axis xy;
colorbar;
xlabel('方位向');
ylabel('距离向 (m)');
colormap jet;
title('DCFT ISAR成像结果');



contrast(R_D_MRADCFT)
contrast(ISAR_image)
EntropyImage(R_D_MRADCFT+eps)
EntropyImage(ISAR_image)