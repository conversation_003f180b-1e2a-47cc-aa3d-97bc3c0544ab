%-------------------------------------------------------------------------%
%--------   测试增强型STVMD ISAR成像算法                 -------------------%
%--------   与传统方法对比，验证对称问题和散焦问题的解决   -------------------%
%-------------------------------------------------------------------------%

clear all; close all; clc;

%% 1. 加载或生成测试数据
fprintf('=== 增强型STVMD ISAR成像算法测试 ===\n');

% 可以使用现有的舰船数据或生成的仿真数据
if exist('shipx2.mat', 'file')
    fprintf('加载舰船数据 shipx2.mat...\n');
    load('shipx2.mat');
    radar_data = shipx2;
    clear shipx2;
elseif exist('s_r_tm2', 'var') && ~isempty(s_r_tm2)
    fprintf('使用仿真生成的雷达数据...\n');
    radar_data = s_r_tm2;
else
    fprintf('生成测试用的仿真雷达数据...\n');
    radar_data = generate_test_isar_data();
end

fprintf('雷达数据尺寸: %d x %d\n', size(radar_data));

%% 2. 设置算法参数
fprintf('\n设置算法参数...\n');

% 增强型STVMD参数
stvmd_params = struct();
stvmd_params.K = 3;                    % 模态数量
stvmd_params.alpha = 2000;             % 平衡参数
stvmd_params.tau = 0.1;                % 步长参数
stvmd_params.tol = 1e-7;               % 收敛容限
stvmd_params.max_iter = 300;           % 最大迭代次数

% 多尺度窗口参数
stvmd_params.window_sizes = [16, 32, 64];
stvmd_params.overlap_ratio = 0.75;
stvmd_params.window_type = 'hamming';  % 窗函数类型

% 自适应参数
stvmd_params.adaptive.enable = true;
stvmd_params.adaptive.energy_factor = 0.5;

% 成像参数（解决对称问题的关键）
stvmd_params.imaging.remove_symmetry = true;
stvmd_params.imaging.window_azimuth = 'kaiser';
stvmd_params.imaging.beta = 2.5;

% 全局优化参数
stvmd_params.global_optimization.enable = true;
stvmd_params.global_optimization.iterations = 2;

% 显示进度
stvmd_params.display_progress = true;

%% 3. 传统FFT成像（用于对比）
fprintf('\n执行传统FFT成像...\n');
tic;
traditional_image = fftshift(fft(radar_data, [], 2), 2);
time_traditional = toc;
fprintf('传统FFT成像时间: %.2f 秒\n', time_traditional);

%% 4. 执行增强型STVMD成像
fprintf('\n执行增强型STVMD成像...\n');
tic;
[stvmd_image, compensated_data, phase_errors, processing_info] = ...
    ISAR_STVMD_Enhanced(radar_data, stvmd_params);
time_stvmd = toc;
fprintf('增强型STVMD成像时间: %.2f 秒\n', time_stvmd);

%% 5. 图像质量评估
fprintf('\n评估图像质量...\n');

% 计算图像对比度
contrast_traditional = calculate_image_contrast(traditional_image);
contrast_stvmd = calculate_image_contrast(stvmd_image);

% 计算图像熵
entropy_traditional = calculate_image_entropy_2d(abs(traditional_image));
entropy_stvmd = calculate_image_entropy_2d(abs(stvmd_image));

% 计算图像锐度
sharpness_traditional = calculate_image_sharpness(traditional_image);
sharpness_stvmd = calculate_image_sharpness(stvmd_image);

% 打印评估结果
fprintf('\n=== 图像质量评估结果 ===\n');
fprintf('传统FFT成像:\n');
fprintf('  对比度: %.4f\n', contrast_traditional);
fprintf('  图像熵: %.4f\n', entropy_traditional);
fprintf('  锐度:   %.4f\n', sharpness_traditional);

fprintf('增强型STVMD成像:\n');
fprintf('  对比度: %.4f (提升: %.2f%%)\n', contrast_stvmd, ...
    100*(contrast_stvmd-contrast_traditional)/contrast_traditional);
fprintf('  图像熵: %.4f (减少: %.2f%%)\n', entropy_stvmd, ...
    100*(entropy_traditional-entropy_stvmd)/entropy_traditional);
fprintf('  锐度:   %.4f (提升: %.2f%%)\n', sharpness_stvmd, ...
    100*(sharpness_stvmd-sharpness_traditional)/sharpness_traditional);

%% 6. 对称性分析
fprintf('\n分析图像对称性...\n');
symmetry_traditional = analyze_image_symmetry(traditional_image);
symmetry_stvmd = analyze_image_symmetry(stvmd_image);

fprintf('传统FFT成像对称性指标: %.4f\n', symmetry_traditional);
fprintf('增强型STVMD成像对称性指标: %.4f (减少: %.2f%%)\n', symmetry_stvmd, ...
    100*(symmetry_traditional-symmetry_stvmd)/symmetry_traditional);

%% 7. 显示处理信息
fprintf('\n=== 处理信息统计 ===\n');
fprintf('收敛距离单元比例: %.1f%%\n', 100*mean(processing_info.convergence_info));
fprintf('平均选择模态: %.1f\n', mean(processing_info.selected_modes));
fprintf('平均熵减少量: %.4f\n', mean(processing_info.entropy_reduction));

%% 8. 可视化结果
fprintf('\n生成可视化结果...\n');

% 设置显示动态范围
dynamic_range_db = 35;

% 图1: 传统FFT成像结果
figure('Name', '传统FFT ISAR成像', 'Position', [100, 500, 600, 450]);
traditional_db = 20*log10(abs(traditional_image)/max(abs(traditional_image(:))));
imagesc(traditional_db);
caxis([-dynamic_range_db, 0]);
colormap(jet); colorbar;
title('传统FFT ISAR成像结果', 'FontSize', 14, 'FontWeight', 'bold');
xlabel('方位单元', 'FontSize', 12);
ylabel('距离单元', 'FontSize', 12);
axis xy;

% 图2: 增强型STVMD成像结果
figure('Name', '增强型STVMD ISAR成像', 'Position', [750, 500, 600, 450]);
stvmd_db = 20*log10(abs(stvmd_image)/max(abs(stvmd_image(:))));
imagesc(stvmd_db);
caxis([-dynamic_range_db, 0]);
colormap(jet); colorbar;
title('增强型STVMD ISAR成像结果', 'FontSize', 14, 'FontWeight', 'bold');
xlabel('方位单元', 'FontSize', 12);
ylabel('距离单元', 'FontSize', 12);
axis xy;

% 图3: 对比分析
figure('Name', '对比分析', 'Position', [425, 50, 800, 600]);

% 子图1: 图像对比度对比
subplot(2,3,1);
bar([contrast_traditional, contrast_stvmd]);
set(gca, 'XTickLabel', {'传统FFT', 'STVMD增强'});
title('图像对比度对比');
ylabel('对比度');
grid on;

% 子图2: 图像熵对比
subplot(2,3,2);
bar([entropy_traditional, entropy_stvmd]);
set(gca, 'XTickLabel', {'传统FFT', 'STVMD增强'});
title('图像熵对比');
ylabel('熵值');
grid on;

% 子图3: 图像锐度对比
subplot(2,3,3);
bar([sharpness_traditional, sharpness_stvmd]);
set(gca, 'XTickLabel', {'传统FFT', 'STVMD增强'});
title('图像锐度对比');
ylabel('锐度');
grid on;

% 子图4: 对称性对比
subplot(2,3,4);
bar([symmetry_traditional, symmetry_stvmd]);
set(gca, 'XTickLabel', {'传统FFT', 'STVMD增强'});
title('图像对称性对比');
ylabel('对称性指标');
grid on;

% 子图5: 处理时间对比
subplot(2,3,5);
bar([time_traditional, time_stvmd]);
set(gca, 'XTickLabel', {'传统FFT', 'STVMD增强'});
title('处理时间对比');
ylabel('时间 (秒)');
grid on;

% 子图6: 相位误差分布
subplot(2,3,6);
phase_error_std = std(phase_errors, 0, 2);
plot(phase_error_std);
title('各距离单元相位误差标准差');
xlabel('距离单元');
ylabel('相位误差标准差');
grid on;

%% 9. 保存结果
fprintf('\n保存结果...\n');
save('STVMD_ISAR_results.mat', 'stvmd_image', 'traditional_image', ...
     'compensated_data', 'phase_errors', 'processing_info', ...
     'contrast_traditional', 'contrast_stvmd', ...
     'entropy_traditional', 'entropy_stvmd', ...
     'sharpness_traditional', 'sharpness_stvmd', ...
     'symmetry_traditional', 'symmetry_stvmd');

fprintf('\n=== 测试完成 ===\n');
fprintf('结果已保存到 STVMD_ISAR_results.mat\n');

%% ========================================================================
%% 辅助函数定义
%% ========================================================================

function test_data = generate_test_isar_data()
% 生成测试用的ISAR数据

% 基本参数
N_range = 101;
N_azimuth = 701;
SNR_db = 20;

% 创建理想的点目标回波
test_data = zeros(N_range, N_azimuth);

% 添加几个强散射点
strong_scatterers = [
    25, 200, 1.0;     % [距离索引, 方位索引, 强度]
    40, 300, 0.8;
    60, 400, 0.9;
    75, 500, 0.7;
    85, 600, 0.6
];

for i = 1:size(strong_scatterers, 1)
    r_idx = strong_scatterers(i, 1);
    a_idx = strong_scatterers(i, 2);
    amplitude = strong_scatterers(i, 3);
    
    % 添加非线性相位误差（模拟运动误差）
    phase_error = 0.5 * sin(2*pi*(0:N_azimuth-1)/100) + ...
                  0.2 * sin(2*pi*(0:N_azimuth-1)/50);
    
    test_data(r_idx, :) = amplitude * exp(1j * phase_error);
end

% 添加噪声
noise_power = 10^(-SNR_db/10);
noise = sqrt(noise_power/2) * (randn(N_range, N_azimuth) + 1j*randn(N_range, N_azimuth));
test_data = test_data + noise;

end

function contrast = calculate_image_contrast(image)
% 计算图像对比度

magnitude = abs(image);
mu = mean(magnitude(:));
sigma = std(magnitude(:));
contrast = sigma / mu;

end

function entropy = calculate_image_entropy_2d(image)
% 计算二维图像熵

% 归一化
normalized_image = image / sum(image(:));

% 计算熵
log_values = log2(normalized_image + eps);
entropy = -sum(normalized_image(:) .* log_values(:));

end

function sharpness = calculate_image_sharpness(image)
% 计算图像锐度（基于梯度）

magnitude = abs(image);

% 计算梯度
[Gx, Gy] = gradient(magnitude);
gradient_magnitude = sqrt(Gx.^2 + Gy.^2);

% 锐度定义为梯度幅度的均值
sharpness = mean(gradient_magnitude(:));

end

function symmetry_index = analyze_image_symmetry(image)
% 分析图像对称性

magnitude = abs(image);
[M, N] = size(magnitude);

% 计算左右对称性
left_half = magnitude(:, 1:floor(N/2));
right_half = magnitude(:, end:-1:ceil(N/2)+1);

% 调整尺寸
min_cols = min(size(left_half, 2), size(right_half, 2));
left_half = left_half(:, 1:min_cols);
right_half = right_half(:, 1:min_cols);

% 计算对称性指标（均方误差）
symmetry_index = mean((left_half(:) - right_half(:)).^2);

end 