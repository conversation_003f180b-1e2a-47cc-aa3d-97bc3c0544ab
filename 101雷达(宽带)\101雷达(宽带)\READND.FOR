C The program reads the narrow-band data from an ISAR data file, displays 
C them on the screen  and saves them to another decimal data file.
C
	CHARACTER*20 NAME
	CHARACTER C(630),B(2)
	EQUIVALENCE (II,B(1))
	INTEGER*4 LV
C        
	WRITE(*,*)'***************************************************' 
	WRITE(*,*)'The program reads the narrow-band data from an ISAR'
	WRITE(*,*)'data  file, displays  them  on the screen and saves'
	WRITE(*,*)'them to another decimal data file.'
	WRITE(*,*)'***************************************************'
	WRITE(*,*)'Now, please, input the file name to be read.'
	READ(*,998) NAME
	WRITE(*,*)'---------------------------------------------------'
998     FORMAT(A20)
	OPEN(1,FILE=NAME, STATUS='OLD',ACCESS='DIRECT',
     &  RECL=630,FORM='UNFORMATTED')
	WRITE(*,*)'Please, input the file name to put narrow-band data.'
	READ(*,998) NAME
	WRITE(*,*)'---------------------------------------------------'
	OPEN(5,FILE=NAME, STATUS='NEW')
	WRITE(*,*)'How many records do you want to read ?'
	READ(*,*) N
	WRITE(*,*)'---------------------------------------------------'
997     FORMAT(A3)
	WRITE(5,*)'   K  ','   DAGC   ','   R  ',
     &  '        V      Azimuth  Elevation    TVX       TVY'
	WRITE(5,*)'      ','   (dB)   ','  (km)  ',
     &  '    (m/s)     (Deg.)    (Deg.)    (Deg.)    (Deg.)'
C        
	DO 10 K=1,N
	READ(1) C
C Calculating the DAGC value (dB)
	B(1)=C(613)
	II=II*2
	DAGC=ICHAR(B(1))/2
	IF(DAGC.GT.63) DAGC=DAGC-48
	B(2)=CHAR(0)
C Checking the validation of the data
	SUM1=0
	SUM2=0
	DO 30 I=1,8
	SUM1=SUM1+ICHAR(C(613+2*I))
	SUM2=SUM2+ICHAR(C(614+2*I))
30      CONTINUE
	ID=SUM2/256
	DSUM2=SUM2-ID*256
	IF(DSUM2.NE.0) THEN
	WRITE(*,990) K
990     FORMAT(1X,'The ',I4,'th record of narrow-band data',
     &  ' is invalid !')
	NV=NV+1
	GOTO 10
	ELSE
	SUM1=SUM1+ID
	IE=SUM1/256
	DSUM1=SUM1-IE*256
		IF(DSUM1.NE.0) THEN
		WRITE(*,990) K
		NV=NV+1
		GOTO 10
		else
		ENDIF
	ENDIF
C Calculating the range value (m)
	B(1)=C(618)
	II=II*32
	R=(256*(8*ICHAR(B(1))+ICHAR(C(615)))+ICHAR(C(616)))*0.9375
	B(2)=CHAR(0)
C Calculating the velocity value (m/s)
	B(1)=C(619)
	II=II*8
	LV=(256*(32*ICHAR(B(1))+ICHAR(C(620)))+ICHAR(C(617)))
	  IF(LV.GE.1048576) LV=1048576-LV
	V=LV*0.014204545
	B(2)=CHAR(0)
C Calculating the azimuth value (deg.)
	B(1)=C(624)
	II=II*32
	AZ=(256*(8*ICHAR(B(1))+ICHAR(C(621)))+ICHAR(C(622)))
     &  *0.0006866455D0
	B(2)=CHAR(0)
C Calculating the elevation value (deg.)
	B(1)=C(625)
	II=II*32
	EL=(256*(8*ICHAR(B(1))+ICHAR(C(626)))+ICHAR(C(623)))
     &  *0.0006866455D0
	B(2)=CHAR(0)
C Calculating the TV correction
	B(1)=C(627)
	II=II*2
	TVY=ICHAR(B(1))/2
	KX=ICHAR(C(627))
	IF(KX.GT.127) TVY=-TVY
	B(2)=CHAR(0)
C
	B(1)=C(628)
	II=II*2
	TVX=ICHAR(B(1))/2
	KX=ICHAR(C(628))
	IF(KX.GT.127) TVX=-TVX
	B(2)=CHAR(0)
C
        WRITE(5,991) K,DAGC,R,V,AZ,EL,TVX,TVY
	IF(K.EQ.1) WRITE(*,*)'   K  ','   DAGC   ','   R  ',
     &  '        V      Azimuth  Elevation    TVX       TVY'
	IF(K.EQ.1) WRITE(*,*)'      ','   (dB)   ','  (km)  ',
     &  '    (m/s)     (Deg.)    (Deg.)    (Deg.)    (Deg.)'
	WRITE(*,991) K,DAGC,R,V,AZ,EL,TVX,TVY
10      CONTINUE
991     FORMAT(I5,7F10.3)
	WRITE(*,*)'*************************************************',
     &  '*****************************'
	WRITE(*,999)N,NV
999     FORMAT(1X,'Total read record number is',I6,', Among them invalid
     & record number is',I5,'.')
	STOP
	END
