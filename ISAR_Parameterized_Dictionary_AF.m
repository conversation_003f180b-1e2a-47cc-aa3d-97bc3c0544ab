%% ISAR Parameterized Dictionary-based Autofocus Method for 3D Complex Motion Compensation
% This method addresses the problem of ISAR image defocusing caused by complex 3D
% rotational motion with time-varying angular velocities. The method uses a 
% parameterized dictionary approach with sparse representation to estimate global 
% motion parameters and recover a focused ISAR image.
%
% Implementation based on the proposed approach for handling cubic phase signals 
% resulting from complex rotational motion.
%
% Author: <PERSON> (Based on user's proposed algorithm)
% Date: 2023

%% Main function 
function [focused_image, param_est, image_contrast] = ISAR_Parameterized_Dictionary_AF(radar_data, options)
% ISAR_Parameterized_Dictionary_AF - Dictionary-based autofocus method for complex motion
%
% Inputs:
%   radar_data - Range compressed and envelope aligned radar data
%               (Range bins x Azimuth/slow time samples)
%   options    - Structure with parameters:
%       .max_iterations - Maximum number of iterations (default: 10)
%       .tolerance      - Convergence tolerance (default: 1e-4)
%       .lambda_x       - L1 regularization parameter for image sparsity (default: 0.1)
%       .lambda_m       - Regularization parameter for motion parameters (default: 0.01)
%       .PRF            - Pulse repetition frequency (default: 1400)
%       .fc             - Carrier frequency (default: 5.2e9)
%       .c              - Speed of light (default: 3e8)
%       .initial_omega  - Initial angular velocity (default: [0.05, 0.2, 0.05])
%       .initial_zeta   - Initial angular acceleration (default: [0, 0, 0])
%       .initial_kappa  - Initial angular jerk (default: [0, 0, 0])
%
% Outputs:
%   focused_image - The focused ISAR image after motion compensation
%   param_est     - Estimated global motion parameters {omega, zeta, kappa}
%   image_contrast - Image contrast metric of the focused image

%% Set default parameters if not specified
if nargin < 2
    options = struct();
end

if ~isfield(options, 'max_iterations')
    options.max_iterations = 10;
end

if ~isfield(options, 'tolerance')
    options.tolerance = 1e-4;
end

if ~isfield(options, 'lambda_x')
    options.lambda_x = 0.1; 
end

if ~isfield(options, 'lambda_m')
    options.lambda_m = 0.01; 
end

if ~isfield(options, 'PRF')
    options.PRF = 1400; % Default PRF from ISARrot_trans.m
end

if ~isfield(options, 'fc')
    options.fc = 5.2e9; % Default carrier frequency from ISARrot_trans.m
end

if ~isfield(options, 'c')
    options.c = 3e8;    % Speed of light
end

if ~isfield(options, 'display')
    options.display = 'iter'; % 'iter', 'final', or 'none'
end

if ~isfield(options, 'initial_omega')
    options.initial_omega = [0.05, 0.2, 0.05]; % Initial angular velocity from ISARrot_trans.m
end

if ~isfield(options, 'initial_zeta')
    options.initial_zeta = [0, 0, 0]; % Initial angular acceleration
end

if ~isfield(options, 'initial_kappa')
    options.initial_kappa = [0, 0, 0]; % Initial angular jerk
end

%% Extract radar parameters
[num_range_bins, num_pulses] = size(radar_data);
lambda = options.c / options.fc;    % Wavelength
PRF = options.PRF;                  % Pulse repetition frequency
tm = (0:num_pulses-1)/PRF;          % Slow time array
Ns = num_pulses;                    % Number of slow-time samples

%% Initialize parameters
% Global motion parameters
M_k = struct('omega', options.initial_omega, ...
             'zeta', options.initial_zeta, ...
             'kappa', options.initial_kappa);

% Constants for phase parameter calculation
C_f = -2/lambda;        % Doppler constant
C_alpha = -2/lambda;    % Chirp rate constant
C_beta = -2/lambda;     % Cubic phase constant

% Initialize sparse image
[X_k, ~] = RDimage_init(radar_data);  % Initialize with standard RD image

% Convert X_k to sparse representation format (for dictionary approach)
% We'll use only the magnitude of the initial RD image as our initial sparse coefficients
X_k = sum(abs(X_k), 2);  % Sum across azimuth to get range profile

% For convergence checking
prev_contrast = 0;
contrast_history = zeros(options.max_iterations, 1);

% For displaying progress
if strcmp(options.display, 'iter')
    figure('Name', 'Dictionary-based ISAR Autofocus Iterations');
end

%% Main iterative algorithm
for k = 1:options.max_iterations
    % Step a: Construct parametric dictionary with current motion parameters
    Psi = constructDictionary(M_k, num_range_bins, num_pulses, tm, Ns, C_f, C_alpha, C_beta);
    
    % Step b: Solve for sparse image coefficients using FISTA
    X_k1 = solveSparseProblem(radar_data, Psi, options.lambda_x, X_k);
    
    % Step c: Update motion parameters using strong scatterers
    M_k1 = updateMotionParameters(radar_data, X_k1, M_k, tm, Ns, C_f, C_alpha, C_beta, options.lambda_m);
    
    % Generate focused image for this iteration
    % Reconstruct the focused signal using the dictionary
    focused_signal = reshape(Psi * X_k1, num_range_bins, num_pulses);
    
    % Apply FFT to get focused image
    focused_img_k = fftshift(fft(focused_signal, [], 2), 2);
    
    % Calculate image contrast for convergence check
    current_contrast = calculateImageContrast(focused_img_k);
    contrast_history(k) = current_contrast;
    
    % Display progress
    if strcmp(options.display, 'iter')
        subplot(2, 2, 1);
        imagesc(20*log10(abs(focused_img_k)./max(abs(focused_img_k(:)))));
        caxis([-30, 0]);
        colorbar;
        title(sprintf('Iteration %d - Contrast: %.4f', k, current_contrast));
        
        subplot(2, 2, 2);
        plot(1:k, contrast_history(1:k), '-o');
        xlabel('Iteration'); ylabel('Contrast');
        title('Contrast History');
        
        subplot(2, 2, 3:4);
        plotMotionParams(M_k1, k);
        
        drawnow;
    end
    
    % Check for convergence
    if abs(current_contrast - prev_contrast) < options.tolerance
        if strcmp(options.display, 'iter') || strcmp(options.display, 'final')
            fprintf('Converged at iteration %d. Final contrast: %.4f\n', k, current_contrast);
        end
        break;
    end
    
    % Update variables for next iteration
    prev_contrast = current_contrast;
    X_k = X_k1;
    M_k = M_k1;
end

% Generate final outputs
% Reconstruct the focused signal using the dictionary
focused_signal = reshape(Psi * X_k, num_range_bins, num_pulses);

% Apply FFT to get focused image
focused_image = fftshift(fft(focused_signal, [], 2), 2);
param_est = M_k;
image_contrast = current_contrast;

% Display final results if requested
if strcmp(options.display, 'final')
    figure('Name', 'ISAR Autofocus Results');
    
    % Original (unfocused) image using standard RD imaging
    [unfocused_img, unfocused_contrast] = RDimage_init(radar_data);
    
    subplot(1, 2, 1);
    imagesc(20*log10(abs(unfocused_img)./max(abs(unfocused_img(:)))));
    caxis([-30, 0]);
    title(sprintf('Unfocused Image (Contrast: %.4f)', unfocused_contrast));
    xlabel('Azimuth'); ylabel('Range');
    colorbar;
    
    % Focused image
    subplot(1, 2, 2);
    imagesc(20*log10(abs(focused_image)./max(abs(focused_image(:)))));
    caxis([-30, 0]);
    title(sprintf('Focused Image (Contrast: %.4f)', image_contrast));
    xlabel('Azimuth'); ylabel('Range');
    colorbar;
end

end

%% Helper Functions

% Function to initialize with RD imaging
function [image, contrast] = RDimage_init(radar_data)
    % Apply simple Range-Doppler imaging to initialize
    [num_range_bins, num_pulses] = size(radar_data);
    
    % Apply Hamming window
    window = repmat(hamming(num_pulses)', num_range_bins, 1);
    windowed_data = radar_data .* window;
    
    % FFT along azimuth dimension
    image = fftshift(fft(windowed_data, [], 2), 2);
    
    % Calculate contrast for initialization
    contrast = calculateImageContrast(image);
end

% Function to calculate image contrast
function contrast = calculateImageContrast(image)
    % Calculate image contrast using ratio of standard deviation to mean
    I = abs(image);
    contrast = std(I(:)) / mean(I(:));
end

% Function to construct the parametric dictionary
function Psi = constructDictionary(M, num_range_bins, num_pulses, tm, Ns, C_f, C_alpha, C_beta)
    % Creates dictionary matrix based on current motion parameters
    % Each column represents the signature of a potential scatterer
    
    % Extract motion parameters
    omega = M.omega;
    zeta = M.zeta;
    kappa = M.kappa;
    
    % Preallocate the dictionary matrix
    Psi = zeros(num_range_bins * num_pulses, num_range_bins);
    
    % For each potential scatterer position
    for r_idx = 1:num_range_bins
        % Create relative position vector (simplified 3D model)
        % We use a simplified model where each range bin corresponds to a different
        % cross-range position, assuming uniform grid
        r_pos = [(r_idx - num_range_bins/2), 0, 0];  % Simplified position vector
        
        % Calculate phase parameters for this scatterer
        f_p = C_f * (omega * r_pos');
        alpha_p = C_alpha * (zeta * r_pos');
        beta_p = C_beta * (kappa * r_pos');
        
        % Generate phase history for this scatterer
        phase = 2*pi*(f_p*tm + (alpha_p/(2*Ns))*tm.^2 + (beta_p/(6*Ns^2))*tm.^3);
        signal = exp(1j * phase);
        
        % Create a template for this scatterer's response
        temp = zeros(num_range_bins, num_pulses);
        temp(r_idx, :) = signal;
        
        % Add to dictionary
        Psi(:, r_idx) = temp(:);
    end
end

% Function to solve the sparse representation problem
function X = solveSparseProblem(radar_data, Psi, lambda, X_init)
    % Solves the L1-regularized least squares problem using FISTA algorithm
    
    % Reshape radar data to vector form
    y = radar_data(:);
    
    % Ensure consistent dimensions
    if nargin < 4 || isempty(X_init)
        X = zeros(size(Psi, 2), 1);
    else
        % Make sure X_init is a column vector of the right size
        if length(X_init) ~= size(Psi, 2)
            % Reshape or pad/truncate as needed
            if length(X_init) > size(Psi, 2)
                X = X_init(1:size(Psi, 2));
            else
                X = [X_init; zeros(size(Psi, 2) - length(X_init), 1)];
            end
        else
            X = X_init;
        end
    end
    
    % Display dimensions for debugging
    fprintf('Dictionary dimensions: %d x %d\n', size(Psi, 1), size(Psi, 2));
    fprintf('Data vector dimension: %d x 1\n', length(y));
    fprintf('Initial X dimension: %d x 1\n', length(X));
    
    % FISTA parameters
    L = power_iteration(Psi, 10) * 1.1;  % Lipschitz constant estimate
    if L == 0 || isnan(L)
        L = 1; % Fallback if power iteration fails
    end
    
    t = 1;
    z = X;
    
    % FISTA iterations
    max_iter = 50; % Reduced from 100 for faster execution
    for i = 1:max_iter
        X_prev = X;
        
        % Gradient step with explicit steps to avoid dimension mismatch
        Psi_z = Psi * z;
        residual = Psi_z - y;
        grad = Psi' * residual;
        
        X_temp = z - (1/L) * grad;
        
        % Soft thresholding (proximal operator)
        X = soft_threshold(X_temp, lambda/L);
        
        % Update FISTA parameters
        t_new = (1 + sqrt(1 + 4*t^2)) / 2;
        z = X + ((t - 1)/t_new) * (X - X_prev);
        t = t_new;
        
        % Check convergence
        if norm(X - X_prev) / (norm(X_prev) + eps) < 1e-4
            fprintf('FISTA converged after %d iterations\n', i);
            break;
        end
    end
end

% Function for soft thresholding (proximal operator for L1 norm)
function y = soft_threshold(x, lambda)
    y = sign(x) .* max(abs(x) - lambda, 0);
end

% Function to estimate Lipschitz constant for FISTA
function L = power_iteration(A, num_iter)
    % Estimate the largest singular value of A'*A using power iteration
    [m, n] = size(A);
    
    % Safety check for empty matrices
    if m == 0 || n == 0
        L = 0;
        return;
    end
    
    % Initialize random vector
    x = randn(n, 1);
    x = x / (norm(x) + eps);
    
    for i = 1:num_iter
        % Safety check for zero vector
        if norm(x) < eps
            x = randn(n, 1);
            x = x / (norm(x) + eps);
        end
        
        % Apply A'*A to x
        y = A' * (A * x);
        
        % Normalize
        x_new = y / (norm(y) + eps);
        
        % Check convergence
        if norm(x_new - x) < 1e-6
            break;
        end
        x = x_new;
    end
    
    % Compute the Rayleigh quotient
    L = norm(A * x)^2;
end

% Function to update motion parameters
function M_new = updateMotionParameters(radar_data, X, M_current, tm, Ns, C_f, C_alpha, C_beta, lambda_m)
    % Extract strong scatterers from the current image estimate
    [num_range_bins, num_pulses] = size(radar_data);
    
    % Reshape X if necessary
    if ~isvector(X)
        X = X(:);
    end
    
    img_power = abs(X).^2;
    threshold = 0.3 * max(img_power);  % Threshold for strong scatterers
    strong_idx = find(img_power > threshold);
    
    % If no strong scatterers found, keep current parameters
    if isempty(strong_idx)
        fprintf('No strong scatterers found, keeping current parameters\n');
        M_new = M_current;
        return;
    end
    
    % Extract strong scatterer signals
    strong_signals = zeros(length(strong_idx), num_pulses);
    for i = 1:length(strong_idx)
        r_idx = strong_idx(i);
        strong_signals(i, :) = radar_data(r_idx, :);
    end
    
    % Create position vectors for strong scatterers (simplified model)
    positions = zeros(length(strong_idx), 3);
    for i = 1:length(strong_idx)
        r_idx = strong_idx(i);
        positions(i, :) = [(r_idx - num_range_bins/2), 0, 0];  % Simplified position vector
    end
    
    % Estimate phase parameters for each strong scatterer using cubic phase function
    f_est = zeros(length(strong_idx), 1);
    alpha_est = zeros(length(strong_idx), 1);
    beta_est = zeros(length(strong_idx), 1);
    
    for i = 1:length(strong_idx)
        signal = strong_signals(i, :);
        [f_est(i), alpha_est(i), beta_est(i)] = estimateCubicPhaseParams(signal, tm, Ns);
    end
    
    % Update motion parameters using least squares with regularization
    % Formulating as: phase_params = C * position_matrix * motion_params
    R_mat = positions;
    
    % Add small offset to avoid singular matrices
    reg_matrix = lambda_m * eye(3);
    
    % For omega (Doppler)
    b_vec = f_est / C_f;
    omega_new = (R_mat' * R_mat + reg_matrix) \ (R_mat' * b_vec);
    
    % For zeta (Chirp rate)
    b_vec = alpha_est / C_alpha;
    zeta_new = (R_mat' * R_mat + reg_matrix) \ (R_mat' * b_vec);
    
    % For kappa (Cubic phase)
    b_vec = beta_est / C_beta;
    kappa_new = (R_mat' * R_mat + reg_matrix) \ (R_mat' * b_vec);
    
    % Package the updated parameters
    M_new = struct('omega', omega_new', ...
                   'zeta', zeta_new', ...
                   'kappa', kappa_new');
                   
    % Print updated parameters
    fprintf('Updated motion parameters:\n');
    fprintf('  omega: [%.4f, %.4f, %.4f]\n', M_new.omega);
    fprintf('  zeta: [%.4f, %.4f, %.4f]\n', M_new.zeta);
    fprintf('  kappa: [%.4f, %.4f, %.4f]\n', M_new.kappa);
end

% Function to estimate cubic phase parameters from a signal
function [f, alpha, beta] = estimateCubicPhaseParams(signal, tm, Ns)
    % Simple parameter estimator for cubic phase signal
    % For a more sophisticated approach, consider implementing HAF or PHAF
    
    % Extract phase
    phase = unwrap(angle(signal));
    
    % Fit cubic polynomial to the phase
    p = polyfit(tm, phase, 3);
    
    % Extract parameters
    % Phase model: 2*pi*(f*tm + (alpha/(2*Ns))*tm^2 + (beta/(6*Ns^2))*tm^3)
    beta = p(1) * 6 * Ns^2 / (2*pi);  % Cubic term
    alpha = p(2) * 2 * Ns / (2*pi);   % Quadratic term
    f = p(3) / (2*pi);                 % Linear term
end

% Function to plot motion parameters for visualization
function plotMotionParams(M, iteration)
    % Plot the current motion parameters
    
    % Extract parameters
    omega = M.omega;
    zeta = M.zeta;
    kappa = M.kappa;
    
    % Create parameter vectors
    params = [omega; zeta; kappa];
    param_names = {'ω_x', 'ω_y', 'ω_z', 'ζ_x', 'ζ_y', 'ζ_z', 'κ_x', 'κ_y', 'κ_z'};
    
    % Plot parameter values
    bar(params(:));
    xticks(1:9);
    xticklabels(param_names);
    ylabel('Parameter Value');
    title(['Motion Parameters at Iteration ', num2str(iteration)]);
    grid on;
end 