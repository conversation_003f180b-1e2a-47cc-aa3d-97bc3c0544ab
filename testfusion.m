% 测试脚本: 比较传统方法、VMD-DCFT串联方法和VMD-ADMM-DCFT融合方法
% 加载测试数据
clc; clear;
load shipx2.mat; % 舰船雷达回波数据
Pos = [-10 -1 0;-9 -1 0;-8 -1 0;-7 -1 0;-6 -1 0;-5 -1 0;-3.75 -1 0;-3 -1 0;-2 -1 0;-1 -1 0;...   %(10��) 
       0 -1 0;...      %(1��)
       1 -1 0;2 -1 0;3 -1 0;4 -1 0;5 -1 0;6 -1 0;7 -1 0;8 -1 0;9 -1 0;10 -1 0;...  %(10��)
       -9.5 0.2 0.5;...    %(1��)
       -9.5 1.2 0.5;-9 1 0;-8 1 0;-7 1 0;-6 1 0;-5.2 1.2 0;-4.1 1 0;-3 1 0;-2 1 0;-1 1 0;...  %(10��)
       0 1 0;...      %(1��)
       1 1 0;2 1 0;3 1 0;4 1 0;5 1 0;6 1 0;7 1 0;8 1 0;9 1 0;10 1 0;...     %(10��)
       10.5 -0.75 0;10.5 0.75 0;11 -0.5 0;11 0.5 0;11.5 0 0;... %�ױ�        %(5��)
       9.5 0.5 0.5;9.5 -0.5 0.5;9 0 0.5;8.5 0.5 0.5;8.5 -0.5 0.5;... %��ͷ��   %(5��) 9.5 0 0.5;9 0.5 0.5;9 -0.5 0.5;8.5 0 0.5;
       5 0 0.5;5 0 1;5 0 1.5;5 0 2;5 0 2.5;5 0 3;5 0 3.5;5 0 4;...   %��ͷ��       %(8��)
       5.5 0.5 0.5;5.5 -0.5 0.5;4.5 0.5 0.5;4.5 -0.5 0.5;... %��ͷ�˶�      %(4��)5 0 0.5; 5.5 0 0.5;5 0.5 0.5;5 -0.5 0.5; 4.5 0 0.5;  
       0.5 0.5 0.9;0.5 -0.5 0.9;-0.5 0.5 0.9;-0.5 -0.5 0.9;0 0 0.5;... %���ж�   %(5��) 0.5 0 1;-0.5 0 1;0 0.5 1;0 -0.5 1;
       -5 0 0.2;-5 0.2 0.8;-5 0.2 1.4;-5 0 2;... %��β��    %(4��) 
       -5.5 -0.5 0.5;-5.5 0.5 0.5;-4.4 0.5 0.5;-4.5 -0.6 0.5;...��β�˶�  %(4��)-5 0 0.5;-5.5 0 0.5; -5 0.5 0.5;-5 -0.5 0.5;-4.5 0 0.5;
       ];
% x_Pos2 = Pos2(:,1)*5;
% y_Pos2 = Pos2(:,2)*5;
% z_Pos2 = Pos2(:,3)*5;
% min_x_Pos = min(x_Pos2);
% min_y_Pos = min(y_Pos2);
% min_z_Pos = min(z_Pos2);
%----------for test---------%
% Pos = [10 -1 0;10 1 0;10.5 -0.75 0;10.5 0.75 0;9.5 0.5 0.5;9.5 -0.5 0.5;9 0 0.5;8.5 0.5 0.5;8.5 -0.5 0.5];   %
%---------------------------%
%-------------------------------------------------------------------------%

%-------------------------------    ��ʾ   -------------------------------%
x_Pos = Pos(:,1)*5;
y_Pos = Pos(:,2)*5;
z_Pos = Pos(:,3)*5;
min_x_Pos = min(x_Pos);
x_Pos = x_Pos + min_x_Pos;
min_y_Pos = min(y_Pos);
y_Pos = y_Pos + min_y_Pos;
min_z_Pos = min(z_Pos);
z_Pos = z_Pos + min_z_Pos;
figure
plot3(x_Pos,y_Pos,z_Pos,'*')
grid on



R = [cos(3*pi/8)*cos(0),cos(3*pi/8)*sin(0),sin(3*pi/8)];  %�״�Ŀ�����ߵ�λʸ��
Num_point = length(x_Pos); %Ŀ�����
x_r = zeros(1,Num_point);
y_r = zeros(1,Num_point);
z_r = zeros(1,Num_point);
for n_point = 1:Num_point
    x_r(n_point) = y_Pos(n_point)*R(3)-z_Pos(n_point)*R(2);
    y_r(n_point) = z_Pos(n_point)*R(1)-x_Pos(n_point)*R(3);
    z_r(n_point) = x_Pos(n_point)*R(2)-y_Pos(n_point)*R(1);
end

x_oumiga = 0.05; %Ŀ����ת��ʼ���ٶ�
y_oumiga = 0.2; %
z_oumiga = 0.05;

x_lamda = 0.05; %0.05%Ŀ����ת���ٶȼ��ٶ�
y_lamda = 0.1; %0.1
z_lamda = 0.05; %0.05

x_gamma = 0.05; %0.05%Ŀ����ת���ٶȼӼ��ٶ�
y_gamma = 0.4; %0.4
z_gamma = 0.05; %0.05

f = zeros(1,Num_point);
alpha = zeros(1,Num_point);
beita = zeros(1,Num_point);
for n_point = 1:Num_point
    f(n_point) = x_r(n_point)*x_oumiga+y_r(n_point)*y_oumiga+z_r(n_point)*z_oumiga;
    alpha(n_point) = x_r(n_point)*x_lamda+y_r(n_point)*y_lamda+z_r(n_point)*z_lamda;
    beita(n_point) = x_r(n_point)*x_gamma+y_r(n_point)*y_gamma+z_r(n_point)*z_gamma;
end

%---------------------------�״������ʱ����ز�---------------------------%
B = 80*1e6;  %����
c = 3*1e8; 
PRF = 1400; %�����ظ�Ƶ��
fc = 5.2*1e9; %��Ƶ
delta_r = c/(2*B);
r = -50*delta_r:delta_r:50*delta_r;
tm = 0:(1/PRF):0.501;
Num_r = length(r);
Num_tm = length(tm);
ones_r = ones(1,Num_r);
ones_tm = ones(1,Num_tm);

s_r_tm = 0; %Խ����У����
for n_point = 1:Num_point
    Delta_R0(n_point) = x_Pos(n_point)*R(1)+y_Pos(n_point)*R(2)+z_Pos(n_point)*R(3);%��ʼʱ�̵ľ�����
    Delta_R = f(n_point).*0+(1/2)*alpha(n_point).*0.*0+(1/6)*beita(n_point).*0.*0.*tm + Delta_R0(n_point);
    sita_Delta_R = 4*pi*(fc/c)*((f(n_point)+0*c/(2*fc)).*tm+(1/2)*alpha(n_point).*0.*0+(1/6)*beita(n_point).*0.*0.*0 + Delta_R0(n_point)); %+4.5����Ŀ����ͼ����λ��
    s_r_tm = s_r_tm + sinc((2*B/c)*(r.'*ones_tm-ones_r.'*Delta_R)).*exp(j*ones_r.'*sita_Delta_R);
end
% figure
% imagesc(flipud(abs(fft(s_r_tm,Num_tm,2))))  %ʵ��ʹ�õĳ�������PRF = 1400; 190--370

tm2 = 0:(1/PRF):0.501;
Num_tm2 = length(tm2);
ones_tm2 = ones(1,Num_tm2);
s_r_tm2 = 0;  %Խ����У����
for n_point = 1:Num_point
    Delta_R0(n_point) = x_Pos(n_point)*R(1)+y_Pos(n_point)*R(2)+z_Pos(n_point)*R(3);%��ʼʱ�̵ľ�����
    Delta_R = f(n_point).*0+(1/2)*alpha(n_point).*0.*tm2+(1/6)*beita(n_point).*0.*tm2.*tm2 + Delta_R0(n_point);
    sita_Delta_R = 4*pi*(fc/c)*((f(n_point)).*tm2+(1/2)*alpha(n_point).*tm2.*tm2+(1/6)*beita(n_point).*tm2.*tm2.*tm2 + Delta_R0(n_point)); %+4.5����Ŀ����ͼ����λ��
    if n_point>53 & n_point<62
        s_r_tm2 = s_r_tm2 + 1.3*sinc((2*B/c)*(r.'*ones_tm2-ones_r.'*Delta_R)).*exp(j*ones_r.'*sita_Delta_R);
    else
        s_r_tm2 = s_r_tm2 + sinc((2*B/c)*(r.'*ones_tm2-ones_r.'*Delta_R)).*exp(j*ones_r.'*sita_Delta_R);        
    end
    
    if n_point == 48
        s_r_tm2 = s_r_tm2 + 1*sinc((2*B/c)*(r.'*ones_tm2-ones_r.'*Delta_R)).*exp(j*ones_r.'*sita_Delta_R);
    else
        s_r_tm2 = s_r_tm2 + sinc((2*B/c)*(r.'*ones_tm2-ones_r.'*Delta_R)).*exp(j*ones_r.'*sita_Delta_R);
    end
end
%shipx2=s_r_tm2;
% 参数设置
params = struct();

% VMD参数
params.vmd.K = 4;  % 增加默认模态数为4
params.vmd.alpha = 3000; % 增大惩罚因子提高模态分离度
params.vmd.tau = 0.2; % 增大更新步长加速收敛
params.vmd.tol = 1e-7;
params.vmd.max_iter = 500; % 增加最大迭代次数以确保收敛
params.vmd.adaptive = true;
params.vmd.use_smoothing = true; % 启用信号平滑预处理
params.vmd.reduce_overlap = true; % 启用模态重叠消除

% DCFT参数
params.dcft.alpha_min = -200;
params.dcft.alpha_max = 200;
params.dcft.alpha_step = 10;
params.dcft.beta_min = -1000;
params.dcft.beta_max = 1000;
params.dcft.beta_step = 50;
params.dcft.gamma_min = -800;
params.dcft.gamma_max = 800;
params.dcft.use_fine_search = true; % 使用细粒度搜索
params.dcft.use_fourth_order = true; % 启用四阶相位估计
params.dcft.use_gradient_descent = false; % 启用梯度下降优化

% ADMM参数
params.admm.rho = 1.0;
params.admm.max_iter = 200; % 增加ADMM迭代次数
params.admm.tol = 1e-6;
params.admm.dynamic_rho = true; % 动态调整rho参数
params.admm.use_wavelet = false; % 使用小波变换
params.admm.use_freq_weights = true; % 使用频率权重
params.admm.use_continuity = true; % 使用频率连续性约束
params.admm.use_weighted_l1 = true; % 使用加权L1正则化

% 融合参数
params.fusion.alpha1 = 0.4; % VMD重要性权重
params.fusion.alpha2 = 0.3; % DCFT重要性权重 
params.fusion.alpha3 = 0.3; % 最小熵重要性权重
params.fusion.alpha4 = 0.8; % 数据保真度权重
params.fusion.alpha5 = 0.2; % 稀疏约束权重
params.fusion.use_hybrid = true; % 使用混合融合策略

% 性能优化参数
params.performance.parallel = true; % 启用并行处理
params.performance.show_progress = true; % 显示处理进度
params.performance.visualize_steps = true; % 可视化中间步骤
params.performance.use_gpu = false; % 不使用GPU（避免兼容性问题）
params.performance.batch_size = 10; % 批处理大小

% 新增: 分区处理策略 - 避免并行处理中的内存问题
params.performance.use_partition = true; % 启用分区处理
params.performance.partition_size = 20; % 每个分区的距离单元数量

% 显示初始图像
tic;
traditional_image = fftshift(fft(shipx2, [], 2), 2);
traditional_time = toc;
contrast_traditional = contrast(traditional_image);
entropy_traditional =EntropyImage (abs(traditional_image));
fprintf('传统FFT方法完成，耗时: %.2f 秒\n', traditional_time);

% 2. 优化的VMD-ADMM-DCFT融合方法
fprintf('执行优化的VMD-ADMM-DCFT融合方法...\n');
tic;

% 修复: 使用分区处理策略避免并行处理内存问题
[fusion_image, s_compensated, processing_info] = enhanced_vmd_admm_dcft(shipx2, params);

fusion_time = toc;
fprintf('VMD-ADMM-DCFT融合方法完成，耗时: %.2f秒\n', fusion_time);

% 结果展示
figure('Name', 'ISAR成像结果比较', 'Position', [100, 100, 800, 400]);

% 传统FFT方法
figure;
traditional_db = 20*log10(abs(traditional_image)./max(abs(traditional_image(:))));
imagesc(traditional_db);
caxis([-30, 0]);
colormap('jet'); colorbar;
title(sprintf('传统FFT方法\n对比度: %.4f, 熵: %.4f', contrast_traditional, entropy_traditional));
xlabel('方位单元'); ylabel('距离单元');
axis xy;

% VMD-ADMM-DCFT融合方法
figure;
fusion_db = 20*log10(abs(fusion_image)./max(abs(fusion_image(:))));
imagesc(fusion_db);
caxis([-30, 0]);
colormap('jet'); colorbar;
title(sprintf('优化的VMD-ADMM-DCFT融合方法\n对比度: %.4f, 熵: %.4f', contrast(fusion_image), EntropyImage(fusion_image)));
xlabel('方位单元'); ylabel('距离单元');
axis xy;

% 打印性能对比
fprintf('======= 性能对比 =======\n');
fprintf('方法\t\t对比度\t\t熵\t\t处理时间(秒)\n');
fprintf('传统FFT\t\t%.4f\t\t%.4f\t\t%.2f\n', contrast_traditional, entropy_traditional, traditional_time);
fprintf('VMD-ADMM-DCFT\t%.4f\t\t%.4f\t\t%.2f\n', contrast_fusion, entropy_fusion, fusion_time);
fprintf('提升比例\t%.2f%%\t\t%.2f%%\n', 100*(contrast_fusion-contrast_traditional)/contrast_traditional, 100*(entropy_traditional-entropy_fusion)/entropy_traditional);

% 显示中间处理步骤
if params.performance.visualize_steps
    visualize_processing_steps(processing_info);
end

%% ==================== 增强型VMD-ADMM-DCFT融合算法 ====================
function [ISAR_image, s_compensated, processing_info] = enhanced_vmd_admm_dcft(radar_data, params)
% 增强型VMD-ADMM-DCFT融合ISAR成像算法
%
% 输入:
%   radar_data - 距离压缩后的回波数据 [距离单元 x 方位单元]
%   params - 参数结构体
%
% 输出:
%   ISAR_image - ISAR成像结果
%   s_compensated - 相位补偿后的信号
%   processing_info - 处理过程信息

% 1. 参数初始化
[num_range_bins, num_azimuth] = size(radar_data);
fprintf('处理数据尺寸: %d x %d\n', num_range_bins, num_azimuth);

% 归一化处理数据
radar_data = radar_data / max(abs(radar_data(:)));

% 创建时间和频率轴
tm = (0:num_azimuth-1) / num_azimuth;
omega = (0:num_azimuth-1) / num_azimuth;

% 初始化补偿后的信号
s_compensated = zeros(size(radar_data), 'like', radar_data);

% 初始化处理信息
processing_info = struct();
processing_info.original_data = radar_data;
processing_info.vmd_modes = cell(num_range_bins, 1);
processing_info.phase_models = cell(num_range_bins, 1);
processing_info.reconstructed_signals = cell(num_range_bins, 1);

% 2. 主循环: 处理每个距离单元
fprintf('开始增强型VMD-ADMM-DCFT处理...\n');
tic;

% 设置并行处理
if params.performance.parallel && isempty(gcp('nocreate'))
    parpool('local');
end

% 存储相位参数，用于帧间连续性约束
phase_params_global = zeros(params.vmd.K, 4);
phi_k_global = zeros(params.vmd.K, num_azimuth);

% 初始化临时结果存储
vmd_modes_cell = cell(num_range_bins, 1);
phase_models_cell = cell(num_range_bins, 1);
recon_signals_cell = cell(num_range_bins, 1);
comp_signals = zeros(size(radar_data), 'like', radar_data);

% 使用分区处理策略
if params.performance.use_partition
    % 计算分区数量
    partition_size = params.performance.partition_size;
    num_partitions = ceil(num_range_bins / partition_size);
    
    fprintf('使用分区处理策略，共 %d 个分区，每个分区 %d 个距离单元\n', num_partitions, partition_size);
    
    for p = 1:num_partitions
        % 计算当前分区的距离单元范围
        start_idx = (p-1) * partition_size + 1;
        end_idx = min(p * partition_size, num_range_bins);
        current_range_indices = start_idx:end_idx;
        
        fprintf('处理分区 %d/%d (距离单元 %d-%d)...\n', p, num_partitions, start_idx, end_idx);
        
        % 为当前分区创建临时存储
        part_vmd_modes = cell(length(current_range_indices), 1);
        part_phase_models = cell(length(current_range_indices), 1);
        part_recon_signals = cell(length(current_range_indices), 1);
        part_comp_signals = zeros(length(current_range_indices), num_azimuth, 'like', radar_data);
        
        % 处理当前分区
        if params.performance.parallel
            % 并行处理分区内的距离单元
            parfor (local_idx = 1:length(current_range_indices), 4)
                r_idx = current_range_indices(local_idx);
                
                % 处理单个距离单元
                [vmd_mode, phase_model, recon_signal, comp_signal] = process_range_cell_parallel(r_idx, radar_data, tm, params, phi_k_global, phase_params_global);
                
                % 存储结果到临时变量
                part_vmd_modes{local_idx} = vmd_mode;
                part_phase_models{local_idx} = phase_model;
                part_recon_signals{local_idx} = recon_signal;
                part_comp_signals(local_idx, :) = comp_signal;
            end
        else
            % 串行处理分区内的距离单元
            for local_idx = 1:length(current_range_indices)
                r_idx = current_range_indices(local_idx);
                
                % 串行处理可以更新全局状态
                [vmd_mode, phase_model, recon_signal, comp_signal, phase_params_updated, phi_k_updated] = process_range_cell_serial(r_idx, radar_data, tm, params, phi_k_global, phase_params_global);
                
                % 存储结果
                part_vmd_modes{local_idx} = vmd_mode;
                part_phase_models{local_idx} = phase_model;
                part_recon_signals{local_idx} = recon_signal;
                part_comp_signals(local_idx, :) = comp_signal;
                
                % 更新全局状态
                phase_params_global = phase_params_updated;
                phi_k_global = phi_k_updated;
            end
        end
        
        % 将分区结果合并到全局结果中
        for local_idx = 1:length(current_range_indices)
            r_idx = current_range_indices(local_idx);
            vmd_modes_cell{r_idx} = part_vmd_modes{local_idx};
            phase_models_cell{r_idx} = part_phase_models{local_idx};
            recon_signals_cell{r_idx} = part_recon_signals{local_idx};
            comp_signals(r_idx, :) = part_comp_signals(local_idx, :);
        end
    end
else
    % 不使用分区，直接处理所有距离单元
    if params.performance.parallel
        % 并行处理
        parfor r_idx = 1:num_range_bins
            % 处理单个距离单元（无状态更新）
            [vmd_mode, phase_model, recon_signal, comp_signal] = process_range_cell_parallel(r_idx, radar_data, tm, params, phi_k_global, phase_params_global);
            
            % 存储结果
            vmd_modes_cell{r_idx} = vmd_mode;
            phase_models_cell{r_idx} = phase_model;
            recon_signals_cell{r_idx} = recon_signal;
            comp_signals(r_idx, :) = comp_signal;
        end
    else
        % 串行处理
        for r_idx = 1:num_range_bins
            % 处理单个距离单元（更新状态）
            [vmd_mode, phase_model, recon_signal, comp_signal, phase_params_updated, phi_k_updated] = process_range_cell_serial(r_idx, radar_data, tm, params, phi_k_global, phase_params_global);
            
            % 存储结果
            vmd_modes_cell{r_idx} = vmd_mode;
            phase_models_cell{r_idx} = phase_model;
            recon_signals_cell{r_idx} = recon_signal;
            comp_signals(r_idx, :) = comp_signal;
            
            % 更新全局状态
            phase_params_global = phase_params_updated;
            phi_k_global = phi_k_updated;
        end
    end
end

% 更新处理信息
processing_info.vmd_modes = vmd_modes_cell;
processing_info.phase_models = phase_models_cell;
processing_info.reconstructed_signals = recon_signals_cell;
s_compensated = comp_signals;

processing_time = toc;
fprintf('处理完成，耗时: %.2f秒\n', processing_time);

% 3. 后处理 - 全局图像增强
% 应用全局对比度增强和边缘保持滤波
for r_idx = 1:num_range_bins
    % 应用二维中值滤波去除孤立噪声点
    window_size = 3;
    if r_idx > window_size && r_idx < num_range_bins-window_size
        % 获取当前距离单元及其邻域
        local_region = s_compensated(r_idx-1:r_idx+1, :);
        
        % 检测异常值
        signal_mean = mean(abs(local_region), 1);
        signal_std = std(abs(local_region), 0, 1);
        
        % 寻找异常点
        current_signal = s_compensated(r_idx, :);
        outlier_indices = abs(abs(current_signal) - signal_mean) > 3*signal_std;
        
        % 替换异常点
        if any(outlier_indices)
            current_signal(outlier_indices) = mean(local_region(:, outlier_indices), 1);
            s_compensated(r_idx, :) = current_signal;
        end
    end
end

% 4. 方位向FFT成像
ISAR_image = fftshift(fft(s_compensated, [], 2), 2);

% 存储补偿后的信号和最终图像
processing_info.compensated_data = s_compensated;
processing_info.final_image = ISAR_image;

% 5. 计算图像质量指标
contrast_value = calculate_contrast(ISAR_image);
entropy_value = calculate_entropy(abs(ISAR_image));

fprintf('成像结果质量指标:\n');
fprintf('- 对比度: %.4f\n', contrast_value);
fprintf('- 熵: %.4f\n', entropy_value);
end

%% ==================== 并行处理单个距离单元 ====================
function [vmd_modes, phase_model, recon_signal, comp_signal] = process_range_cell_parallel(r_idx, radar_data, tm, params, phi_k_prev, phase_params_prev)
% 处理单个距离单元（并行版本 - 无状态更新）
    % 显示进度（仅当参数设置为显示时）
    if params.performance.show_progress && (mod(r_idx, 10) == 0 || r_idx == 1 || r_idx == size(radar_data, 1))
        fprintf('处理距离单元: %d/%d\n', r_idx, size(radar_data, 1));
    end
    
    % 获取当前距离单元信号
    signal = radar_data(r_idx, :);
    
    % 跳过能量过低的距离单元
    if sum(abs(signal).^2) < 1e-10
        vmd_modes = [];
        phase_model = [];
        recon_signal = signal;
        comp_signal = signal;
        return;
    end
    
    % 自适应确定VMD模态数
    local_params = params;
    if params.vmd.adaptive
        K_optimal = estimate_optimal_modes_parallel(signal);
        local_params.vmd.K = K_optimal;
    else
        K_optimal = params.vmd.K;
    end
    
    % 初始化变量
    u_k = zeros(K_optimal, length(signal), 'like', 1j); % 模态
    omega_k = zeros(K_optimal, 1); % 中心频率
    
    % 初始化模态中心频率
    for k = 1:K_optimal
        omega_k(k) = (k-0.5) * (0.5/K_optimal);
    end
    
    % VMD分解
    [u_k, omega_k] = optimized_vmd_decomposition_parallel(signal, omega_k, local_params);
    
    % DCFT相位估计 (不使用之前状态，避免并行问题)
    phi_k = zeros(K_optimal, length(signal));
    phase_params_out = zeros(K_optimal, 4);
    
    % 循环处理每个模态
    for k = 1:K_optimal
        mode_signal = u_k(k, :);
        [phi_k(k,:), phase_params_out(k,:)] = optimized_dcft_phase_estimation_single_mode(mode_signal, [], [], local_params);
    end
    
    % 初始化ADMM变量
    X = fft(signal); % 初始频谱
    Z = X; % 辅助变量
    Lambda = zeros(size(X), 'like', 1j); % 拉格朗日乘子
    
    % ADMM重建
    [X, ~, ~] = optimized_admm_reconstruction_parallel(u_k, phi_k, signal, X, Z, Lambda, local_params);
    
    % 重构信号
    recon_signal = ifft(X);
    
    % 选择主导模态
    mode_energies = sum(abs(u_k).^2, 2);
    [~, dominant_idx] = max(mode_energies);
    
    % 构建补偿函数
    comp_phase = phi_k(dominant_idx, :);
    
    % 应用相位补偿
    comp_signal = signal .* exp(-1j * comp_phase);
    
    % 返回结果
    vmd_modes = u_k;
    phase_model = phi_k;
end

%% ==================== 串行处理单个距离单元 ====================
function [vmd_modes, phase_model, recon_signal, comp_signal, phase_params_updated, phi_k_updated] = process_range_cell_serial(r_idx, radar_data, tm, params, phi_k_prev, phase_params_prev)
% 处理单个距离单元（串行版本 - 返回更新的状态）
    % 显示进度（仅当参数设置为显示时）
    if params.performance.show_progress && (mod(r_idx, 10) == 0 || r_idx == 1 || r_idx == size(radar_data, 1))
        fprintf('处理距离单元: %d/%d\n', r_idx, size(radar_data, 1));
    end
    
    % 获取当前距离单元信号
    signal = radar_data(r_idx, :);
    
    % 跳过能量过低的距离单元
    if sum(abs(signal).^2) < 1e-10
        vmd_modes = [];
        phase_model = [];
        recon_signal = signal;
        comp_signal = signal;
        % 保持前一帧的状态
        phase_params_updated = phase_params_prev;
        phi_k_updated = phi_k_prev;
        return;
    end
    
    % 自适应确定VMD模态数
    local_params = params;
    if params.vmd.adaptive
        K_optimal = estimate_optimal_modes_parallel(signal);
        local_params.vmd.K = K_optimal;
    else
        K_optimal = params.vmd.K;
    end
    
    % 初始化变量
    u_k = zeros(K_optimal, length(signal), 'like', 1j); % 模态
    omega_k = zeros(K_optimal, 1); % 中心频率
    
    % 初始化模态中心频率
    for k = 1:K_optimal
        omega_k(k) = (k-0.5) * (0.5/K_optimal);
    end
    
    % 3.1 VMD-引导的信号分解
    [u_k, omega_k] = optimized_vmd_decomposition(signal, omega_k, local_params);
    
    % 3.2 DCFT-增强的相位估计
    [phi_k, phase_params] = optimized_dcft_phase_estimation(u_k, phi_k_prev, phase_params_prev, local_params);
    
    % 3.3 ADMM-约束的稀疏重建
    X = fft(signal); % 初始频谱
    Z = X; % 辅助变量
    Lambda = zeros(size(X), 'like', 1j); % 拉格朗日乘子
    
    [X, Z, Lambda] = optimized_admm_reconstruction(u_k, phi_k, signal, X, Z, Lambda, local_params);
    
    % 重构信号
    recon_signal = ifft(X);
    
    % 3.4 最终相位补偿
    % 加权组合所有模态的相位修正
    % 选择主导模态
    mode_energies = sum(abs(u_k).^2, 2);
    [~, dominant_indices] = sort(mode_energies, 'descend');
    
    % 构建补偿函数 - 使用能量加权的混合模态
    total_energy = sum(mode_energies);
    comp_phase = zeros(1, length(signal));
    
    for k_idx = 1:min(2, K_optimal)  % 使用前两个主导模态
        k = dominant_indices(k_idx);
        weight = mode_energies(k) / total_energy;
        comp_phase = comp_phase + weight * phi_k(k, :);
    end
    
    % 应用相位补偿
    comp_signal = signal .* exp(-1j * comp_phase);
    
    % 返回结果
    vmd_modes = u_k;
    phase_model = phi_k;
    phase_params_updated = phase_params;
    phi_k_updated = phi_k;
end

%% ==================== 辅助函数 - 并行版本 ====================
function K_optimal = estimate_optimal_modes_parallel(signal)
% 估计最优VMD模态数 - 并行版本

% 计算信号功率谱
signal_fft = fft(signal);
psd = abs(signal_fft).^2;
    
% 找出主要峰值
[~, locs] = findpeaks(psd, 'MinPeakHeight', 0.1*max(psd), 'MinPeakDistance', 5);
    
% 基于峰值数量确定最优模态数
if isempty(locs)
    K_optimal = 2; % 默认值
else
    K_optimal = min(max(length(locs), 2), 6); % 至少2个，最多6个
end
end

function [u_k, omega_k] = optimized_vmd_decomposition_parallel(signal, omega_k_init, params)
% 优化的VMD分解算法 - 并行版本

N = length(signal);
signal_fft = fft(signal);
alpha = params.vmd.alpha;
tau = params.vmd.tau;
K = length(omega_k_init); 
tol = params.vmd.tol;
max_iter = params.vmd.max_iter;

% 初始化
u_k = zeros(K, N, 'like', 1j);
omega_k = omega_k_init;
lambda = zeros(1, N, 'like', 1j);

% 频率轴
omega_axis = (0:N-1)/N;

% 自适应惩罚因子
base_alpha = alpha;
alphas = base_alpha * ones(K, 1);

% VMD迭代
for iter = 1:max_iter
    % 保存上一次迭代结果
    u_k_prev = u_k;
    omega_k_prev = omega_k;
    
    % 更新每个模态
    sum_uk = zeros(1, N, 'like', 1j);
    for k = 1:K
        % 计算除当前模态外的所有模态之和
        sum_u_i = sum_uk - u_k(k, :);
        
        % 在频域更新u_k
        num = signal_fft - fft(sum_u_i) + fft(lambda)/2;
        den = 1 + 2*alphas(k)*(omega_axis - omega_k(k)).^2;
        
        u_k_fft = num ./ den;
        u_k(k, :) = ifft(u_k_fft);
        
        % 更新sum_uk
        sum_uk = sum_uk - u_k_prev(k, :) + u_k(k, :);
        
        % 更新中心频率
        power_spectrum = abs(u_k_fft).^2;
        
        if sum(power_spectrum) > 0
            omega_k(k) = sum(omega_axis .* power_spectrum) / sum(power_spectrum);
        end
    end
    
    % 更新拉格朗日乘子
    lambda = lambda + tau * (signal - sum_uk);
    
    % 检查收敛性
    u_diff = norm(u_k - u_k_prev, 'fro')^2 / norm(u_k_prev, 'fro')^2;
    omega_diff = norm(omega_k - omega_k_prev)^2 / norm(omega_k_prev)^2;
    
    if u_diff < tol && omega_diff < tol
        break;
    end
end

% 排序
[omega_k, sort_idx] = sort(omega_k);
u_k = u_k(sort_idx, :);

% 能量归一化
for k = 1:K
    mode_energy = sum(abs(u_k(k,:)).^2);
    if mode_energy > 0
        u_k(k,:) = u_k(k,:) / sqrt(mode_energy);
    end
end
end

function [phi_k, phase_params] = optimized_dcft_phase_estimation_single_mode(mode_signal, phi_prev, phase_params_prev, params)
% 单个模态的DCFT相位估计 - 并行版本

N = length(mode_signal);
tm = (0:N-1)/N;

% 高阶参数
use_fourth_order = params.dcft.use_fourth_order || false;

% 初始化参数搜索范围
alpha_range = params.dcft.alpha_min:params.dcft.alpha_step*2:params.dcft.alpha_max;
beta_range = params.dcft.beta_min:params.dcft.beta_step*2:params.dcft.beta_max;

best_response = -inf;
best_fd = 0;
best_alpha = 0;
best_beta = 0;
best_gamma = 0;

% 计算瞬时频率
analytic_signal = hilbert(mode_signal);
inst_phase = unwrap(angle(analytic_signal));
inst_freq = diff([inst_phase(1), inst_phase]) / (2*pi);

% 分析瞬时频率趋势
freq_trend = polyfit(tm, inst_freq, 3);

% 根据趋势调整搜索范围
fd_center = freq_trend(end);
alpha_center = freq_trend(end-1) * 2;
beta_center = freq_trend(end-2) * 6;

% 构建精细搜索范围
fd_range_fine = fd_center + (-0.1:0.01:0.1);
alpha_range_fine = alpha_center + (-4:1:4) * params.dcft.alpha_step;
beta_range_fine = beta_center + (-4:1:4) * params.dcft.beta_step;

% 组合搜索范围
fd_range = unique([fd_range_fine]);
alpha_range = unique([alpha_range_fine]);
beta_range = unique([beta_range_fine]);

% 网格搜索
for fd = fd_range
    for alpha = alpha_range
        for beta = beta_range
            % 构建去啁啾项
            dechirp_phase = -1j * 2*pi * (fd*tm + (1/2)*alpha*tm.^2 + (1/6)*beta*tm.^3);
            dechirped = mode_signal .* exp(dechirp_phase);
            
            % FFT处理
            spectrum = fft(dechirped);
            response = max(abs(spectrum));
            
            if response > best_response
                best_response = response;
                best_fd = fd;
                best_alpha = alpha;
                best_beta = beta;
            end
        end
    end
end

% 更新相位参数
phase_params = [best_fd, best_alpha, best_beta, 0];

% 构建完整相位模型
phi_k = 2*pi * (best_fd*tm + (1/2)*best_alpha*tm.^2 + (1/6)*best_beta*tm.^3);
end

function [X, Z, Lambda] = optimized_admm_reconstruction_parallel(u_k, phi_k, signal, X_init, Z_init, Lambda_init, params)
% 优化的ADMM稀疏重建算法 - 并行版本

[K, N] = size(u_k);
X = X_init;
Z = Z_init;
Lambda = Lambda_init;

% 从模态和相位构建补偿后的信号
Y = zeros(1, N, 'like', 1j);
for k = 1:K
    comp_signal = u_k(k, :) .* exp(-1j * phi_k(k, :));
    Y = Y + comp_signal;
end
Y_fft = fft(Y);

% ADMM参数
rho = params.admm.rho;
max_iter = params.admm.max_iter;
tol = params.admm.tol;

% 数据保真度和稀疏性权重
alpha4 = params.fusion.alpha4;
alpha5 = params.fusion.alpha5;

% 基于信号能量自适应调整参数
signal_energy = sum(abs(signal).^2);
if params.admm.dynamic_rho
    rho = rho * (1 + 0.5*log10(signal_energy + 1));
    alpha5 = alpha5 * (1 + exp(-signal_energy));
end

% 计算信号频域稀疏度
signal_fft = fft(signal);
sorted_mag = sort(abs(signal_fft), 'descend');
cumsum_energy = cumsum(sorted_mag.^2) / sum(sorted_mag.^2);
sparsity_idx = find(cumsum_energy > 0.95, 1);
sparsity_ratio = sparsity_idx / N;

% 根据信号稀疏度调整alpha5
if sparsity_ratio < 0.1
    alpha5 = alpha5 * 1.5;
elseif sparsity_ratio > 0.3
    alpha5 = alpha5 * 0.8;
end

% 计算频率权重
psd = abs(signal_fft).^2;
freq_weights = psd / max(psd);
freq_weights = freq_weights + 0.1;
freq_weights = freq_weights / max(freq_weights);

% ADMM迭代
for iter = 1:max_iter
    % 保存上一次迭代结果
    X_prev = X;
    Z_prev = Z;
    
    % 更新X (数据保真度)
    X = (alpha4 * Y_fft + rho * Z - Lambda) ./ (alpha4 + rho);
    
    % 频率连续性约束
    X_smooth = (X + circshift(X, 1) + circshift(X, -1)) / 3;
    X = 0.7 * X + 0.3 * X_smooth;
    
    % 更新Z (稀疏性)
    Z_update = X + Lambda/rho;
    Z = sign(Z_update) .* max(abs(Z_update) - alpha5/rho * (1 ./ (freq_weights + 0.01)), 0);
    
    % 更新Lambda (拉格朗日乘子)
    Lambda = Lambda + rho * (X - Z);
    
    % 收敛检查
    if norm(X - X_prev)/norm(X_prev) < tol
        break;
    end
end

% 返回
X = X;
end

%% ==================== 辅助函数 ====================
function y = soft_threshold(x, lambda)
% 软阈值函数
y = sign(x) .* max(abs(x) - lambda, 0);
end

function y = soft_threshold_weighted(x, lambda, weights)
% 加权软阈值函数
y = sign(x) .* max(abs(x) - lambda * (1 ./ weights), 0);
end

function contrast = calculate_contrast(image)
% 计算图像对比度
mag = abs(image);
contrast = std(mag(:)) / mean(mag(:));
end

function entropy = calculate_entropy(image)
% 计算图像熵
normalized = image / sum(image(:));
normalized(normalized < eps) = eps;
entropy = -sum(normalized(:) .* log2(normalized(:)));
end

%% ==================== 可视化处理步骤 ====================
function visualize_processing_steps(processing_info)
% 可视化处理过程的中间步骤
fprintf('可视化处理步骤...\n');

% 创建图形窗口
figure('Name', '优化VMD-ADMM-DCFT融合处理步骤', 'Position', [100, 100, 1200, 800]);

% 1. 原始数据
subplot(3, 3, 1);
imagesc(abs(processing_info.original_data));
title('原始雷达数据');
xlabel('方位单元');
ylabel('距离单元');
colormap('jet');
colorbar;

% 2. 检查VMD分解 - 取中间距离单元
middle_range = floor(size(processing_info.original_data, 1) / 2);
if ~isempty(processing_info.vmd_modes{middle_range})
    vmd_modes = processing_info.vmd_modes{middle_range};
    
    subplot(3, 3, 2);
    hold on;
    for k = 1:size(vmd_modes, 1)
        plot(abs(vmd_modes(k,:)));
    end
    hold off;
    title('VMD模态分解 (中间距离单元)');
    xlabel('方位单元');
    ylabel('幅度');
    
    % 3. 相位模型
    phase_model = processing_info.phase_models{middle_range};
    
    subplot(3, 3, 3);
    hold on;
    for k = 1:size(phase_model, 1)
        plot(unwrap(phase_model(k,:)));
    end
    hold off;
    title('相位模型 (中间距离单元)');
    xlabel('方位单元');
    ylabel('相位 (rad)');
end

% 4. 原始信号与重构信号对比
subplot(3, 3, 4);
if ~isempty(processing_info.reconstructed_signals{middle_range})
    hold on;
    plot(abs(processing_info.original_data(middle_range,:)), 'b-', 'LineWidth', 1);
    plot(abs(processing_info.reconstructed_signals{middle_range}), 'r-', 'LineWidth', 1);
    hold off;
    title('信号重构对比 (中间距离单元)');
    xlabel('方位单元');
    ylabel('幅度');
    legend('原始信号', '重构信号');
end

% 5. 相位补偿前后对比
subplot(3, 3, 5);
original_phase = angle(processing_info.original_data(middle_range,:));
compensated_phase = angle(processing_info.compensated_data(middle_range,:));
hold on;
plot(unwrap(original_phase), 'b-', 'LineWidth', 1);
plot(unwrap(compensated_phase), 'r-', 'LineWidth', 1);
hold off;
title('相位补偿前后对比');
xlabel('方位单元');
ylabel('相位 (rad)');
legend('原始相位', '补偿后相位');

% 6. 相干积累改善
subplot(3, 3, 6);
original_fft = fftshift(fft(processing_info.original_data(middle_range,:)), 2);
compensated_fft = fftshift(fft(processing_info.compensated_data(middle_range,:)), 2);
hold on;
plot(20*log10(abs(original_fft)/max(abs(original_fft))), 'b-', 'LineWidth', 1);
plot(20*log10(abs(compensated_fft)/max(abs(compensated_fft))), 'r-', 'LineWidth', 1);
hold off;
title('相干积累改善 (中间距离单元)');
xlabel('方位单元');
ylabel('幅度 (dB)');
legend('原始FFT', '补偿后FFT');
ylim([-40, 0]);

% 7. 未补偿原始图像
subplot(3, 3, 7);
original_image = fftshift(fft(processing_info.original_data, [], 2), 2);
original_db = 20*log10(abs(original_image)./max(abs(original_image(:))));
imagesc(original_db);
caxis([-30, 0]);
colormap('jet');
colorbar;
title('未补偿ISAR图像 (dB)');
xlabel('方位单元');
ylabel('距离单元');

% 8. 补偿后图像
subplot(3, 3, 8);
compensated_db = 20*log10(abs(processing_info.final_image)./max(abs(processing_info.final_image(:))));
imagesc(compensated_db);
caxis([-30, 0]);
colormap('jet');
colorbar;
title('优化VMD-ADMM-DCFT融合ISAR图像 (dB)');
xlabel('方位单元');
ylabel('距离单元');

% 9. 图像质量分析
subplot(3, 3, 9);
% 计算每个距离单元的对比度改善
contrast_original = zeros(size(processing_info.original_data, 1), 1);
contrast_improved = zeros(size(processing_info.original_data, 1), 1);

for r = 1:size(processing_info.original_data, 1)
    if sum(abs(original_image(r,:))) > 0
        contrast_original(r) = contrast(abs(original_image(r,:)));
    end
    if sum(abs(processing_info.final_image(r,:))) > 0
        contrast_improved(r) = contrast(abs(processing_info.final_image(r,:)));
    end
end

% 绘制对比度改善
plot(contrast_improved ./ contrast_original, 'LineWidth', 2);
title('对比度改善比例 (各距离单元)');
xlabel('距离单元');
ylabel('对比度改善比例');
grid on;

% 显示总体改善
overall_contrast_original = contrast(abs(original_image));
overall_contrast_improved = contrast(abs(processing_info.final_image));
overall_entropy_original = EntropyImage(abs(original_image) + eps);
overall_entropy_improved = EntropyImage(abs(processing_info.final_image) + eps);

annotation('textbox', [0.7, 0.05, 0.25, 0.05], ...
    'String', sprintf('总体改善: 对比度 +%.2f%%, 熵 -%.2f%%', ...
    100*(overall_contrast_improved-overall_contrast_original)/overall_contrast_original, ...
    100*(overall_entropy_original-overall_entropy_improved)/overall_entropy_original), ...
    'EdgeColor', 'none', 'FontWeight', 'bold');
end