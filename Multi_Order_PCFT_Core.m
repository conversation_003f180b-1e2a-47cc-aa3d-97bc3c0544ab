function [X_pcft, computation_time] = Multi_Order_PCFT_Core(signal, phase_params, tm_norm, order)
% Multi_Order_PCFT_Core - 多阶多项式Chirp傅里叶变换核心实现
%
% 核心创新：
% 1. 支持0-4阶多项式相位的快速变换
% 2. 逐级Chirp-FFT-Chirp结构，复杂度P*N*log(N)
% 3. 当P=3时退化为MDCFT，与现有代码兼容
% 4. GPU加速支持
%
% 输入:
%   signal       - 输入信号 [1 × N]
%   phase_params - 相位参数 [f, α, β, γ, δ] (最多5个参数)
%   tm_norm      - 归一化时间轴 [1 × N]
%   order        - 多项式阶数 (0-4)
%
% 输出:
%   X_pcft           - PCFT变换结果
%   computation_time - 计算时间

tic;

N = length(signal);
if nargin < 4
    order = min(3, length(phase_params)-1); % 默认最大3阶
end

% 参数检查
if length(phase_params) < order + 1
    error('相位参数数量不足，需要至少 %d 个参数', order + 1);
end

% 提取相位参数
f = phase_params(1);      % 0阶 (频率偏移)
alpha = 0; beta = 0; gamma = 0; delta = 0;
if order >= 1 && length(phase_params) > 1, alpha = phase_params(2); end % 1阶 (线性)
if order >= 2 && length(phase_params) > 2, beta = phase_params(3); end  % 2阶 (二次)
if order >= 3 && length(phase_params) > 3, gamma = phase_params(4); end % 3阶 (三次)
if order >= 4 && length(phase_params) > 4, delta = phase_params(5); end % 4阶 (四次)

%% 多阶PCFT核心算法
% 使用逐级Chirp-FFT-Chirp结构
X_pcft = multi_stage_pcft_transform(signal, f, alpha, beta, gamma, delta, tm_norm, order);

computation_time = toc;
end

%% 多级PCFT变换实现
function X_result = multi_stage_pcft_transform(signal, f, alpha, beta, gamma, delta, tm_norm, order)
% 多级PCFT变换 - 逐级Chirp-FFT-Chirp结构

N = length(signal);
X_current = signal(:); % 确保为列向量

% 阶段0：频率偏移 (如果有)
if abs(f) > eps
    freq_shift = exp(-1j * 2*pi * f * tm_norm(:));
    X_current = X_current .* freq_shift;
end

% 根据阶数执行相应的变换
switch order
    case 0
        % 0阶：仅FFT
        X_result = fft(X_current);
        
    case 1
        % 1阶：线性相位 + FFT
        if abs(alpha) > eps
            linear_phase = exp(-1j * 2*pi * alpha * tm_norm(:));
            X_current = X_current .* linear_phase;
        end
        X_result = fft(X_current);
        
    case 2
        % 2阶：DCFT (二次)
        X_result = dcft_second_order(X_current, beta, tm_norm);
        
    case 3
        % 3阶：MDCFT (三次) - 与现有代码兼容
        X_result = mdcft_third_order(X_current, beta, gamma, tm_norm);
        
    case 4
        % 4阶：扩展PCFT (四次)
        X_result = pcft_fourth_order(X_current, beta, gamma, delta, tm_norm);
        
    otherwise
        error('不支持的多项式阶数: %d', order);
end

X_result = X_result(:).'; % 转换为行向量
end

%% 二阶DCFT实现
function X_dcft = dcft_second_order(signal, beta, tm_norm)
% 二阶DCFT变换
% X(k,l) = (1/√N) * Σ x(n) * W_N^(kn + ln²)

N = length(signal);

if abs(beta) < eps
    % 如果二阶系数为0，退化为普通FFT
    X_dcft = fft(signal);
    return;
end

% 预补偿
pre_chirp = exp(-1j * pi * beta * tm_norm(:).^2);
signal_pre = signal .* pre_chirp;

% FFT
signal_fft = fft(signal_pre);

% 后补偿 (频域)
freq_indices = (0:N-1)';
post_chirp = exp(-1j * pi * beta * (freq_indices/N).^2);
X_dcft = signal_fft .* post_chirp;
end

%% 三阶MDCFT实现
function X_mdcft = mdcft_third_order(signal, beta, gamma, tm_norm)
% 三阶MDCFT变换 - 与现有代码兼容
% X(k,l,m) = (1/√N) * Σ x(n) * W_N^(kn + ln² + mn³)

N = length(signal);

% 第一级：三次项预补偿
if abs(gamma) > eps
    cubic_chirp = exp(-1j * 2*pi * (1/6) * gamma * tm_norm(:).^3);
    signal_cubic = signal .* cubic_chirp;
else
    signal_cubic = signal;
end

% 第二级：二次项处理
if abs(beta) > eps
    X_mdcft = dcft_second_order(signal_cubic, beta, tm_norm);
else
    X_mdcft = fft(signal_cubic);
end
end

%% 四阶PCFT实现
function X_pcft4 = pcft_fourth_order(signal, beta, gamma, delta, tm_norm)
% 四阶PCFT变换
% X(k,l,m,n) = (1/√N) * Σ x(n) * W_N^(kn + ln² + mn³ + nn⁴)

N = length(signal);

% 第一级：四次项预补偿
if abs(delta) > eps
    quartic_chirp = exp(-1j * 2*pi * (1/24) * delta * tm_norm(:).^4);
    signal_quartic = signal .* quartic_chirp;
else
    signal_quartic = signal;
end

% 第二级：三次项处理
X_pcft4 = mdcft_third_order(signal_quartic, beta, gamma, tm_norm);
end

%% 快速多阶PCFT核 (向量化实现)
function X_fast = fast_multi_order_pcft(signal, phase_params, tm_norm, order)
% 快速多阶PCFT实现 - 向量化版本

N = length(signal);
X_current = signal(:);

% 构造完整的相位补偿函数
phase_total = zeros(N, 1);

% 累加各阶相位项
for p = 0:order
    if p+1 <= length(phase_params) && abs(phase_params(p+1)) > eps
        coeff = phase_params(p+1);
        
        % 计算p阶项的系数
        factorial_p = factorial(p);
        phase_contribution = (coeff / factorial_p) * tm_norm(:).^p;
        phase_total = phase_total + phase_contribution;
    end
end

% 应用总相位补偿
if norm(phase_total) > eps
    total_chirp = exp(-1j * 2*pi * phase_total);
    X_current = X_current .* total_chirp;
end

% FFT变换
X_fast = fft(X_current);
X_fast = X_fast(:).'; % 转换为行向量
end

%% GPU加速版本 (如果支持)
function X_gpu = gpu_accelerated_pcft(signal, phase_params, tm_norm, order)
% GPU加速的PCFT实现

try
    % 检查GPU可用性
    if gpuDeviceCount > 0
        % 将数据传输到GPU
        signal_gpu = gpuArray(signal);
        tm_norm_gpu = gpuArray(tm_norm);
        phase_params_gpu = gpuArray(phase_params);
        
        % 在GPU上执行PCFT
        X_gpu_result = fast_multi_order_pcft(signal_gpu, phase_params_gpu, tm_norm_gpu, order);
        
        % 将结果传回CPU
        X_gpu = gather(X_gpu_result);
    else
        % 如果没有GPU，使用CPU版本
        X_gpu = fast_multi_order_pcft(signal, phase_params, tm_norm, order);
    end
catch
    % GPU计算失败时的后备方案
    warning('GPU计算失败，使用CPU版本');
    X_gpu = fast_multi_order_pcft(signal, phase_params, tm_norm, order);
end
end

%% 批量PCFT处理 (用于分块并行)
function X_batch = batch_pcft_processing(signal_batch, phase_params_batch, tm_norm, order)
% 批量PCFT处理 - 用于分块并行优化

[num_signals, N] = size(signal_batch);
X_batch = zeros(num_signals, N, 'like', 1j);

% 并行处理每个信号
parfor i = 1:num_signals
    signal_i = signal_batch(i, :);
    phase_params_i = phase_params_batch(i, :);
    
    [X_i, ~] = Multi_Order_PCFT_Core(signal_i, phase_params_i, tm_norm, order);
    X_batch(i, :) = X_i;
end
end

%% 自适应阶数选择
function optimal_order = adaptive_order_selection(signal, tm_norm, max_order)
% 自适应选择最优多项式阶数

if nargin < 3
    max_order = 4;
end

N = length(signal);
order_scores = zeros(max_order + 1, 1);

% 对每个阶数计算聚焦度评分
for order = 0:max_order
    % 使用简单的参数估计
    phase_params = estimate_phase_params_simple(signal, tm_norm, order);
    
    % 计算PCFT
    [X_pcft, ~] = Multi_Order_PCFT_Core(signal, phase_params, tm_norm, order);
    
    % 计算聚焦度 (峰值与平均值的比值)
    spectrum_power = abs(X_pcft).^2;
    peak_power = max(spectrum_power);
    mean_power = mean(spectrum_power);
    
    order_scores(order + 1) = peak_power / (mean_power + eps);
end

% 选择得分最高的阶数
[~, optimal_idx] = max(order_scores);
optimal_order = optimal_idx - 1;
end

%% 简单相位参数估计
function phase_params = estimate_phase_params_simple(signal, tm_norm, order)
% 简单的相位参数估计 (用于自适应阶数选择)

phase_params = zeros(1, order + 1);

% 使用短时傅里叶变换估计瞬时频率
window_size = min(32, length(signal)/4);
[S, F, T] = spectrogram(signal, window_size, window_size/2, window_size, 1);

% 提取瞬时频率轨迹
[~, peak_indices] = max(abs(S), [], 1);
inst_freq = F(peak_indices);

% 多项式拟合
if length(inst_freq) > order
    poly_coeffs = polyfit(T, inst_freq, order);
    phase_params(1:length(poly_coeffs)) = fliplr(poly_coeffs); % 翻转顺序
end
end
