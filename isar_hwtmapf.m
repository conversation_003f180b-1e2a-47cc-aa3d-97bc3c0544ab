function z=isar_hwtmapf(xf,w,len)

%% Function to compute Harmonic Wavelet Map.

%% Author : SHREYAMSHA KUMAR B.K.
%% Created on 26-02-2005. 
%% updated on 26-02-2005.

% 获取窗口长度
k=length(w);

% 确保w是行向量
if size(w,1) > 1
    w = w';
end

% 初始化输出矩阵
z = zeros(len, len);

% 遍历每一个时间位置
for ii=1:len
    % 初始化频谱
    y = zeros(1,len);
    
    % 计算窗口中心位置
    center = ii;
    
    % 计算窗口起始和结束位置
    half_win = floor(k/2);
    start_idx = max(1, center - half_win);
    end_idx = min(len, center + half_win - (1-mod(k,2))); % 处理奇偶窗口
    
    % 计算需要从窗口截取的部分
    win_start = 1 + max(0, half_win - (center-1));
    win_end = win_start + (end_idx - start_idx);
    
    % 确保不超出窗口范围
    if win_end > k
        win_end = k;
    end
    
    % 确保索引维度匹配
    actual_signal_len = end_idx - start_idx + 1;
    actual_window_len = win_end - win_start + 1;
    
    % 如果长度不匹配，调整窗口长度
    if actual_signal_len ~= actual_window_len
        win_end = win_start + actual_signal_len - 1;
        if win_end > k
            win_end = k;
            % 如果窗口不够长，则调整信号段
            end_idx = start_idx + (win_end - win_start);
        end
    end
    
    % 应用窗口函数
    y(start_idx:end_idx) = w(win_start:win_end) .* xf(start_idx:end_idx);
    
    % 计算时频图
    temp = ifft(y)';
    z(:,ii) = abs(temp).^2; % 幅度平方
end
% function z=isar_hwtmapf(xf,w,len)
%     k=length(w);
%     z = zeros(len, len); % Initialize output matrix with full length
% 
%     for ii=1:len
%         start_idx = max(1, ii - floor(k/2));
%         end_idx = min(len, ii + floor(k/2) - 1);
%         windowed_xf = w(1:end_idx-start_idx+1) .* xf(start_idx:end_idx);
% 
%         y=zeros(1,len);
%         y(start_idx:end_idx) = windowed_xf;
%         temp=ifft(y)';
%         z(:,ii)=abs(temp).^2; %% Magnitude Squared.
%     end
% end
% function z=isar_hwtmapf(xf,w,len)
%     k=length(w);
%     half_k = floor(k / 2);
%     z = zeros(len, len); % Initialize output matrix with full length
% 
%     for ii=1:len
%         start_idx = max(1, ii - half_k);
%         end_idx = min(len, ii + half_k - 1);
%         windowed_xf = w(1:end_idx-start_idx+1) .* xf(start_idx:end_idx);
% 
%         y=zeros(1,len);
%         y(start_idx:end_idx) = windowed_xf;
%         temp=ifft(y)';
%         z(:,ii)=abs(temp).^2; %% Magnitude Squared.
%     end
% end
% 
% 
