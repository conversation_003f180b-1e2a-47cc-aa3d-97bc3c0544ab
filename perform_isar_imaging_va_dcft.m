% perform_isar_imaging_va_dcft.m
% 改进版VMD-ADMM-OptimizedDCFT ISAR成像算法

function [ISAR_image_sparse, s_compensated_dominant_mode] = perform_isar_imaging_va_dcft(radar_data, params_proc)

% 1. 参数初始化
[num_range_bins, num_azimuth] = size(radar_data);
fprintf('  处理数据尺寸: %d x %d\n', num_range_bins, num_azimuth);

% 从params_proc中提取参数
K_vmd = params_proc.vmd.K;
tm_normalized = (0:num_azimuth-1) / num_azimuth; % VMD和相位估计内部使用的归一化时间

% 初始化输出
ISAR_image_sparse = zeros(num_range_bins, num_azimuth, 'like', 1j*radar_data(1));
s_compensated_dominant_mode = zeros(size(radar_data), 'like', radar_data); % 用于可选的非稀疏对比

% 2. 预处理：应用一个简单的距离对齐
% 这有助于提高处理效果，特别是对于存在距离单元漂移的数据
fprintf('  执行初步距离对齐...\n');
aligned_data = radar_data;
for j = 2:num_azimuth
    [~, shift_idx] = max(abs(xcorr(radar_data(:,1), radar_data(:,j))));
    shift_needed = shift_idx - num_range_bins;
    if abs(shift_needed) < num_range_bins/10 % 防止过大偏移
        aligned_data(:,j) = circshift(radar_data(:,j), -shift_needed);
    else
        aligned_data(:,j) = radar_data(:,j); % 保持原样
    end
end
radar_data = aligned_data;

% 3. 估计和去除DC分量（消除强静态散射点的影响）
mean_data = mean(radar_data, 2);
dc_suppressed_data = radar_data - mean_data * ones(1, num_azimuth);

% 4. 主循环: 处理每个距离单元
fprintf('  开始逐距离单元处理 (共 %d 个)...\n', num_range_bins);
parfor r_idx = 1:num_range_bins % 使用 parfor 进行并行处理
    if mod(r_idx, round(num_range_bins/10)) == 0 && r_idx > 1
        fprintf('    正在处理距离单元: %d/%d\n', r_idx, num_range_bins);
    end

    signal_orig = dc_suppressed_data(r_idx, :); % 当前距离单元的信号

    % 跳过能量过低的距离单元
    signal_energy = sum(abs(signal_orig).^2);
    if signal_energy < 1e-8 * num_azimuth % 动态能量阈值
        ISAR_image_sparse(r_idx, :) = fft(signal_orig); % 对低能量单元直接FFT
        s_compensated_dominant_mode(r_idx, :) = signal_orig;
        continue;
    end

    % 对当前信号进行归一化，避免数值问题
    signal_norm_factor = max(abs(signal_orig));
    if signal_norm_factor < 1e-10, signal_norm_factor = 1; end
    signal = signal_orig / signal_norm_factor;

    % 初始化VMD模态和中心频率
    u_k = zeros(K_vmd, num_azimuth, 'like', 1j*signal(1));
    omega_k_vmd = zeros(K_vmd, 1); 
    
    % VMD初始化基于FFT峰值
    if strcmp(params_proc.vmd.init_omega_method, 'peaks')
        fft_spectrum = abs(fft(signal));
        [~, locs] = findpeaks(fft_spectrum, 'SortStr', 'descend', 'NPeaks', K_vmd*2);
        
        % 确保峰值位置有足够间隔，避免重复模态
        if length(locs) >= K_vmd
            selected_locs = locs(1:min(length(locs), K_vmd));
            omega_k_vmd(1:length(selected_locs)) = (selected_locs-1)/num_azimuth;
        else
            % 如果找不到足够的峰值，使用均匀分布
            for k_idx = 1:K_vmd
                omega_k_vmd(k_idx) = (k_idx-1)/(K_vmd*2) + 0.1; % 从0.1开始，避开DC
            end
        end
    else % linear初始化
        for k_idx = 1:K_vmd
            omega_k_vmd(k_idx) = (k_idx-1)/(K_vmd*2) + 0.1; % 从0.1开始，避开DC
        end
    end

    % 初始化相位多项式系数 [fd, ka, kb] (对应相位中的 t, t^2, t^3)
    poly_coeffs_k = zeros(K_vmd, params_proc.phase_est.poly_order); 

    % 初始化ADMM变量 (对于当前距离单元)
    X_admm = fft(signal); % 初始稀疏重建结果 (频谱)
    Z_admm = X_admm;      % 辅助变量
    Lambda_admm_dual = zeros(size(X_admm), 'like', 1j*signal(1)); % 拉格朗日乘子 (对偶变量)

    phi_k_phase_model = zeros(K_vmd, num_azimuth); % 存储每个模态的相位模型 phi(t)

    % 5. 联合迭代优化 (VMD <-> 相位估计)
    for global_iter = 1:params_proc.fusion.max_global_iter
        signal_sum_prev_iter = sum(u_k, 1); % 用于收敛检查

        % 5.1 VMD分解
        % 改进：首次迭代不使用相位引导，后续迭代再应用相位引导
        if global_iter == 1
            % 第一次迭代：纯VMD分解，不使用相位引导
            [u_k, omega_k_vmd, ~] = vmd_decompose(signal, params_proc.vmd, u_k, omega_k_vmd);
        else
            % 后续迭代：使用相位引导的VMD分解
            [u_k, omega_k_vmd, ~] = vmd_decompose(signal, params_proc.vmd, u_k, omega_k_vmd, phi_k_phase_model, params_proc.fusion.alpha_vmd_guidance);
        end

        % 5.2 优化后的相位参数估计 (逐模态)
        for k_idx = 1:K_vmd
            mode_energy = sum(abs(u_k(k_idx,:)).^2);
            if mode_energy < 1e-6 * sum(abs(signal).^2) % 能量比例阈值
                poly_coeffs_k(k_idx,:) = 0;
                phi_k_phase_model(k_idx,:) = 0;
                continue;
            end
            
            % 相位估计，使用之前的系数作为初始值
            [estimated_coeffs, estimated_phase] = estimate_phase_polynomial_improved(u_k(k_idx,:), ...
                                                              poly_coeffs_k(k_idx,:), ...
                                                              params_proc.phase_est, ...
                                                              tm_normalized, params_proc.PRF);
            poly_coeffs_k(k_idx,:) = estimated_coeffs;
            phi_k_phase_model(k_idx,:) = estimated_phase;
        end

        % 5.3 全局收敛检查
        signal_sum_current_iter = sum(u_k, 1);
        if global_iter > 1
            change = norm(signal_sum_current_iter - signal_sum_prev_iter) / (norm(signal_sum_prev_iter) + eps);
            if change < params_proc.fusion.global_tol
                break;
            end
        end
    end % end global iteration

    % 6. 构造补偿并叠加后的信号
    Y_compensated_sum = zeros(1, num_azimuth, 'like', 1j*signal(1));
    
    % 6.1 计算模态能量，用于加权补偿
    mode_energies = sum(abs(u_k).^2, 2);
    total_energy = sum(mode_energies);
    if total_energy > 0
        mode_weights = mode_energies / total_energy;
    else
        mode_weights = ones(K_vmd, 1) / K_vmd;
    end
    
    % 6.2 加权叠加补偿后的模态
    for k_idx = 1:K_vmd
        if mode_weights(k_idx) > 0.05 % 仅使用显著模态
            Y_compensated_sum = Y_compensated_sum + mode_weights(k_idx) * u_k(k_idx,:) .* exp(-1j * phi_k_phase_model(k_idx,:));
        end
    end
    Y_fft = fft(Y_compensated_sum);

    % 7. 执行ADMM稀疏重建
    [X_reconstructed_spectrum, ~, ~] = admm_reconstruct_improved(Y_fft, params_proc.admm, X_admm, Z_admm, Lambda_admm_dual);

    % 8. 存储当前距离单元的稀疏频谱 (未移位)
    ISAR_image_sparse(r_idx, :) = X_reconstructed_spectrum * signal_norm_factor; % 恢复幅度

    % 9. 保存用主导模态相位补偿的原始信号，用于对比
    [~, dominant_idx] = max(mode_energies);
    dominant_phase_compensation = phi_k_phase_model(dominant_idx, :);
    s_compensated_dominant_mode(r_idx, :) = signal_orig .* exp(-1j * dominant_phase_compensation);

end % end parfor r_idx

fprintf('  所有距离单元处理完毕。\n');

end

% 改进版相位估计函数
function [poly_coeffs_estimated, phase_estimated] = estimate_phase_polynomial_improved(signal_mode, initial_coeffs, params_phase_est, tm_normalized, PRF)
% 输入:
%   signal_mode       - 单个VMD模态 (1D)
%   initial_coeffs    - [fd0, ka0, kb0] 初始系数估计 (用于迭代优化或范围中心)
%   params_phase_est  - 相位估计参数: poly_order, fd_search_range_factor, etc.
%   tm_normalized     - 归一化慢时间轴 (0 to N-1)/N
%   PRF               - 脉冲重复频率 (用于fd的实际单位转换和搜索范围)
% 输出:
%   poly_coeffs_estimated - [fd_est, ka_est, kb_est] 估计的系数 (针对 tm_normalized)
%   phase_estimated       - 2*pi*(fd*t + 0.5*ka*t^2 + (1/6)*kb*t^3) 估计的相位

N = length(signal_mode);
poly_order = params_phase_est.poly_order;
poly_coeffs_estimated = zeros(1, poly_order);

% --- 1. 估计 fd (线性项系数, 多普勒中心) ---
% 使用更健壮的频率估计（结合FFT峰值和能量质心）
signal_mode_fft = fft(signal_mode);
power_spectrum = abs(signal_mode_fft).^2;

% 1.1 FFT峰值检测
[~, idx_max_fft] = max(power_spectrum);
fd_est_norm_peak = (idx_max_fft - 1) / N; % 归一化频率 (0 to 1)

% 1.2 频谱能量质心计算
freq_axis = (0:N-1) / N;
centroid_numerator = sum(freq_axis .* power_spectrum);
centroid_denominator = sum(power_spectrum);
if centroid_denominator > 0
    fd_est_norm_centroid = centroid_numerator / centroid_denominator;
else
    fd_est_norm_centroid = fd_est_norm_peak;
end

% 1.3 结合两种估计（加权平均）
fd_est_norm = 0.7 * fd_est_norm_peak + 0.3 * fd_est_norm_centroid;

% 1.4 将fd_est_norm调整到 [-0.5, 0.5] 范围
if fd_est_norm > 0.5 
    fd_est_norm = fd_est_norm - 1;
end
poly_coeffs_estimated(1) = fd_est_norm;

% --- 2. 估计 ka (二次项系数, 线性调频率) ---
if poly_order >= 2
    % 2.1 补偿fd
    signal_comp_fd = signal_mode .* exp(-1j * 2*pi * poly_coeffs_estimated(1) * tm_normalized);
    
    % 2.2 使用初始值作为搜索中心（如果有）
    ka_center = 0;
    if ~isempty(initial_coeffs) && length(initial_coeffs) >= 2 && abs(initial_coeffs(2)) > 1e-10
        ka_center = initial_coeffs(2);
    end
    
    % 2.3 确定搜索范围
    % 更合理的ka范围（考虑到实际调频率的物理意义）
    max_chirp_rate_hz_s = (PRF/2); % 假设0.5s内频率可能变化PRF/2
    ka_norm_max_abs = max_chirp_rate_hz_s / PRF * 2; % 对应于归一化时间单位的ka
    
    % 2.4 自适应搜索范围（根据信号特性调整）
    ka_search_values = linspace(ka_center - ka_norm_max_abs, ka_center + ka_norm_max_abs, params_phase_est.ka_search_pts);
    
    % 2.5 搜索最优ka
    sharpness_ka = zeros(size(ka_search_values));
    for i_ka = 1:length(ka_search_values)
        ka_current = ka_search_values(i_ka);
        dechirped_signal = signal_comp_fd .* exp(-1j * 2*pi * 0.5 * ka_current * tm_normalized.^2);
        spectrum_dechirped = abs(fft(dechirped_signal)).^2;
        
        % 使用改进的锐度度量（结合对比度和熵）
        spectrum_norm = spectrum_dechirped / sum(spectrum_dechirped + eps);
        entropy = -sum(spectrum_norm .* log2(spectrum_norm + eps));
        contrast = std(spectrum_dechirped) / (mean(spectrum_dechirped) + eps);
        
        % 组合指标（最大化对比度，最小化熵）
        sharpness_ka(i_ka) = contrast / (entropy + eps);
    end
    
    % 2.6 找到最优ka
    [~, idx_max_ka] = max(sharpness_ka);
    poly_coeffs_estimated(2) = ka_search_values(idx_max_ka);
else
    if poly_order >= 2
        poly_coeffs_estimated(2) = 0;
    end
end

% --- 3. 估计 kb (三次项系数) ---
if poly_order >= 3
    % 3.1 补偿fd和ka
    signal_comp_fd_ka = signal_mode .* exp(-1j * 2*pi * (poly_coeffs_estimated(1) * tm_normalized + ...
                                                       0.5 * poly_coeffs_estimated(2) * tm_normalized.^2));
    
    % 3.2 使用初始值作为搜索中心（如果有）
    kb_center = 0;
    if ~isempty(initial_coeffs) && length(initial_coeffs) >= 3 && abs(initial_coeffs(3)) > 1e-10
        kb_center = initial_coeffs(3);
    end
    
    % 3.3 确定搜索范围（类似ka的方法）
    kb_norm_max_abs = (PRF/2) / PRF^2 * 3; % 启发式范围，适应性更强
    kb_search_values = linspace(kb_center - kb_norm_max_abs, kb_center + kb_norm_max_abs, params_phase_est.kb_search_pts);
    
    % 3.4 搜索最优kb
    sharpness_kb = zeros(size(kb_search_values));
    for i_kb = 1:length(kb_search_values)
        kb_current = kb_search_values(i_kb);
        decubic_signal = signal_comp_fd_ka .* exp(-1j * 2*pi * (1/6) * kb_current * tm_normalized.^3);
        spectrum_decubic = abs(fft(decubic_signal)).^2;
        
        % 使用与ka相同的改进锐度度量
        spectrum_norm = spectrum_decubic / sum(spectrum_decubic + eps);
        entropy = -sum(spectrum_norm .* log2(spectrum_norm + eps));
        contrast = std(spectrum_decubic) / (mean(spectrum_decubic) + eps);
        
        sharpness_kb(i_kb) = contrast / (entropy + eps);
    end
    
    % 3.5 找到最优kb
    [~, idx_max_kb] = max(sharpness_kb);
    poly_coeffs_estimated(3) = kb_search_values(idx_max_kb);
else
    if poly_order >= 3
        poly_coeffs_estimated(3) = 0;
    end
end

% --- 4. 构建最终的估计相位 ---
phase_estimated = zeros(size(tm_normalized));
if poly_order >= 1
    phase_estimated = phase_estimated + 2*pi * poly_coeffs_estimated(1) * tm_normalized;
end
if poly_order >= 2
    phase_estimated = phase_estimated + 2*pi * 0.5 * poly_coeffs_estimated(2) * tm_normalized.^2;
end
if poly_order >= 3
    phase_estimated = phase_estimated + 2*pi * (1/6) * poly_coeffs_estimated(3) * tm_normalized.^3;
end

end

% 改进版ADMM重建函数
function [X_sparse, Z_sparse, Lambda_dual] = admm_reconstruct_improved(Y_fft, params_admm, X_init, Z_init, Lambda_init)
% 输入:
%   Y_fft        - 观测到的频谱 (经VMD分解、相位补偿、叠加后FFT的结果)
%   params_admm  - ADMM参数: rho, lambda_sparsity, max_iter, tol
%   X_init       - X 的初始值
%   Z_init       - Z 的初始值
%   Lambda_init  - Lambda (对偶变量) 的初始值
% 输出:
%   X_sparse     - 重建的稀疏频谱
%   Z_sparse     - 辅助稀疏变量
%   Lambda_dual  - 更新后的对偶变量

% ADMM参数
rho = params_admm.rho;
lambda_s = params_admm.lambda_sparsity; % 稀疏项权重
max_iter = params_admm.max_iter;
tol = params_admm.tol;

% 初始化
X = X_init;
Z = Z_init;
Lambda_dual = Lambda_init;

% 改进：自适应稀疏权重（根据信号能量动态调整）
signal_energy = sum(abs(Y_fft).^2);
if signal_energy > 0
    noise_level_est = median(abs(Y_fft)) * 1.4826; % 噪声水平的稳健估计
    lambda_s_adapted = lambda_s * noise_level_est / sqrt(signal_energy);
else
    lambda_s_adapted = lambda_s;
end

% 改进：增加一个保持数据连贯性的约束
% 这有助于减少假散射点和保持目标结构
Y_fft_abs = abs(Y_fft);
mask = Y_fft_abs > 0.3 * max(Y_fft_abs); % 创建显著信号的掩码

% ADMM迭代
for iter = 1:max_iter
    X_prev = X;
    Z_prev = Z;

    % 更新X (数据保真度)
    X = (Y_fft - Lambda_dual + rho * Z) / (1 + rho);
    
    % 应用掩码约束（保留显著信号）
    X = X .* mask + X .* (1-mask) * 0.7; % 显著区域保持，非显著区域衰减
    
    % 更新Z (稀疏性)
    Z = soft_threshold(X + Lambda_dual/rho, lambda_s_adapted/rho);
    
    % 更新Lambda_dual (拉格朗日乘子 / 对偶变量)
    Lambda_dual = Lambda_dual + rho * (X - Z);

    % 收敛检查
    primal_residual = norm(X - Z);
    dual_residual = rho * norm(Z - Z_prev);

    if primal_residual < tol && dual_residual < tol
        break;
    end
end

X_sparse = X;
Z_sparse = Z;

end

% 辅助函数: 软阈值
function y = soft_threshold(x, threshold_val)
    y = sign(x) .* max(abs(x) - threshold_val, 0);
end 