% %% 简化自适应参数化ISAR聚焦方法
% function [focused_image, motion_params] = ISAR_Adaptive_Focus(radar_data, options)
% % 简化版自适应参数化ISAR聚焦方法
% % 输入:
% %   radar_data - 雷达数据 (距离单元 x 方位采样)
% %   options - 参数结构体
% 
% % 设置默认参数
% if nargin < 2
%     options = struct();
% end
% if ~isfield(options, 'max_iter'), options.max_iter = 5; end
% if ~isfield(options, 'lambda'), options.lambda = 0.01; end
% if ~isfield(options, 'PRF'), options.PRF = 1400; end
% if ~isfield(options, 'fc'), options.fc = 5.2e9; end
% if ~isfield(options, 'c'), options.c = 3e8; end
% if ~isfield(options, 'window_func'), options.window_func = @hamming; end
% 
% % 雷达参数
% [num_range_bins, num_pulses] = size(radar_data);
% lambda = options.c / options.fc;
% PRF = options.PRF;
% tm = (0:num_pulses-1)/PRF;
% 
% % 初始化运动参数
% if ~isfield(options, 'omega')
%     options.omega = [0.05, 0.2, 0.05];
% end
% if ~isfield(options, 'zeta')
%     options.zeta = [0.05, 0.1, 0.05];
% end
% if ~isfield(options, 'kappa')
%     options.kappa = [0.05, 0.4, 0.05];
% end
% 
% motion_params = struct('omega', options.omega, ...
%                       'zeta', options.zeta, ...
%                       'kappa', options.kappa);
% 
% % 显示设置
% figure('Name', '自适应ISAR聚焦');
% 
% % 主循环
% for iter = 1:options.max_iter
%     fprintf('迭代 %d/%d\n', iter, options.max_iter);
% 
%     % 步骤1: 应用当前运动参数的相位补偿
%     comp_data = apply_motion_compensation(radar_data, motion_params, tm, lambda);
% 
%     % 步骤2: 生成当前聚焦图像
%     image = generate_ISAR_image(comp_data, options.window_func);
% 
%     % 步骤3: 提取强散射点并更新运动参数
%     motion_params = update_motion_parameters(radar_data, image, motion_params, tm, lambda, options.lambda);
% 
%     % 可视化当前结果
%     subplot(2, 2, 1);
%     imagesc(20*log10(abs(image)./max(abs(image(:)))));
%     caxis([-30, 0]); colorbar; colormap jet; axis xy;
%     title(sprintf('迭代 %d', iter));
% 
%     subplot(2, 2, 2);
%     plot_motion_parameters(motion_params);
% 
%     drawnow;
% end
% 
% % 最终成像
% focused_image = generate_ISAR_image(comp_data, options.window_func);
% 
% end
% 
% %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%版本二
% function [focused_image, motion_params] = ISAR_Adaptive_Focus(radar_data, options)
%     % 设置默认参数
%     if nargin < 2
%         options = struct();
%     end
%     if ~isfield(options, 'max_iter'), options.max_iter = 5; end
%     if ~isfield(options, 'lambda'), options.lambda = 0.01; end
%     if ~isfield(options, 'PRF'), options.PRF = 1400; end
%     if ~isfield(options, 'fc'), options.fc = 5.2e9; end
%     if ~isfield(options, 'c'), options.c = 3e8; end
%     if ~isfield(options, 'window_func'), options.window_func = @hamming; end
% 
%     % 雷达参数
%     [num_range_bins, num_pulses] = size(radar_data);
%     lambda = options.c / options.fc;
%     PRF = options.PRF;
%     tm = (0:num_pulses-1)/PRF;
% 
%     % 初始化运动参数
%     if ~isfield(options, 'omega')
%         options.omega = [0.05, 0.2, 0.05];
%     end
%     if ~isfield(options, 'zeta')
%         options.zeta = [0.05, 0.1, 0.05];
%     end
%     if ~isfield(options, 'kappa')
%         options.kappa = [0.05, 0.4, 0.05];
%     end
% 
%     current_params = struct('omega', options.omega, ...
%                            'zeta', options.zeta, ...
%                            'kappa', options.kappa);
% 
%     % 显示设置
%     figure('Name', '改进的自适应ISAR聚焦');
% 
%     % 跟踪图像质量
%     best_image = [];
%     best_quality = -Inf;
%     best_params = current_params;
%     quality_history = zeros(1, options.max_iter);
% 
%     % 动态步长设置
%     step_decay = 1.0;
% 
%     % 主循环
%     for iter = 1:options.max_iter
%         fprintf('迭代 %d/%d (步长因子: %.2f)\n', iter, options.max_iter, step_decay);
% 
%         % 步骤1: 应用当前运动参数的相位补偿
%         comp_data = apply_motion_compensation(radar_data, current_params, tm, lambda);
% 
%         % 步骤2: 生成当前聚焦图像
%         image = generate_ISAR_image(comp_data, options.window_func);
% 
%         % 计算图像质量
%         current_quality = calculate_image_quality(image);
%         quality_history(iter) = current_quality;
%         fprintf('  当前图像质量: %.4f\n', current_quality);
% 
%         % 保存最佳结果
%         if current_quality > best_quality
%             best_quality = current_quality;
%             best_image = image;
%             best_params = current_params;
%             fprintf('  发现更好的结果！\n');
%         end
% 
%         % 如果质量开始下降且下降幅度大，考虑提前停止
%         if iter > 1 && quality_history(iter) < 0.9*quality_history(iter-1)
%             fprintf('  图像质量显著下降，使用前一次迭代结果\n');
%             break;
%         end
% 
%         % 步骤3: 提取强散射点并更新运动参数
%         new_params = update_motion_parameters(radar_data, image, current_params, tm, lambda,  step_decay);
% 
%         % 可视化当前结果
%         subplot(2, 2, 1);
%         imagesc(20*log10(abs(image)./max(abs(image(:)))));
%         caxis([-30, 0]); colorbar; colormap jet; axis xy;
%         title(sprintf('迭代 %d - 质量: %.4f', iter, current_quality));
%         xlabel('方位'); ylabel('距离');
% 
%         subplot(2, 2, 2);
%         plot(1:iter, quality_history(1:iter), '-o');
%         title('图像质量历史');
%         xlabel('迭代'); ylabel('质量指标');
%         grid on;
% 
%         subplot(2, 2, 3:4);
%         plot_motion_parameters(new_params);
% 
%         drawnow;
% 
%         % 更新参数
%         current_params = new_params;
% 
%         % 每次迭代后衰减步长
%         step_decay = step_decay * 0.7;
%     end
% 
%     % 使用最佳参数生成最终图像
%     final_comp_data = apply_motion_compensation(radar_data, best_params, tm, lambda);
%     focused_image = generate_ISAR_image(final_comp_data, options.window_func);
%     motion_params = best_params;
% 
%     % 最终结果展示
%     figure('Name', '最终ISAR聚焦结果');
%     imagesc(20*log10(abs(focused_image)./max(abs(focused_image(:)))));
%     caxis([-30, 0]); colorbar; colormap jet; axis xy;
%     title(sprintf('最终聚焦图像 - 质量: %.4f', best_quality));
%     xlabel('方位'); ylabel('距离');
% end
% 
% % 其他函数保持不变...
% %% 辅助函数
% 
% % 应用运动补偿
% function comp_data = apply_motion_compensation(radar_data, params, tm, lambda)
%     [num_range_bins, num_pulses] = size(radar_data);
%     comp_data = zeros(size(radar_data));
% 
%     % 取中距离单元作为参考
%     mid_bin = round(num_range_bins/2);
% 
%     % 计算参考相位
%     omega = params.omega;
%     zeta = params.zeta;
%     kappa = params.kappa;
% 
%     % 为简化计算，仅使用Y轴分量
%     f_ref = -(2/lambda) * omega(2) * mid_bin;
%     alpha_ref = -(2/lambda) * zeta(2) * mid_bin;
%     beta_ref = -(2/lambda) * kappa(2) * mid_bin;
% 
%     % 生成补偿相位
%     phase_comp = zeros(num_range_bins, num_pulses);
%     for r = 1:num_range_bins
%         % 计算相对于中心单元的位置
%         rel_pos = r - mid_bin;
% 
%         % 计算当前单元的相位参数
%         f = -(2/lambda) * omega(2) * rel_pos;
%         alpha = -(2/lambda) * zeta(2) * rel_pos;
%         beta = -(2/lambda) * kappa(2) * rel_pos;
% 
%         % 计算相对补偿相位
%         phase = 2*pi*((f-f_ref)*tm + 0.5*(alpha-alpha_ref)*tm.^2 + (1/6)*(beta-beta_ref)*tm.^3);
%         phase_comp(r,:) = exp(-1j * phase);
%     end
% 
%     % 应用相位补偿
%     comp_data = radar_data .* phase_comp;
% end
% 
% % 生成ISAR图像
% function image = generate_ISAR_image(radar_data, window_func)
%     [num_range_bins, num_pulses] = size(radar_data);
% 
%     % 应用窗函数
%     win = window_func(num_pulses);
%     window = repmat(win', num_range_bins, 1);
%     windowed_data = radar_data .* window;
% 
%     % 应用FFT生成图像
%     image = fftshift(fft(windowed_data, [], 2), 2);
% end
% 
% % 更新运动参数
% function params = update_motion_parameters(radar_data, image, current_params, tm, lambda, lambda_reg)
%     [num_range_bins, num_pulses] = size(radar_data);
% 
%     % 提取强散射点
%     img_power = abs(image).^2;
%     threshold = 0.3 * max(img_power(:));
%     [strong_r, strong_a] = find(img_power > threshold);
% 
%     % 如果没有找到足够的强散射点，保持当前参数
%     if length(strong_r) < 5
%         params = current_params;
%         fprintf('  未找到足够的强散射点，保持当前参数\n');
%         return;
%     end
% 
%     % 仅使用前10个最强散射点
%     if length(strong_r) > 10
%         [~, idx] = sort(img_power(sub2ind(size(img_power), strong_r, strong_a)), 'descend');
%         strong_r = strong_r(idx(1:10));
%         strong_a = strong_a(idx(1:10));
%     end
% 
%     % 从每个强散射点提取信号
%     signals = zeros(length(strong_r), num_pulses);
%     positions = zeros(length(strong_r), 1);
% 
%     for i = 1:length(strong_r)
%         r = strong_r(i);
%         signals(i,:) = radar_data(r,:);
%         positions(i) = r - round(num_range_bins/2);  % 相对位置
%     end
% 
%     % 估计相位参数
%     f_est = zeros(length(strong_r), 1);
%     alpha_est = zeros(length(strong_r), 1);
%     beta_est = zeros(length(strong_r), 1);
% 
%     for i = 1:length(strong_r)
%         [f_est(i), alpha_est(i), beta_est(i)] = estimate_phase_params(signals(i,:), tm);
%     end
% 
%     % 使用Y轴分量进行估计
%     R_mat = positions;
%     reg_mat = lambda_reg * eye(1);
% 
%     % 更新omega (Y轴分量)
%     b_vec = f_est * (lambda/(-2));
%     omega_y_new = (R_mat' * R_mat + reg_mat) \ (R_mat' * b_vec);
% 
%     % 更新zeta (Y轴分量)
%     b_vec = alpha_est * (lambda/(-2));
%     zeta_y_new = (R_mat' * R_mat + reg_mat) \ (R_mat' * b_vec);
% 
%     % 更新kappa (Y轴分量)
%     b_vec = beta_est * (lambda/(-2));
%     kappa_y_new = (R_mat' * R_mat + reg_mat) \ (R_mat' * b_vec);
% 
%     % 更新参数
%     params = current_params;
%     params.omega(2) = omega_y_new;
%     params.zeta(2) = zeta_y_new;
%     params.kappa(2) = kappa_y_new;
% 
%     % 限制参数变化
%     max_change = 0.3;
% 
%     for field = {'omega', 'zeta', 'kappa'}
%         f = field{1};
%         change = params.(f) - current_params.(f);
%         limited_change = sign(change) .* min(abs(change), max_change*abs(current_params.(f)));
%         params.(f) = current_params.(f) + limited_change;
%     end
% 
%     fprintf('  更新的运动参数:\n');
%     fprintf('    omega_y: %.4f\n', params.omega(2));
%     fprintf('    zeta_y: %.4f\n', params.zeta(2));
%     fprintf('    kappa_y: %.4f\n', params.kappa(2));
% end
% 
% % 估计相位参数
% function [f, alpha, beta] = estimate_phase_params(signal, tm)
%     % 提取相位
%     phase = unwrap(angle(signal));
% 
%     % 多项式拟合
%     p = polyfit(tm, phase, 3);
% 
%     % 提取参数
%     beta = p(1);     % 三次项
%     alpha = p(2);    % 二次项
%     f = p(3);        % 一次项
% end
% 
% % 绘制运动参数
% function plot_motion_parameters(params)
%     % 提取参数
%     omega = params.omega;
%     zeta = params.zeta;
%     kappa = params.kappa;
% 
%     % 创建参数向量 - 只使用Y轴分量进行简化
%     param_vals = [omega(2), zeta(2), kappa(2)];
%     param_names = {'ω_y', 'ζ_y', 'κ_y'};
% 
%     % 绘制参数
%     bar(param_vals);
%     set(gca, 'XTickLabel', param_names);
%     ylabel('参数值');
%     title('估计的运动参数');
%     grid on;
% end
% 
% function quality = calculate_image_quality(image)
%     % 综合图像对比度和熵的质量指标
%     I = abs(image);
%     I_norm = I / max(I(:));
% 
%     % 对比度
%     contrast = std(I_norm(:)) / mean(I_norm(:));
% 
%     % 熵 (熵越低越好)
%     I_prob = I_norm(:) / sum(I_norm(:));
%     I_prob(I_prob < eps) = eps;
%     entropy = -sum(I_prob .* log2(I_prob));
% 
%     % 强散射点集中度
%     threshold = 0.3 * max(I_norm(:));
%     strong_points = I_norm > threshold;
%     concentration = sum(strong_points(:)) / numel(I_norm);
% 
%     % 组合指标 (高对比度、低熵、适度集中度为佳)
%     quality = contrast - 0.2*entropy - 5*abs(concentration - 0.05);
% end

function [focused_image, motion_params, quality_metrics] = Fast_ISAR_Focus(radar_data, options)
%% 高性能ISAR自适应聚焦算法
% 输入:
%   radar_data - 雷达数据 (距离单元 x 方位采样)
%   options - 参数结构体
% 输出:
%   focused_image - 聚焦后的ISAR图像
%   motion_params - 估计的运动参数
%   quality_metrics - 图像质量指标

% 设置默认参数
if nargin < 2
    options = struct();
end
if ~isfield(options, 'max_iter'), options.max_iter = 5; end
if ~isfield(options, 'PRF'), options.PRF = 1400; end
if ~isfield(options, 'fc'), options.fc = 5.2e9; end
if ~isfield(options, 'c'), options.c = 3e8; end
if ~isfield(options, 'window_func'), options.window_func = @hamming; end
if ~isfield(options, 'scatterer_threshold'), options.scatterer_threshold = 0.3; end
if ~isfield(options, 'downsample_factor'), options.downsample_factor = 1; end % 1表示不降采样

% 预处理 - 可选的降采样加速
if options.downsample_factor > 1
    radar_data = radar_data(:, 1:options.downsample_factor:end);
    fprintf('应用降采样: 脉冲降采样因子 = %d\n', options.downsample_factor);
end

% 雷达参数
[num_range_bins, num_pulses] = size(radar_data);
lambda = options.c / options.fc;
PRF = options.PRF;
tm = (0:num_pulses-1)/PRF;

% 初始化运动参数
if ~isfield(options, 'omega')
    options.omega = [0.05, 0.2, 0.05];
end
if ~isfield(options, 'zeta')
    options.zeta = [0.05, 0.1, 0.05];
end
if ~isfield(options, 'kappa')
    options.kappa = [0.05, 0.4, 0.05];
end

current_params = struct('omega', options.omega, ...
                       'zeta', options.zeta, ...
                       'kappa', options.kappa);

% 跟踪图像质量
best_quality = -Inf;
best_image = [];
best_params = current_params;
quality_history = zeros(1, options.max_iter);

% 初始化显示
figure('Name', '快速自适应ISAR聚焦', 'Position', [100, 100, 900, 600]);

% 主迭代循环
for iter = 1:options.max_iter
    tic; % 开始计时
    fprintf('迭代 %d/%d\n', iter, options.max_iter);
    
    % 步骤1: 应用当前运动参数的相位补偿 (向量化处理)
    comp_data = fast_motion_compensation(radar_data, current_params, tm, lambda);
    
    % 步骤2: 生成当前聚焦图像
    current_image = generate_ISAR_image(comp_data, options.window_func);
    
    % 步骤3: 快速提取强散射点
    [strong_r, strong_a, strong_vals] = fast_extract_scatterers(current_image, options.scatterer_threshold);
    
    % 步骤4: 快速更新运动参数
    new_params = fast_update_motion_parameters(radar_data, strong_r, strong_a, strong_vals, current_params, tm, lambda);
    
    % 计算图像质量
    current_quality = fast_calculate_image_quality(current_image);
    quality_history(iter) = current_quality;
    
    % 保存最佳结果
    if current_quality > best_quality
        best_quality = current_quality;
        best_image = current_image;
        best_params = new_params;
        fprintf('  发现更好的结果！质量: %.4f\n', current_quality);
    end
    
    % 可视化当前结果
    subplot(2, 2, 1);
    imagesc(20*log10(abs(current_image)./max(abs(current_image(:)))));
    caxis([-30, 0]); colorbar; colormap jet; axis xy;
    title(sprintf('迭代 %d - 质量: %.4f', iter, current_quality));
    xlabel('方位'); ylabel('距离');
    
    subplot(2, 2, 2);
    plot(1:iter, quality_history(1:iter), '-o');
    title('图像质量历史');
    xlabel('迭代'); ylabel('质量指标');
    grid on;
    
    subplot(2, 2, 3:4);
    plot_motion_parameters(new_params);
    
    iter_time = toc; % 结束计时
    fprintf('  迭代耗时: %.2f 秒\n', iter_time);
    
    drawnow;
    
    % 提前停止条件
    if iter > 2 && quality_history(iter) < quality_history(iter-1) && quality_history(iter-1) < quality_history(iter-2)
        fprintf('  图像质量连续下降，提前停止迭代\n');
        break;
    end
    
    % 更新参数
    current_params = new_params;
end

% 使用最佳参数生成最终图像
final_comp_data = fast_motion_compensation(radar_data, best_params, tm, lambda);
focused_image = generate_ISAR_image(final_comp_data, options.window_func);
motion_params = best_params;

% 计算最终质量指标
contrast = fast_calculate_contrast(focused_image);
entropy = fast_calculate_entropy(focused_image);

quality_metrics = struct('contrast', contrast, ...
                         'entropy', entropy, ...
                         'quality_score', best_quality);

% 最终结果展示
figure('Name', '最终ISAR聚焦结果', 'Position', [150, 150, 800, 600]);

subplot(2,2,1);
imagesc(20*log10(abs(focused_image)./max(abs(focused_image(:)))));
caxis([-30, 0]); colorbar; colormap jet; axis xy;
title(sprintf('最终聚焦图像 - 质量: %.4f', best_quality));
xlabel('方位'); ylabel('距离');

subplot(2,2,2);
plot_motion_parameters(motion_params);

subplot(2,2,3);
plot(1:find(quality_history ~= 0, 1, 'last'), quality_history(quality_history ~= 0), '-o');
title('优化过程中的图像质量');
xlabel('迭代次数'); ylabel('质量指标');
grid on;

subplot(2,2,4);
bar([contrast, -entropy]);
set(gca, 'XTickLabel', {'对比度', '负熵'});
title('图像质量指标');
grid on;

end

%% 快速运动补偿函数 (向量化实现)
function comp_data = fast_motion_compensation(radar_data, params, tm, lambda)
    [num_range_bins, num_pulses] = size(radar_data);
    
    % 参考点（中心距离单元）
    mid_bin = round(num_range_bins/2);
    
    % 提取Y轴运动参数
    omega_y = params.omega(2);
    zeta_y = params.zeta(2);
    kappa_y = params.kappa(2);
    
    % 计算位置相对矩阵 (向量化)
    rel_pos = (1:num_range_bins)' - mid_bin;
    
    % 计算每个距离单元的相位参数 (向量化)
    f = -(2/lambda) * omega_y * rel_pos;
    alpha = -(2/lambda) * zeta_y * rel_pos;
    beta = -(2/lambda) * kappa_y * rel_pos;
    
    % 参考相位参数
    f_ref = -(2/lambda) * omega_y * 0;  % mid_bin - mid_bin = 0
    alpha_ref = -(2/lambda) * zeta_y * 0;
    beta_ref = -(2/lambda) * kappa_y * 0;
    
    % 准备时间矩阵 (为了向量化)
    [R, T] = meshgrid(1:num_range_bins, tm);
    T = T';  % 转置以匹配雷达数据维度
    
    % 直接计算相位补偿 (向量化)
    phase = 2*pi*( (f-f_ref).*T + 0.5*(alpha-alpha_ref).*T.^2 + (1/6)*(beta-beta_ref).*T.^3 );
    phase_comp = exp(-1j * phase);
    
    % 应用相位补偿
    comp_data = radar_data .* phase_comp;
end

%% 生成ISAR图像
function image = generate_ISAR_image(radar_data, window_func)
    [num_range_bins, num_pulses] = size(radar_data);
    
    % 应用窗函数
    win = window_func(num_pulses);
    window = repmat(win', num_range_bins, 1);
    windowed_data = radar_data .* window;
    
    % 应用FFT生成图像
    image = fftshift(fft(windowed_data, [], 2), 2);
end

%% 快速提取强散射点
function [r_idx, a_idx, values] = fast_extract_scatterers(image, threshold_ratio)
    % 计算图像功率
    img_power = abs(image).^2;
    
    % 快速阈值处理
    max_power = max(img_power(:));
    threshold = threshold_ratio * max_power;
    
    % 找出超过阈值的点
    [r_idx, a_idx] = find(img_power > threshold);
    
    % 取前50个最强点，避免处理太多散射点
    if length(r_idx) > 50
        [sorted_vals, sort_idx] = sort(img_power(sub2ind(size(img_power), r_idx, a_idx)), 'descend');
        r_idx = r_idx(sort_idx(1:50));
        a_idx = a_idx(sort_idx(1:50));
    end
    
    % 获取对应的复值
    values = image(sub2ind(size(image), r_idx, a_idx));
end

%% 快速更新运动参数
function params = fast_update_motion_parameters(radar_data, strong_r, strong_a, strong_vals, current_params, tm, lambda)
    [num_range_bins, num_pulses] = size(radar_data);
    
    % 如果没有找到强散射点，返回当前参数
    if isempty(strong_r)
        params = current_params;
        return;
    end
    
    % 参考点
    mid_bin = round(num_range_bins/2);
    
    % 计算相对位置
    positions = strong_r - mid_bin;
    
    % 提取信号
    signals = zeros(length(strong_r), num_pulses);
    for i = 1:length(strong_r)
        signals(i,:) = radar_data(strong_r(i),:);
    end
    
    % 使用FFT加速的相位参数估计
    [f_est, alpha_est, beta_est] = fast_estimate_phase_params(signals, tm);
    
    % 构建最小二乘问题
    % 使用Y轴分量估计
    X = positions;
    
    % 避免病态问题，加入正则化
    lambda_reg = 0.01;
    reg_mat = lambda_reg * eye(1);
    
    % 解决最小二乘问题
    Y_f = f_est * (lambda/(-2));
    Y_alpha = alpha_est * (lambda/(-2));
    Y_beta = beta_est * (lambda/(-2));
    
    % 计算正规方程解
    XtX = X' * X + reg_mat;
    
    % 更新omega (Y轴分量)
    omega_y_new = (X' * Y_f) / XtX;
    
    % 更新zeta (Y轴分量)
    zeta_y_new = (X' * Y_alpha) / XtX;
    
    % 更新kappa (Y轴分量)
    kappa_y_new = (X' * Y_beta) / XtX;
    
    % 限制参数变化幅度
    max_change_ratio = 0.3;
    
    omega_y_change = limit_change(omega_y_new - current_params.omega(2), max_change_ratio * abs(current_params.omega(2)));
    zeta_y_change = limit_change(zeta_y_new - current_params.zeta(2), max_change_ratio * abs(current_params.zeta(2)));
    kappa_y_change = limit_change(kappa_y_new - current_params.kappa(2), max_change_ratio * abs(current_params.kappa(2)));
    
    % 更新参数
    params = current_params;
    params.omega(2) = current_params.omega(2) + omega_y_change;
    params.zeta(2) = current_params.zeta(2) + zeta_y_change;
    params.kappa(2) = current_params.kappa(2) + kappa_y_change;
    
    % 输出更新信息
    fprintf('  更新的运动参数: omega_y=%.4f, zeta_y=%.4f, kappa_y=%.4f\n', ...
        params.omega(2), params.zeta(2), params.kappa(2));
end

%% 限制参数变化幅度的辅助函数
function limited_change = limit_change(change, max_change)
    if abs(change) > max_change
        limited_change = sign(change) * max_change;
    else
        limited_change = change;
    end
end

%% 基于FFT的快速相位参数估计
function [f, alpha, beta] = fast_estimate_phase_params(signals, tm)
    [num_signals, num_samples] = size(signals);
    f = zeros(num_signals, 1);
    alpha = zeros(num_signals, 1);
    beta = zeros(num_signals, 1);
    
    % 使用批处理向量化操作
    phases = unwrap(angle(signals), [], 2);
    
    % 使用多项式拟合估计相位参数
    % 避免循环，使用矩阵运算
    X = [ones(num_samples, 1), tm(:), tm(:).^2, tm(:).^3];
    
    % 计算伪逆以用于所有信号
    X_pinv = pinv(X);
    
    % 一次计算所有信号的多项式系数
    coeffs = (X_pinv * phases')';
    
    % 提取参数
    f = coeffs(:, 2) / (2*pi);      % 一次项
    alpha = 2 * coeffs(:, 3) / (2*pi); % 二次项
    beta = 6 * coeffs(:, 4) / (2*pi);  % 三次项
end

%% 绘制运动参数
function plot_motion_parameters(params)
    % 提取参数
    omega = params.omega;
    zeta = params.zeta;
    kappa = params.kappa;
    
    % 创建参数向量 - 只使用Y轴分量进行简化
    param_vals = [omega(2), zeta(2), kappa(2)];
    param_names = {'ω_y', 'ζ_y', 'κ_y'};
    
    % 绘制参数
    bar(param_vals);
    set(gca, 'XTickLabel', param_names);
    ylabel('参数值');
    title('估计的运动参数');
    grid on;
end

%% 快速计算图像质量
function quality = fast_calculate_image_quality(image)
    % 计算对比度和熵
    contrast = fast_calculate_contrast(image);
    entropy = fast_calculate_entropy(image);
    
    % 综合质量指标
    quality = contrast - 0.2*entropy;
end

%% 快速计算对比度
function contrast = fast_calculate_contrast(image)
    I = abs(image);
    I_norm = I / max(I(:));
    contrast = std(I_norm(:)) / mean(I_norm(:));
end

%% 快速计算熵
function entropy = fast_calculate_entropy(image)
    I = abs(image);
    I_norm = I / sum(I(:));
    I_norm(I_norm < eps) = eps;
    entropy = -sum(I_norm(:) .* log2(I_norm(:)));
end