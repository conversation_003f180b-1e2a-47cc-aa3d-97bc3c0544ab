%% Test script for ISAR Parameterized Dictionary-based Autofocus
% This script tests the parametric dictionary-based autofocus method
% on the ISAR data with complex 3D rotational motion.

clear all; close all; clc;

%% Load or generate ISAR data
% Using the same radar and motion parameters as in ISARrot_trans.m
% You can either load existing data or generate synthetic data

% Option 1: Use data from ISARrot_trans.m
run_ISARrot_trans = true;

if run_ISARrot_trans
    % Run ISARrot_trans.m to generate s_r_tm2 data
    run('ISARrot_trans.m');
    % Extract the range-compressed data
    radar_data = s_r_tm2;
    fprintf('Using data from ISARrot_trans.m\n');
    
    % Print radar data dimensions
    [num_range_bins, num_pulses] = size(radar_data);
    fprintf('Radar data dimensions: %d x %d (range bins x pulses)\n', num_range_bins, num_pulses);
    
    % Downsample to reduce computation time if needed
    downsample_factor = 2; % Adjust as needed
    if downsample_factor > 1
        radar_data = radar_data(1:downsample_factor:end, 1:downsample_factor:end);
        [num_range_bins, num_pulses] = size(radar_data);
        fprintf('Downsampled data dimensions: %d x %d\n', num_range_bins, num_pulses);
    end
else
    % Option 2: Load existing data if available
    % if exist('your_isar_data.mat', 'file')
    %     load('your_isar_data.mat');
    %     fprintf('Loaded ISAR data from file\n');
    % else
    %     error('ISAR data file not found. Run with run_ISARrot_trans=true instead.');
    % end
end

%% Display original (unfocused) image
figure('Name', 'Original Unfocused Image');
% Standard Range-Doppler processing
Y = fftshift(fft(radar_data, [], 2), 2);
G1 = 20*log10(abs(Y)./max(abs(Y(:))));
imagesc(G1); caxis([-30, 0]);
grid on; axis xy; colorbar;
xlabel('Azimuth'); ylabel('Range (m)');
title('Unfocused ISAR Image (Range-Doppler)');
colormap jet;

original_contrast = calcImageContrast(Y);
fprintf('Original image contrast: %.4f\n', original_contrast);

%% Apply conventional autofocus methods for comparison
% 1. Phase compensation using mid-range bin
fprintf('Applying mid-range phase compensation...\n');
X2 = PhaseComp_MidTr(radar_data);
Y2 = fftshift(fft(X2, [], 2), 2);
contrast_midtr = calcImageContrast(Y2);
fprintf('Mid-range phase compensation complete. Contrast: %.4f\n', contrast_midtr);

% 2. Minimum entropy method
fprintf('Applying minimum entropy method...\n');
miu = 1e-3;
P = 150;
[X3, fai] = PhaseComp_MinEntropy_NI(radar_data, miu, P);
Y3 = fftshift(fft(X3, [], 2), 2);
contrast_entropy = calcImageContrast(Y3);
fprintf('Minimum entropy method complete. Contrast: %.4f\n', contrast_entropy);

%% Apply our new parameterized dictionary-based autofocus method
% Set options for the autofocus method
options = struct();
options.max_iterations = 5; % Reduced from 10 for faster testing
options.tolerance = 1e-3;   % Slightly relaxed for faster convergence
options.lambda_x = 0.05;    % Decreased from 0.1 for less sparse solution
options.lambda_m = 0.01;    % Regularization for motion parameters
options.PRF = PRF;          % Use PRF from ISARrot_trans.m
options.fc = fc;            % Use carrier frequency from ISARrot_trans.m
options.c = c;              % Speed of light
options.display = 'iter';   % Show progress

% Initial motion parameter estimates (from ISARrot_trans.m)
options.initial_omega = [x_oumiga, y_oumiga, z_oumiga]; % Angular velocity
options.initial_zeta = [x_lamda, y_lamda, z_lamda];     % Angular acceleration
options.initial_kappa = [x_gamma, y_gamma, z_gamma];    % Angular jerk

fprintf('Initial motion parameters:\n');
fprintf('  omega: [%.4f, %.4f, %.4f]\n', options.initial_omega);
fprintf('  zeta: [%.4f, %.4f, %.4f]\n', options.initial_zeta);
fprintf('  kappa: [%.4f, %.4f, %.4f]\n', options.initial_kappa);

% Run the autofocus algorithm
fprintf('Running parameterized dictionary-based autofocus...\n');
tic;
try
    [focused_image, param_est, dict_contrast] = ISAR_Parameterized_Dictionary_AF(radar_data, options);
    autofocus_success = true;
    fprintf('Autofocus completed successfully!\n');
catch ME
    autofocus_success = false;
    fprintf('Error in autofocus algorithm: %s\n', ME.message);
    fprintf('At line %d in %s\n', ME.line, ME.stack(1).name);
    % Use minimum entropy results as fallback
    focused_image = Y3;
    dict_contrast = contrast_entropy;
    param_est = struct('omega', options.initial_omega, ...
                      'zeta', options.initial_zeta, ...
                      'kappa', options.initial_kappa);
end
toc;

%% Comparative display of results
figure('Name', 'ISAR Autofocus Comparison', 'Position', [100, 100, 1200, 800]);

% Original unfocused image
subplot(2, 2, 1);
imagesc(20*log10(abs(Y)./max(abs(Y(:)))));
caxis([-30, 0]);
title(sprintf('Original (Unfocused) Image\nContrast: %.4f', original_contrast));
xlabel('Azimuth'); ylabel('Range');
colorbar; axis xy; colormap jet;

% Mid-range phase compensation result
subplot(2, 2, 2);
imagesc(20*log10(abs(Y2)./max(abs(Y2(:)))));
caxis([-30, 0]);
title(sprintf('Phase Comp (Mid-range)\nContrast: %.4f', contrast_midtr));
xlabel('Azimuth'); ylabel('Range');
colorbar; axis xy;

% Minimum entropy result
subplot(2, 2, 3);
imagesc(20*log10(abs(Y3)./max(abs(Y3(:)))));
caxis([-30, 0]);
title(sprintf('Minimum Entropy Method\nContrast: %.4f', contrast_entropy));
xlabel('Azimuth'); ylabel('Range');
colorbar; axis xy;

% Our parameterized dictionary-based result
subplot(2, 2, 4);
imagesc(20*log10(abs(focused_image)./max(abs(focused_image(:)))));
caxis([-30, 0]);
if autofocus_success
    title(sprintf('Parameterized Dictionary Method\nContrast: %.4f', dict_contrast));
else
    title('Parameterized Dictionary Method\n(Failed - Using Min Entropy)');
end
xlabel('Azimuth'); ylabel('Range');
colorbar; axis xy;

% Print summary
fprintf('\n----- ISAR Autofocus Results -----\n');
fprintf('Original (unfocused) contrast: %.4f\n', original_contrast);
fprintf('Mid-range phase comp contrast: %.4f\n', contrast_midtr);
fprintf('Minimum entropy contrast: %.4f\n', contrast_entropy);
if autofocus_success
    fprintf('Parameterized dictionary contrast: %.4f\n', dict_contrast);
else
    fprintf('Parameterized dictionary method failed\n');
end

% Estimated motion parameters
fprintf('\nEstimated motion parameters:\n');
fprintf('Angular velocity (omega): [%.4f, %.4f, %.4f]\n', param_est.omega);
fprintf('Angular acceleration (zeta): [%.4f, %.4f, %.4f]\n', param_est.zeta);
fprintf('Angular jerk (kappa): [%.4f, %.4f, %.4f]\n', param_est.kappa);

%% Helper function to calculate image contrast
function contrast = calcImageContrast(image)
    % Calculate image contrast using ratio of standard deviation to mean
    I = abs(image);
    contrast = std(I(:)) / mean(I(:));
end 