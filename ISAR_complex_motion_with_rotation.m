%% ISAR复杂运动回波模型仿真（含匀速转动）
% 基于2015-01-30.md中的复杂运动模型，包含高阶多项式项、突变事件和匀速转动
% 包括回波生成和成像处理全过程
clear all; close all; clc;

%% 雷达参数设置
c = 3e8;                  % 光速 (m/s)
fc = 10e9;                % 载波频率 (Hz)
lambda = c/fc;            % 波长 (m)
Tp = 2e-6;                % 脉冲宽度 (s)
B = 800e6;                % 带宽 (Hz)
K = B/Tp;                 % 调频率 (Hz/s)
PRF = 500;                % 脉冲重复频率 (Hz)
PRI = 1/PRF;              % 脉冲重复间隔 (s)

%% 观测参数
M = 256;                  % 慢时间采样点数（脉冲数）
T = (M-1)*PRI;            % 总观测时间 (s)
t = (0:M-1)*PRI;          % 慢时间序列 (s)

%% 使用简单的13点散射点模型
L = 13;
x = [0, 5, 10, 15, -5, -10, -15, 5, 10, 15, -5, -10, -15];
y = [0, 5, 10, 15, 5, 10, 15, -5, -10, -15, -5, -10, -15];
scatters = x + j*y;  % 散射点初始位置（相对于质心）

% 可视化散射点模型
figure('Position', [100, 100, 800, 600]);
plot(x, y, 'o', 'MarkerSize', 4, 'MarkerFaceColor', 'b');
xlabel('X (m)'); ylabel('Y (m)');
title('简化散射点模型');
axis equal; grid on;

L = length(x);                    % 散射点数量
scatterers = [x, y, zeros(1, L)]';  % 散射点三维坐标 (3xL)
sigma = ones(1, L);               % 散射系数（简化为1）

%% 目标运动参数设置
% 基础运动参数（沿雷达径向，假设雷达在y轴负向）
R0 = 100e3;                       % 初始质心距离 (m)
v0 = 120;                         % 初始径向速度 (m/s)
a0 = 5;                           % 初始加速度 (m/s^2)

% 高阶多项式系数
c3 = 0.8;                         % 三阶项系数
c4 = 0.1;                         % 四阶项系数
c5 = 0.02;                        % 五阶项系数

% 突变事件设置 - 包含3个突变事件
% 1. 速度阶跃 - 在t1时刻速度突然增加
t1 = T/4;
A1 = 20;
g1 = @(t) (t >= 0);               % 阶跃函数

% 2. 加速度脉冲 - 在t2时刻短暂加速
t2 = T/2;
A2 = 20;
g2 = @(t) (t >= 0) .* exp(-50*t); % 衰减脉冲

% 3. 抖动/振荡 - 在t3时刻开始振荡
t3 = 3*T/4;
A3 = 5;
g3 = @(t) (t >= 0) .* sin(20*pi*t); % 正弦振荡

% 旋转参数（匀速转动）
omega = 0.05;                      % 角速度 (rad/s)，绕Z轴旋转
theta = omega * t;                % 每个时刻的旋转角度 (rad)

%% 距离历程计算 R(t)
% 基本运动项 (二阶多项式)
R_basic = R0 + v0*t + 0.5*a0*t.^2;

% 高阶多项式项
R_high = c3*t.^3 + c4*t.^4 + c5*t.^5;

% 突变项
R_event1 = A1 * g1(t - t1) .* (t - t1);  % 速度阶跃 (积分后为斜坡)
R_event2 = A2 * arrayfun(@(x) integral(@(t) g2(t-t2), 0, x), t);  % 加速度脉冲积分
R_event3 = A3 * arrayfun(g3, t - t3) .* (t >= t3);  % 振荡

% 总距离历程（质心）
R_t = R_basic + R_high + R_event1 + R_event2 + R_event3;

%% 瞬时多普勒频率及其导数计算（用于分析）
% 一阶导数 - 瞬时速度/多普勒
v_basic = v0 + a0*t;
v_high = 3*c3*t.^2 + 4*c4*t.^3 + 5*c5*t.^4;
v_event1 = A1 * g1(t - t1);
v_event2 = A2 * arrayfun(g2, t - t2) .* (t >= t2);
v_event3 = A3 * arrayfun(@(x) 20*pi*cos(20*pi*(x-t3)), t) .* (t >= t3);

v_total = v_basic + v_high + v_event1 + v_event2 + v_event3;
fd = 2 * v_total / lambda;  % 瞬时多普勒频率 (Hz)

%% 接收信号参数设置
% 快时间采样
max_extent = max(sqrt(x.^2 + y.^2)) * 2;
ts_min = -2*max_extent/c - Tp/2;
ts_max = 2*max_extent/c + Tp/2;
Ns = 256;                         % 快时间采样点数
ts = linspace(ts_min, ts_max, Ns); % 快时间序列
range_axis = linspace(-Ns/2, Ns/2-1, Ns) * c/(2*B);
%% 回波生成
% 预分配内存
raw = zeros(Ns, M);

% 参考距离（用于去斜处理）
Rref = R_t;
tauref = 2*Rref/c;

% 用于记录每个散射点在每个脉冲的瞬时位置
scatterer_positions = zeros(3, L, M);

fprintf('生成回波信号...\n');
for m = 1:M
    if mod(m, 50) == 0
        fprintf('处理脉冲 %d/%d\n', m, M);
    end
    
    % 计算当前脉冲的旋转矩阵（绕Z轴旋转）
    cos_theta = cos(theta(m));
    sin_theta = sin(theta(m));
    R_z = [cos_theta, -sin_theta, 0;
           sin_theta, cos_theta, 0;
           0, 0, 1];
    
    % 当前时刻的参考延时
    t_fast = ts + tauref(m);
    
    % 对每个散射点生成回波
    for l = 1:L
        % 应用旋转到散射点
        rotated_pos = R_z * [x(l); y(l); 0];
        scatterer_positions(:, l, m) = rotated_pos;
        
        % 散射点的绝对距离（雷达在y轴负向，因此距离增量为-rotated_pos(2)）
        R_scatter = R_t(m) - rotated_pos(2);  % 注意符号：径向为y轴负向
        tau = 2*R_scatter/c;
        
        % 时间窗
        win = (t_fast - tau >= -Tp/2) & (t_fast - tau < Tp/2);
        
        % 生成回波信号（LFM回波）
        raw(:,m) = raw(:,m) + sigma(l) * win.' .* ...
            exp(1j*2*pi*fc*(t_fast-tau) + 1j*pi*K*(t_fast-tau).^2).';
    end
    
    % 去斜处理
    sref = exp(1j*2*pi*fc*(t_fast-tauref(m)) + 1j*pi*K*(t_fast-tauref(m)).^2);
    raw(:,m) = raw(:,m) .* sref';
end

%% 保存原始数据
save('ISAR_aircraft_complex_motion.mat', 'raw', 'R_t', 'fd', 'v_total', ...
     'scatterers', 'scatterer_positions', 'PRF', 't', 'ts', 'fc', 'c', 'lambda', 'B');

%% ISAR成像处理
[N,M]=size(raw);
figure;
plot(abs(raw));
title('包络对齐前的一维距离像');

figure();
imagesc(abs(raw));
title('包络对齐前的一维距离像');

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%包络对齐%%%%%%%%%%%%%%%%%%%%%%%%
%x1=fftshift(fft(fftshift(x1)));
%s3=fftshift(ifft(fftshift(TmpData)));
x1=RangeAlign_Corr(raw ,8,30);                 %以两包络相关系数最大为包络对齐标准（数据，插值，窗）
%x1=RangeAlign_MinEnt(s3,8,300,300);        %最小熵包络对齐（数据，插值，对齐窗，搜索窗）
% figure;
% plot(abs(x1));
% title('包络对齐后的一维距离像');
% 
% figure();
% imagesc(abs(x1));
% title('包络对齐的一维距离像');
%x1=InterPoleCumulateCorrelateAlignXcorrFast2(x1,8,30);
% figure('name','包络对齐后');
% imagesc(20*log10(abs(x1)./max(abs(x1(:)))));
% caxis([-30,0]);
% grid on;axis xy;colorbar;
% 
 %x1=InterPoleCumulateCorrelateAlignXcorrFast2(hrrp,8,30)
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%x1=hrrp(180:490,1350:1700);
x2=PhaseComp_MidTr(x1);  
miu = 1e-3;
P=150;

% 
 [ret_data fai]=PhaseComp_MinEntropy_NI(x2,miu,P);
% 
x3 = ret_data;
% 
%x3=CompensateByMultiProNo(x1,30);

Y=RDimaging(x3); %直接FFT
G1=20*log10(abs(Y)./max(abs(Y(:))));
figure('name','DCT成像结果');
imagesc(G1);caxis([-30,0]);
grid on;axis xy;colorbar;%axis equal;
% axis([-0.6 0.6 y(select_row(1)) y(select_row(end))]);%set(gca,'xtick',[-0.2 0 0.2]);
xlabel('azimuth');ylabel('range (m)');colormap jet;



%% 可视化结果
fprintf('生成可视化图形...\n');

% Figure 1: 散射点模型和运动参数
figure('Position', [100, 100, 1000, 800]);
subplot(3,1,1);
plot(t, R_basic, 'b--', 'LineWidth', 1.5); hold on;
plot(t, R_basic + R_high, 'r--', 'LineWidth', 1.5);
plot(t, R_t, 'k-', 'LineWidth', 2);
legend('基本运动 (二阶多项式)', '加高阶项', '总距离历程');
xlabel('时间 (s)'); ylabel('距离 (m)');
title('目标距离历程 R(t)');
grid on;

% 标记突变事件
xline(t1, 'g--', '速度阶跃');
xline(t2, 'm--', '加速度脉冲');
xline(t3, 'c--', '振荡开始');

% Figure 1 (continued): 瞬时速度
subplot(3,1,2);
plot(t, v_total, 'k-', 'LineWidth', 2);
xlabel('时间 (s)'); ylabel('速度 (m/s)');
title('瞬时速度');
grid on;
xline(t1, 'g--');
xline(t2, 'm--');
xline(t3, 'c--');

% Figure 1 (continued): 散射点轨迹在xz平面上的投影（显示旋转）
subplot(3,1,3);
hold on;
for l = 1:min(L, 50) % 如果散射点太多，只显示部分
    x_traj = squeeze(scatterer_positions(1, l, :));
    z_traj = squeeze(scatterer_positions(2, l, :));
    plot(x_traj, z_traj, '.', 'MarkerSize', 1);
end
xlabel('X (m)'); ylabel('Z (m)');
title('散射点旋转轨迹投影（XZ平面）');
axis equal;
grid on;

% Figure 2: 原始回波数据和距离压缩
figure('Position', [100, 100, 1000, 400]);

imagesc(t, range_axis, 20*log10(abs(raw)/max(max(abs(raw)))));
xlabel('慢时间 (s)'); ylabel('相对距离 (m)');
title('原始回波数据');
colorbar;
colormap('jet');
caxis([-40, 0]);





fprintf('完成！\n'); 
