%-------------------------------------------------------------------------%
%--------        Range-azimuth image after range compressrion      -------%
%--------                 wuli<PERSON>(20101126-2010)                   -------%
%-------------------------------------------------------------------------%
clc; clear all;close all;

%-----------------------------����ɢ���ģ�� ------------------------------%
Pos = [-10 -1 0;-9 -1 0;-8 -1 0;-7 -1 0;-6 -1 0;-5 -1 0;-3.75 -1 0;-3 -1 0;-2 -1 0;-1 -1 0;...   %(10��) 
       0 -1 0;...      %(1��)
       1 -1 0;2 -1 0;3 -1 0;4 -1 0;5 -1 0;6 -1 0;7 -1 0;8 -1 0;9 -1 0;10 -1 0;...  %(10��)
       -9.5 0.2 0.5;...    %(1��)
       -9.5 1.2 0.5;-9 1 0;-8 1 0;-7 1 0;-6 1 0;-5.2 1.2 0;-4.1 1 0;-3 1 0;-2 1 0;-1 1 0;...  %(10��)
       0 1 0;...      %(1��)
       1 1 0;2 1 0;3 1 0;4 1 0;5 1 0;6 1 0;7 1 0;8 1 0;9 1 0;10 1 0;...     %(10��)
       10.5 -0.75 0;10.5 0.75 0;11 -0.5 0;11 0.5 0;11.5 0 0;... %�ױ�        %(5��)
       9.5 0.5 0.5;9.5 -0.5 0.5;9 0 0.5;8.5 0.5 0.5;8.5 -0.5 0.5;... %��ͷ��   %(5��) 9.5 0 0.5;9 0.5 0.5;9 -0.5 0.5;8.5 0 0.5;
       5 0 0.5;5 0 1;5 0 1.5;5 0 2;5 0 2.5;5 0 3;5 0 3.5;5 0 4;...   %��ͷ��       %(8��)
       5.5 0.5 0.5;5.5 -0.5 0.5;4.5 0.5 0.5;4.5 -0.5 0.5;... %��ͷ�˶�      %(4��)5 0 0.5; 5.5 0 0.5;5 0.5 0.5;5 -0.5 0.5; 4.5 0 0.5;  
       0.5 0.5 0.9;0.5 -0.5 0.9;-0.5 0.5 0.9;-0.5 -0.5 0.9;0 0 0.5;... %���ж�   %(5��) 0.5 0 1;-0.5 0 1;0 0.5 1;0 -0.5 1;
       -5 0 0.2;-5 0.2 0.8;-5 0.2 1.4;-5 0 2;... %��β��    %(4��) 
       -5.5 -0.5 0.5;-5.5 0.5 0.5;-4.4 0.5 0.5;-4.5 -0.6 0.5;...��β�˶�  %(4��)-5 0 0.5;-5.5 0 0.5; -5 0.5 0.5;-5 -0.5 0.5;-4.5 0 0.5;
       ];
% x_Pos2 = Pos2(:,1)*5;
% y_Pos2 = Pos2(:,2)*5;
% z_Pos2 = Pos2(:,3)*5;
% min_x_Pos = min(x_Pos2);
% min_y_Pos = min(y_Pos2);
% min_z_Pos = min(z_Pos2);
%----------for test---------%
% Pos = [10 -1 0;10 1 0;10.5 -0.75 0;10.5 0.75 0;9.5 0.5 0.5;9.5 -0.5 0.5;9 0 0.5;8.5 0.5 0.5;8.5 -0.5 0.5];   %
%---------------------------%
%-------------------------------------------------------------------------%

%-------------------------------    ��ʾ   -------------------------------%
x_Pos = Pos(:,1)*5;
y_Pos = Pos(:,2)*5;
z_Pos = Pos(:,3)*5;
min_x_Pos = min(x_Pos);
x_Pos = x_Pos + min_x_Pos;
min_y_Pos = min(y_Pos);
y_Pos = y_Pos + min_y_Pos;
min_z_Pos = min(z_Pos);
z_Pos = z_Pos + min_z_Pos;
% figure
% plot3(x_Pos,y_Pos,z_Pos,'.')
% grid on
%-------------------------------------------------------------------------%
% stop = stop;

R = [cos(3*pi/8)*cos(0),cos(3*pi/8)*sin(0),sin(3*pi/8)];  %�״�Ŀ�����ߵ�λʸ��
Num_point = length(x_Pos); %Ŀ�����
x_r = zeros(1,Num_point);
y_r = zeros(1,Num_point);
z_r = zeros(1,Num_point);
for n_point = 1:Num_point
    x_r(n_point) = y_Pos(n_point)*R(3)-z_Pos(n_point)*R(2);
    y_r(n_point) = z_Pos(n_point)*R(1)-x_Pos(n_point)*R(3);
    z_r(n_point) = x_Pos(n_point)*R(2)-y_Pos(n_point)*R(1);
end

x_oumiga = 0.05; %Ŀ����ת��ʼ���ٶ�
y_oumiga = 0.2; %
z_oumiga = 0.05;

x_lamda = 0.05; %0.05%Ŀ����ת���ٶȼ��ٶ�
y_lamda = 0.1; %0.1
z_lamda = 0.05; %0.05

x_gamma = 0.05; %0.05%Ŀ����ת���ٶȼӼ��ٶ�
y_gamma = 0.4; %0.4
z_gamma = 0.05; %0.05

f = zeros(1,Num_point);
alpha = zeros(1,Num_point);
beita = zeros(1,Num_point);
for n_point = 1:Num_point
    f(n_point) = x_r(n_point)*x_oumiga+y_r(n_point)*y_oumiga+z_r(n_point)*z_oumiga;
    alpha(n_point) = x_r(n_point)*x_lamda+y_r(n_point)*y_lamda+z_r(n_point)*z_lamda;
    beita(n_point) = x_r(n_point)*x_gamma+y_r(n_point)*y_gamma+z_r(n_point)*z_gamma;
end

%---------------------------�״������ʱ����ز�---------------------------%
B = 80*1e6;  %����
c = 3*1e8; 
PRF = 1400; %�����ظ�Ƶ��
fc = 5.2*1e9; %��Ƶ
delta_r = c/(2*B);
r = -50*delta_r:delta_r:50*delta_r;
tm = 0:(1/PRF):0.501;
Num_r = length(r);
Num_tm = length(tm);
ones_r = ones(1,Num_r);
ones_tm = ones(1,Num_tm);

s_r_tm = 0; %Խ����У����
for n_point = 1:Num_point
    Delta_R0(n_point) = x_Pos(n_point)*R(1)+y_Pos(n_point)*R(2)+z_Pos(n_point)*R(3);%��ʼʱ�̵ľ�����
    Delta_R = f(n_point).*0+(1/2)*alpha(n_point).*0.*0+(1/6)*beita(n_point).*0.*0.*tm + Delta_R0(n_point);
    sita_Delta_R = 4*pi*(fc/c)*((f(n_point)+0*c/(2*fc)).*tm+(1/2)*alpha(n_point).*0.*0+(1/6)*beita(n_point).*0.*0.*0 + Delta_R0(n_point)); %+4.5����Ŀ����ͼ����λ��
    s_r_tm = s_r_tm + sinc((2*B/c)*(r.'*ones_tm-ones_r.'*Delta_R)).*exp(j*ones_r.'*sita_Delta_R);
end
% figure
% imagesc(flipud(abs(fft(s_r_tm,Num_tm,2))))  %ʵ��ʹ�õĳ�������PRF = 1400; 190--370

tm2 = 0:(1/PRF):0.501;
Num_tm2 = length(tm2);
ones_tm2 = ones(1,Num_tm2);
s_r_tm2 = 0;  %Խ����У����
%% 添加平动参数
% 平动速度分量（根据论文中的Vr和Vt）
V_r = 10;  % 沿雷达视线方向的速度 (m/s)
V_t = 5;   % 垂直于雷达视线方向的速度 (m/s)
psi = 0.1; % 论文中的ψ角
alpha = 0.2; % 论文中的α角（视角）

% 计算平动的速度分量
V_x = V_r * sin(psi) - V_t * cos(alpha);
V_y = V_r * cos(psi) - V_t * sin(alpha);
V_z = 0; % 简化情况下可设为0

% 初始距离
R_0 = 10000; % 目标到雷达的初始距离(m)

%% 修改回波生成循环
s_r_tm2 = 0;  % 回波信号初始化
for n_point = 1:Num_point
    % 目标局部坐标系中的散射点位置
    r_local = [x_Pos(n_point); y_Pos(n_point); z_Pos(n_point)];
    
    % 旋转矩阵初始化
    % 使用论文中的公式(5)和(6)构建旋转矩阵
    
    % 计算每个时间点的回波
    for n_tm = 1:Num_tm2
        t = tm2(n_tm);
        
        % 构建时变旋转矩阵 - 基于论文公式(5)(6)
        theta_x = x_oumiga*t + 0.5*x_lamda*t^2 + (1/6)*x_gamma*t^3;
        theta_y = y_oumiga*t + 0.5*y_lamda*t^2 + (1/6)*y_gamma*t^3;
        theta_z = z_oumiga*t + 0.5*z_lamda*t^2 + (1/6)*z_gamma*t^3;
        
        % 计算旋转矩阵
        Rot_x = [1 0 0; 0 cos(theta_x) -sin(theta_x); 0 sin(theta_x) cos(theta_x)];
        Rot_y = [cos(theta_y) 0 sin(theta_y); 0 1 0; -sin(theta_y) 0 cos(theta_y)];
        Rot_z = [cos(theta_z) -sin(theta_z) 0; sin(theta_z) cos(theta_z) 0; 0 0 1];
        Rot = Rot_z * Rot_y * Rot_x;
        
        % 计算平动分量 - 基于公式中的x_trans(tm)
        x_trans = [V_x*t; V_y*t; V_z*t];
        
        % 计算散射点在雷达坐标系中的位置（包含旋转和平动）
        p_radar = Rot * r_local + x_trans;
        
        % 计算到雷达的距离（基于公式(14)）
        Delta_R_vec(n_tm) = norm(p_radar);
        
        % 计算相位（基于公式(15)）
        sita_Delta_R_new(n_tm) = 4*pi*(fc/c)*Delta_R_vec(n_tm);
    end
    
    % 使用计算的距离和相位生成回波
    Delta_R = Delta_R_vec;
    sita_Delta_R = sita_Delta_R_new;
    
    % 生成回波信号（保持原有的信号强度逻辑）
    if n_point>53 & n_point<62
        s_r_tm2 = s_r_tm2 + 1.3*sinc((2*B/c)*(r.'*ones_tm2-ones_r.'*Delta_R)).*exp(j*ones_r.'*sita_Delta_R);
    else
        s_r_tm2 = s_r_tm2 + sinc((2*B/c)*(r.'*ones_tm2-ones_r.'*Delta_R)).*exp(j*ones_r.'*sita_Delta_R);        
    end
    
    if n_point == 48
        s_r_tm2 = s_r_tm2 + 1*sinc((2*B/c)*(r.'*ones_tm2-ones_r.'*Delta_R)).*exp(j*ones_r.'*sita_Delta_R);
    end
end
%---------------------------- ����ƽ�Ƶ��� --------------------------------%
% s_phase_comp1 = exp(j*ones_r.'*(2*pi*((1/2)*40.*tm2.*tm2+(1/6)*400.*tm2.*tm2.*tm2)));  %��������λ����ֹ���ָ�ֵ��ʵ�ʳ�����
% % s_phase_comp =exp(j*ones_r.'*(2*pi*(190.*tm2+(1/2)*40.*tm2.*tm2+(1/6)*400.*tm2.*tm2.*tm2)));  %��������λ����ֹ���ָ�ֵ��ʵ�ʳ�����
% s_phase_comp =exp(j*ones_r.'*(2*pi*(190.*tm2))); %ֻ�г���Doppler������
% s_r_tm2 = s_r_tm2.*s_phase_comp.*s_phase_comp1; %
% % figure
% imagesc(flipud(abs(fft(s_r_tm2,Num_tm,2))))
%-------------------------------------------------------------------------%

%-----------------------------    ����      -----------------------------%
Mu = 0;
Sigmma = sqrt(10.^(1.5));
n_t_tm = normrnd(Mu,Sigmma,Num_r,Num_tm2); %ϵ��(1/length(r_profile))ΪIDFT���
n_r_t = fft(n_t_tm,Num_r)./Num_r;
% s_r_tm2 = s_r_tm2 + n_r_t; %�൱��ԭʼ�ز���������
%-------------------------------------------------------------------------%

%-----------------���ÿһ��ɢ����Ƶ�ʣ���Ƶ�ʼ���Ƶ�ʱ仯��---------------%
[r_max,n_r_max]=max(abs(fft(s_r_tm.')));
% for n_point = 1:Num_point
%     f_real(n_point) = 2*(fc/c)*f(n_point)+190 + 79/700*1400;
%     alpha_real(n_point) = 2*(fc/c)*alpha(n_point)+40;
%     beita_real(n_point) = 2*(fc/c)*beita(n_point)+400;
% 
%     pos_f(n_point) = round(f_real(n_point)*Num_tm/PRF)+1;
%     pos_r(n_point) = ((Delta_R0(n_point)+50*delta_r)/delta_r)+1;
%     pos_f2 = pos_f.';
%     pos_r2 = pos_r.';
% end
%------------------------------------------------------------------------%
 s_r_tm2(35,:) = 0;
 s_r_tm2
%------------------------  ������Ϊ����ʱ�ĳ�����  ---------------------%
R_D = abs((fft(s_r_tm2,Num_tm,2)));
R_D(1:2,:) = [];
R_D(Num_r-1-2:Num_r-2,:) = [];
R_D = horzcat(R_D(:,Num_tm2-79:Num_tm2),R_D(:,1:Num_tm2-80));
%%%%Ϊ�˷������·���չʾ�������ĳ�����
R_D(51,:) = 1.2*R_D(51,:);
R_D(51,160:210) = 0.4*R_D(51,160:210); %Ϊ�˺�MDCFT�Ա�
R_D(43,:) = 0.6*R_D(43,:);
R_D(43,325:355) = 0.4*R_D(43,325:355);

% figure
% imagesc(flipud(abs(s_r_tm2)))
% R_D_max = max(max(R_D));
% R_D = R_D_max-R_D;
% figure
% imagesc(flipud(R_D./R_D_max))
figure('name','ISAR');
G1=20*log10(abs(R_D)/max(abs(R_D(:))));
imagesc(G1);caxis([-30 0]);
%axis equal;axis([-0.6 0.6 -0.6 0.6]);
xlabel('Azimuth cells');ylabel('Range cells');
grid on;axis xy;%set(gca,'XDir','reverse');
% set(gca,'xtick',[-0.6 -0.4 -0.2 0 0.2 0.4 0.6]);
% set(gca,'ytick',[-0.6 -0.4 -0.2 0 0.2 0.4 0.6]);
colorbar;colormap jet;

figure
plot(1:length(R_D(51,:)),abs(R_D(51,:)))
figure
plot(1:length(R_D(43,:)),abs(R_D(43,:)))
