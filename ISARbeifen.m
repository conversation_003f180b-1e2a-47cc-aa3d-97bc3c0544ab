

clc;
clear all;

%% r -> Real B-727 data.Motion compensation & range processing has been applied to the data.

 load YAK.mat
 load shipx2.mat
 load s_r_tm2.mat
 % load datafangzhen.mat
 % load ximg1.mat
 %load D922.mat
%y    = y./max(max(y));

WN=4;%window length is assumed as even. (4)

% y=y(:,64*2+1:64*3);
% y    = y./max(max(y));

X1=shipx2;%%yak
% X1=ret_data;%%D901

%X1=X;
% [na,nr] = size(X1); 
% nstd    = .505*0.03*sqrt(2);    %1.57 
% noise = random('normal',0,nstd,na,nr) + 1j*random('normal',0,nstd,na,nr) ;
% SNR = db(norm(y,'fro')^2/(var(noise(:))*na*nr))/2;
% fprintf('SNR=%d;\n',SNR)
% X1=X1+noise;

[N,M] = size(X1);
[na,nr] = size(X1);     %
% X1=X(1:2:64,1:32);
% X1=X(1:4:64,449:512);
% f0=9e9;
% c=3e8;
% B=0.512e9;
% %X1 = Keystone(X1,B,f0);

[m,n]=size(X1);
w=hammwin(WN);%%Hamming Window is better for this.

Xf=fft(X1,[],2);%% Take FFT along Rowwise (2).
[row, len]=size(Xf);

xtemp=Xf;
Xf=zeros(len,len+WN);%% Pad zeros to the start as well as end, to reduce the effect
Xf(1:row,WN/2+1:len+WN/2)=xtemp;%% of neglecting last window length points.
[row, len]=size(Xf);

[row, len] = size(Xf);
Xf = Xf(:, (WN/2 + 1):(len - WN/2)); % Remove the padded zeros before processing
[row, len] = size(Xf);

for i1=1:m;
    xf=Xf(i1,:);
    wx=isar_hwtmapf1(xf,w,len);
    V((i1-1)*len+1:i1*len, :) = wx;i1
end

V=fftshift(V,2);

[m,n]=size(V);
m2 = m/n;
m1 = n;

% figure
for i1=1:1:n
    for k1=1:1:m2
        temp(k1,:)=V(i1+(k1-1)*m1,:);
    end           
    t3 = temp;i1

end
temp1=t3;

figure('name','ISAR');
G1=20*log10(abs(temp)/max(abs(temp(:))));
imagesc(G1);caxis([-50 0]);
%axis equal;axis([-0.6 0.6 -0.6 0.6]);
xlabel('Azimuth cells');ylabel('Range cells');
grid on;axis xy;%set(gca,'XDir','reverse');
% set(gca,'xtick',[-0.6 -0.4 -0.2 0 0.2 0.4 0.6]);
% set(gca,'ytick',[-0.6 -0.4 -0.2 0 0.2 0.4 0.6]);
colorbar;colormap jet;
e=EntropyImage(t3+eps);
c=contrast(t3)
% 


% % 提取指定行的数据（第179行）
% row_data = G1(179,:);
% 
% % 处理-inf值
% row_data(isinf(row_data)) = min(row_data(~isinf(row_data)));
% 
% % 归一化处理
% normalized_data = (row_data - min(row_data)) / (max(row_data) - min(row_data));
% 
% % 创建方位单元格向量
% azimuth_cells = 1:length(normalized_data);

% % 绘制方位剖面图
% figure('Name', 'Azimuth Profile');
% plot(azimuth_cells, normalized_data, 'b-', 'LineWidth', 1.5);
% grid on;
% xlabel('Azimuth Cells');
% ylabel('Normalized Amplitude');
% title('Normalized Azimuth Profile at Range Cell 179');
% 
% % 计算-3dB带宽
% threshold_3dB = max(normalized_data) - 3/20; % 考虑到dB刻度
% above_threshold = normalized_data >= threshold_3dB;
% first_point = find(above_threshold, 1, 'first');
% last_point = find(above_threshold, 1, 'last');
% resolution = last_point - first_point;

% % 在图上标注-3dB带宽位置
% hold on;
% plot([first_point, last_point], [normalized_data(first_point), normalized_data(last_point)], 'ro');
% plot([first_point, last_point], [threshold_3dB, threshold_3dB], 'r--');
% text(mean([first_point, last_point]), threshold_3dB + 0.1, ...
%     ['\Delta\theta_{-3dB} = ', num2str(resolution), ' cells'], ...
%     'HorizontalAlignment', 'center');
% hold off;

% 输出分辨率信息
%fprintf('方位向分辨率: %d cells\n', resolution);
% 
% function z=isar_hwtmapf1(xf,w,len)
%     k=length(w);
%     half_k = floor(k / 2);
%     z = zeros(len, len);
% 
%     % 对每个时间点构建谐波小波变换
%     for ii=1:len
%         % 获取当前时间窗口的信号
%         start_idx = max(1, ii - half_k);
%         end_idx = min(len, ii + half_k - 1);
%         curr_sig = zeros(1, len);
%         curr_sig(start_idx:end_idx) = xf(start_idx:end_idx);
% 
%         % 对当前时间窗口构建谐波小波基矩阵
%         H = zeros(len, end_idx-start_idx+1);
%         for m = 1:len  % 频率点
%             for n = 1:(end_idx-start_idx+1)  % 时间点
%                 t = start_idx + n - 2;  % 实际时间索引
%                 f = m - 1;  % 频率索引
%                 % 构建基函数：窗口函数 * 复指数项
%                 H(m,n) = w(n) * exp(-1i*2*pi*f*t/len);
%             end
%         end
% 
%         % 应用谐波小波变换
%         windowed_sig = curr_sig(start_idx:end_idx);
%         temp = H * windowed_sig.';
%         z(:,ii) = abs(temp).^2;  % 保持原代码的平方操作
%     end
% end


... existing code ...

% 修改后的谐波小波变换函数
function z = isar_hwtmapf1(xf, w, len)
    k = length(w);
    half_k = floor(k / 2);
    z = zeros(len, len);

    % 添加自适应参数
    alpha = 0.01;  % 自适应步长
    beta = 0.5;    % 正则化参数

    % 对每个时间点构建自适应谐波小波变换
    for ii = 1:len
        % 获取当前时间窗口的信号
        start_idx = max(1, ii - half_k);
        end_idx = min(len, ii + half_k - 1);
        curr_sig = zeros(1, len);
        curr_sig(start_idx:end_idx) = xf(start_idx:end_idx);

        % 计算信号能量
        signal_energy = sum(abs(curr_sig(start_idx:end_idx)).^2);

        % 构建自适应基矩阵
        H = zeros(len, end_idx-start_idx+1);
        for m = 1:len
            for n = 1:(end_idx-start_idx+1)
                t = start_idx + n - 2;
                f = m - 1;

                % 计算自适应权重
                weight = 1 / (1 + beta * exp(-alpha * signal_energy));

                % 增强的基函数
                phase = -2*pi*f*t/len;
                H(m,n) = weight * w(n) * exp(1i*phase);

                % 添加频率调制项
                freq_mod = exp(-alpha * abs(f-len/2)/len);
                H(m,n) = H(m,n) * freq_mod;
            end
        end

        % 正则化基矩阵
        H = H ./ (norm(H, 'fro') + eps);

        % 应用自适应谐波小波变换
        windowed_sig = curr_sig(start_idx:end_idx);

        % 添加噪声抑制
        noise_threshold = 0.1 * max(abs(windowed_sig));
        windowed_sig(abs(windowed_sig) < noise_threshold) = 0;

        % 计算变换系数
        temp = H * windowed_sig.';

        % 非线性增强
        z(:,ii) = abs(temp).^2 .* (1 + tanh(abs(temp)));
    end


end








% function z = isar_hwtmapf1(xf, w, len)
%     k = length(w);
%     half_k = floor(k / 2);
%     z = zeros(len, len);
% 
%     % 恢复更保守的ADMM参数
%     max_iter = 100;    % 恢复迭代次数
%     rho = 1.0;        % 更稳定的步长
%     lambda = 0.1;     % 保持原有正则化强度
% 
%     % 优化自适应参数
%     alpha = 0.01;
%     beta = 0.5;
%     gamma = 0.1;      % 新增：控制非线性增强强度
% 
%     % 预计算
%     F = dftmtx(len)/sqrt(len);
%     freq_mod_matrix = exp(-alpha * abs((1:len)-len/2)/len);
% 
%     % 并行处理
%     parfor ii = 1:len
%         % 获取窗口数据
%         start_idx = max(1, ii - half_k);
%         end_idx = min(len, ii + half_k - 1);
%         y = xf(start_idx:end_idx).';
% 
%         % 增强的信号能量计算
%         signal_energy = sum(abs(y).^2);
%         local_snr = signal_energy / (std(y) + eps);
% 
%         % 改进的自适应基矩阵构建
%         H = enhanced_adaptive_basis(F, w, start_idx, end_idx, len, ...
%                                   alpha, beta, signal_energy, ...
%                                   freq_mod_matrix, local_snr);
% 
%         % 稳定的矩阵计算
%         HtH = H'*H;
%         Hty = H'*y;
% 
%         % 数值稳定的Cholesky分解
%         [L, flag] = chol(HtH + rho*eye(len), 'lower');
%         if flag ~= 0
%             % 使用更稳定的SVD分解作为备选
%             [U, S, ~] = svd(HtH + rho*eye(len));
%             L = U * sqrt(S);
%         end
% 
%         % ADMM迭代
%         x = zeros(len, 1);
%         z_var = zeros(len, 1);
%         u = zeros(len, 1);
%         prev_x = x;
% 
%         for iter = 1:max_iter
%             % 更新x
%             b = Hty + rho*(z_var - u);
%             x = L' \ (L \ b);
% 
%             % 改进的软阈值处理
%             z_temp = x + u;
%             z_var = adaptive_soft_threshold(z_temp, lambda/rho, local_snr);
% 
%             % 更新u
%             u = u + (x - z_var);
% 
%             % 改进的收敛检查
%             rel_change = norm(x - prev_x) / (norm(prev_x) + eps);
%             if mod(iter, 5) == 0 && rel_change < 1e-4
%                 break;
%             end
%             prev_x = x;
%         end
% 
%         % 改进的非线性增强
%         z(:,ii) = enhanced_power_spectrum(x, gamma, local_snr);
%     end
% 
% end
% 
% function H = enhanced_adaptive_basis(F, w, start_idx, end_idx, len, ...
%                                    alpha, beta, energy, freq_mod, snr)
%     window_length = end_idx - start_idx + 1;
% 
%     % 改进的自适应权重计算
%     weight = 1 / (1 + beta * exp(-alpha * energy));
%     weight = weight * (1 + tanh(snr/100));  % SNR自适应
% 
%     % 构建基矩阵
%     indices = start_idx:end_idx;
%     H = weight * (w(1:window_length)' .* F(indices,:)) .* freq_mod;
% 
%     % 改进的正则化
%     norm_H = sqrt(sum(sum(abs(H).^2)));
%     if norm_H > eps
%         H = H / norm_H;
%     end
% end
% 
% function z = adaptive_soft_threshold(x, thresh, snr)
%     % SNR自适应的软阈值
%     adaptive_thresh = thresh * exp(-snr/100);
%     z = sign(x) .* max(abs(x) - adaptive_thresh, 0);
% 
%     % 保持大信号分量
%     strong_signals = abs(x) > 3*std(abs(x));
%     z(strong_signals) = x(strong_signals);
% end
% 
% function spectrum = enhanced_power_spectrum(x, gamma, snr)
%     % 改进的功率谱计算
%     base_spectrum = abs(x).^2;
%     enhancement = 1 + gamma * tanh(abs(x) * snr/100);
%     spectrum = base_spectrum .* enhancement;
% end
% 
% 
% 
% function F = dftmtx(n)
%     [i, j] = ndgrid(0:n-1);
%     F = exp(-2*pi*1i*i.*j/n) / sqrt(n);
% end

% Modified Harmonic Wavelet Transform Function with ADMM Optimization
%%%%%%%%%%%LADMM
