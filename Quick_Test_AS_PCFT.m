%% Quick_Test_AS_PCFT - 快速测试AS-PCFT修复
%
% 验证修复后的AS-PCFT算法是否能正常运行

clear; close all; clc;

fprintf('=== AS-PCFT快速测试 ===\n\n');

%% 测试1：参数结构创建
fprintf('测试1: 参数结构创建...\n');

try
    % 测试演示脚本中的参数创建函数
    params = get_optimized_as_pcft_params();
    fprintf('  ✓ 参数结构创建成功\n');
    fprintf('  - 雷达PRF: %d Hz\n', params.radar.PRF);
    if isfield(params, 'preprocessing')
        fprintf('  - 预处理字段: 存在\n');
    else
        fprintf('  - 预处理字段: 缺失\n');
    end
    if isfield(params, 'processing')
        fprintf('  - 处理字段: 存在\n');
    else
        fprintf('  - 处理字段: 缺失\n');
    end
catch ME
    fprintf('  ✗ 参数结构创建失败: %s\n', ME.message);
    return;
end

%% 测试2：数据预处理
fprintf('\n测试2: 数据预处理...\n');

try
    % 创建测试数据
    test_data = randn(16, 32) + 1j*randn(16, 32);

    % 测试预处理函数
    processed_data = preprocess_echo_data(test_data, params);

    fprintf('  ✓ 数据预处理成功\n');
    fprintf('  - 输入维度: %d × %d\n', size(test_data, 1), size(test_data, 2));
    fprintf('  - 输出维度: %d × %d\n', size(processed_data, 1), size(processed_data, 2));
catch ME
    fprintf('  ✗ 数据预处理失败: %s\n', ME.message);
    return;
end

%% 测试3：多阶PCFT核函数
fprintf('\n测试3: 多阶PCFT核函数...\n');

try
    % 创建简单测试信号
    N = 32;
    tm_norm = (0:N-1) / N;
    test_signal = exp(1j * 2*pi * (0.1*tm_norm + 0.5*10*tm_norm.^2));
    phase_params = [0.1, 10, 0, 0];

    % 测试PCFT变换
    [X_pcft, comp_time] = Multi_Order_PCFT_Core(test_signal, phase_params, tm_norm, 2);

    fprintf('  ✓ 多阶PCFT核函数成功\n');
    fprintf('  - 计算时间: %.4f秒\n', comp_time);
    fprintf('  - 输出长度: %d\n', length(X_pcft));
    fprintf('  - 峰值比: %.2f\n', max(abs(X_pcft)) / mean(abs(X_pcft)));
catch ME
    fprintf('  ✗ 多阶PCFT核函数失败: %s\n', ME.message);
    fprintf('    错误位置: %s (行 %d)\n', ME.stack(1).name, ME.stack(1).line);
end

%% 测试4：原子范数最小化（简化版）
fprintf('\n测试4: 原子范数最小化...\n');

try
    % 创建多原子测试信号
    N_test = 32;
    tm_test = (0:N_test-1) / N_test;
    multi_atom_signal = exp(1j * 2*pi * 0.1*tm_test) + 0.5*exp(1j * 2*pi * (-0.2*tm_test + 5*tm_test.^2));
    multi_atom_signal = multi_atom_signal + 0.1*(randn(1,N_test) + 1j*randn(1,N_test));

    % 简化的原子范数参数
    atomic_params = struct();
    atomic_params.island_threshold_ratio = 0.1;
    atomic_params.max_atoms = 5;
    atomic_params.max_iterations = 10; % 减少迭代次数
    atomic_params.tolerance = 1e-4;
    atomic_params.lambda_atomic = 0.01;
    atomic_params.step_size_amplitude = 0.01;
    atomic_params.step_size_phase = 0.001;
    atomic_params.alpha_min = -50; atomic_params.alpha_max = 50;
    atomic_params.beta_min = -100; atomic_params.beta_max = 100;
    atomic_params.gamma_min = -500; atomic_params.gamma_max = 500;
    atomic_params.island_merge_distance = 3;
    atomic_params.local_window_size = 16;
    atomic_params.init_noise_level = 0.01;

    [A_optimal, a_optimal, convergence_info] = Atomic_Norm_Minimization(multi_atom_signal, atomic_params);

    fprintf('  ✓ 原子范数最小化成功\n');
    fprintf('  - 检测原子数: %d\n', size(a_optimal, 1));
    if convergence_info.converged
        fprintf('  - 收敛状态: 收敛\n');
    else
        fprintf('  - 收敛状态: 未收敛\n');
    end
    fprintf('  - 迭代次数: %d\n', convergence_info.iterations);
catch ME
    fprintf('  ✗ 原子范数最小化失败: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('    错误位置: %s (行 %d)\n', ME.stack(1).name, ME.stack(1).line);
    end
end

%% 测试5：稀疏-低秩正则化（简化版）
fprintf('\n测试5: 稀疏-低秩正则化...\n');

try
    % 创建测试信号
    N_sparse = 32;
    test_signal_sparse = zeros(1, N_sparse, 'like', 1j);
    test_signal_sparse([5, 15, 25]) = [2, 1.5, 1.8];
    test_signal_sparse = test_signal_sparse + 0.2*(randn(1,N_sparse) + 1j*randn(1,N_sparse));

    % 简化的稀疏-低秩参数
    sparse_lr_params = struct();
    sparse_lr_params.algorithm = 'admm';
    sparse_lr_params.lambda_sparse = 0.1;
    sparse_lr_params.lambda_lowrank = 0.05;
    sparse_lr_params.max_iterations = 10; % 减少迭代次数
    sparse_lr_params.tolerance = 1e-3;
    sparse_lr_params.rho = 1.0;
    sparse_lr_params.hankel_size = 16;

    [X_optimal, convergence_info] = Sparse_LowRank_Regularization(test_signal_sparse, test_signal_sparse, sparse_lr_params);

    fprintf('  ✓ 稀疏-低秩正则化成功\n');
    if convergence_info.converged
        fprintf('  - 收敛状态: 收敛\n');
    else
        fprintf('  - 收敛状态: 未收敛\n');
    end
    fprintf('  - 迭代次数: %d\n', convergence_info.iterations);
    fprintf('  - 最终误差: %.2e\n', convergence_info.final_error);
catch ME
    fprintf('  ✗ 稀疏-低秩正则化失败: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('    错误位置: %s (行 %d)\n', ME.stack(1).name, ME.stack(1).line);
    end
end

%% 测试6：AS-PCFT完整流程（最小规模）
fprintf('\n测试6: AS-PCFT完整流程...\n');

try
    % 创建最小规模的测试数据
    N_range_small = 8;
    N_azimuth_small = 16;
    echo_small = zeros(N_range_small, N_azimuth_small, 'like', 1j);

    % 添加一个简单的散射点
    tm_small = (0:N_azimuth_small-1) / 1000;
    phase_simple = 2*pi * (0.05*tm_small + 0.5*10*tm_small.^2);
    echo_small(4, :) = exp(1j * phase_simple);

    % 添加少量噪声
    echo_small = echo_small + 0.1*(randn(N_range_small, N_azimuth_small) + 1j*randn(N_range_small, N_azimuth_small));

    % 简化的AS-PCFT参数
    params_small = params;
    params_small.processing.block_size = 4;
    params_small.atomic.max_iter = 5;
    params_small.sparse_lr.max_iter = 5;
    params_small.coarse.alpha_step = 10;
    params_small.coarse.beta_step = 100;

    fprintf('  运行AS-PCFT完整算法（小规模）...\n');
    tic;
    [ISAR_image_small, processing_info_small] = AS_PCFT_ISAR_Framework(echo_small, params_small);
    total_time_small = toc;

    fprintf('  ✓ AS-PCFT完整流程成功\n');
    fprintf('  - 处理时间: %.2f秒\n', total_time_small);
    fprintf('  - 输出维度: %d × %d\n', size(ISAR_image_small, 1), size(ISAR_image_small, 2));
    fprintf('  - 平均重构误差: %.2e\n', processing_info_small.mean_error);

    % 计算简单的质量指标
    image_mag = abs(ISAR_image_small);
    contrast_small = std(image_mag(:)) / mean(image_mag(:));
    peak_ratio_small = max(image_mag(:)) / mean(image_mag(:));

    fprintf('  - 图像对比度: %.2f\n', contrast_small);
    fprintf('  - 峰值比: %.2f\n', peak_ratio_small);

catch ME
    fprintf('  ✗ AS-PCFT完整流程失败: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('    错误位置: %s (行 %d)\n', ME.stack(1).name, ME.stack(1).line);
        if length(ME.stack) > 1
            fprintf('    调用链: %s (行 %d)\n', ME.stack(2).name, ME.stack(2).line);
        end
    end
end

%% 测试总结
fprintf('\n=== AS-PCFT快速测试完成 ===\n');

if exist('ISAR_image_small', 'var')
    fprintf('✓ 所有核心功能测试通过\n');
    fprintf('✓ AS-PCFT算法可以正常运行\n');
    fprintf('✓ 修复成功，算法已就绪\n');
else
    fprintf('⚠ 部分功能存在问题，需要进一步调试\n');
end

%% 包含演示脚本中的参数函数
function params = get_optimized_as_pcft_params()
% 获取优化的AS-PCFT参数

% 直接定义参数而不是调用不存在的函数
params = struct();

% 雷达参数
params.radar.PRF = 1000; % 脉冲重复频率

% 预处理参数
params.preprocessing.remove_dc = true;
params.preprocessing.range_alignment = false;
params.preprocessing.apply_window = true;

% 处理参数
params.processing.block_size = 50; % 分块大小
params.energy_threshold = 1e-8; % 能量阈值

% 粗聚焦参数
params.coarse.alpha_min = -50;
params.coarse.alpha_max = 50;
params.coarse.alpha_step = 5;
params.coarse.beta_min = -500;
params.coarse.beta_max = 500;
params.coarse.beta_step = 50;

% 原子范数参数
params.atomic.max_iter = 100;
params.atomic.tolerance = 1e-6;
params.atomic.refinement_factor = 10;

% 稀疏-低秩参数
params.sparse_lr.lambda_sparse = 0.1;
params.sparse_lr.lambda_lowrank = 0.05;
params.sparse_lr.max_iter = 50;
params.sparse_lr.tolerance = 1e-4;

% 后处理参数
params.postprocessing.contrast_enhancement = true;

% 针对演示优化的参数
params.coarse.alpha_step = 8;        % 粗搜索步长
params.coarse.beta_step = 80;        % 粗搜索步长
params.atomic.max_iter = 50;         % 原子范数迭代次数
params.sparse_lr.max_iter = 30;      % 稀疏-低秩迭代次数
params.processing.block_size = 25;   % 分块大小

% 正则化参数调整
params.sparse_lr.lambda_sparse = 0.05;
params.sparse_lr.lambda_lowrank = 0.02;
end
