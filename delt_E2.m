function ret_data = delt_E2(data_image,hrrp,fai_old)
%图像熵对fai的二阶偏导
[N,M] = size(hrrp);
% for m = 1:M
%     hrrp_back1(:,m) = sum(data_image.^2./abs(data_image).^2.*exp(j*4*pi/M.*(ones(N,1)*[0:M-1])*m),2);
% end
% ret_data1 = -2*real(sum(hrrp_back1.*conj(hrrp).^2.*(ones(N,1)*exp(-j*2.*fai_old)),1));
hrrp_back2 = sum((2+log(abs(data_image).^2)),2)*ones(1,M);
ret_data2 = -2*sum(hrrp_back2.*abs(hrrp).^2,1);
hrrp_back3 = fty((1+log(abs(data_image).^2)).*conj(data_image));
ret_data3 = 2*real(sum(hrrp_back3.*hrrp.*(ones(N,1)*exp(j*fai_old)),1));
ret_data = ret_data2+ret_data3;
end