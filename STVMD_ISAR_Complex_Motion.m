function [ISAR_image, decomposed_modes, phase_errors, analysis_results] = STVMD_ISAR_Complex_Motion(s_r_tm, params)
% STVMD_ISAR_Complex_Motion - 使用STVMD处理复杂运动的ISAR成像
%
% 输入:
%   s_r_tm - 距离压缩后的ISAR数据 (Nr x Ntm)
%   params - 参数结构体
%
% 输出:
%   ISAR_image - 最终的ISAR图像
%   decomposed_modes - 分解得到的模式
%   phase_errors - 估计的相位误差
%   analysis_results - 中间分析结果

% 默认参数
if nargin < 2
    params = struct();
end

% STVMD参数
K = getfield_default(params, 'K', 3);                    % 模式数量
alpha = getfield_default(params, 'alpha', 2000);        % 平衡参数
tau = getfield_default(params, 'tau', 0.1);             % 双上升时间步长
tol = getfield_default(params, 'tol', 1e-7);            % 收敛容限
max_iter = getfield_default(params, 'max_iter', 500);   % 最大迭代次数
window_length = getfield_default(params, 'window_length', 64); % STFT窗口长度
overlap = getfield_default(params, 'overlap', 0.75);    % 窗口重叠率

[Nr, Ntm] = size(s_r_tm);

% 初始化输出
s_compensated = zeros(Nr, Ntm, 'like', 1j);
decomposed_modes = cell(Nr, 1);
phase_errors = zeros(Nr, Ntm);
analysis_results = struct();

% 存储分析结果
analysis_results.instantaneous_freq = cell(Nr, 1);
analysis_results.mode_energies = zeros(Nr, K);
analysis_results.center_frequencies = zeros(Nr, K);

fprintf('开始STVMD-ISAR处理 (共 %d 个距离单元)...\n', Nr);

% 对每个距离单元进行处理
for r_idx = 1:Nr
    if mod(r_idx, round(Nr/10)) == 0 || r_idx == 1 || r_idx == Nr
        fprintf('处理距离单元: %d / %d\n', r_idx, Nr);
    end
    
    % 获取当前距离单元的信号
    signal_r = s_r_tm(r_idx, :);
    
    % 执行STVMD分解
    [modes, omega_k, IF_modes] = STVMD_decompose(signal_r, K, alpha, tau, tol, max_iter, window_length, overlap);
    
    % 存储分解结果
    decomposed_modes{r_idx} = modes;
    analysis_results.center_frequencies(r_idx, :) = omega_k;
    analysis_results.instantaneous_freq{r_idx} = IF_modes;
    
    % 计算模式能量
    for k = 1:K
        analysis_results.mode_energies(r_idx, k) = sum(abs(modes(k, :)).^2);
    end
    
    % 相位误差估计 - 使用主导模式
    [~, dominant_idx] = max(analysis_results.mode_energies(r_idx, :));
    phase_error = estimate_phase_error_cubic(modes(dominant_idx, :), omega_k(dominant_idx), IF_modes{dominant_idx});
    phase_errors(r_idx, :) = phase_error;
    
    % 相位补偿
    s_compensated(r_idx, :) = signal_r .* exp(-1j * phase_error);
end

% 方位向FFT成像
ISAR_image = fftshift(fft(s_compensated, Ntm, 2), 2);

fprintf('STVMD-ISAR处理完成。\n');

% 生成分析图
generate_analysis_plots(s_r_tm, decomposed_modes, analysis_results, ISAR_image);

end

function [modes, omega_k, IF_modes] = STVMD_decompose(signal, K, alpha, tau, tol, max_iter, window_length, overlap)
% STVMD分解核心函数

N = length(signal);
modes = zeros(K, N, 'like', 1j);
IF_modes = cell(K, 1);

% STFT参数
hop_size = round(window_length * (1 - overlap));
num_windows = floor((N - window_length) / hop_size) + 1;
window = hamming(window_length);

% 初始化
U_k_stft = zeros(K, window_length, num_windows, 'like', 1j);
omega_k = linspace(0, 0.5, K+2);
omega_k = omega_k(2:end-1); % 初始中心频率

% 频率轴
freq_axis = (0:window_length-1) / window_length;

% STFT变换
signal_stft = zeros(window_length, num_windows, 'like', 1j);
for w_idx = 1:num_windows
    start_idx = (w_idx - 1) * hop_size + 1;
    end_idx = start_idx + window_length - 1;
    if end_idx <= N
        windowed_signal = signal(start_idx:end_idx) .* window.';
        signal_stft(:, w_idx) = fft(windowed_signal);
    end
end

% STVMD迭代
lambda_stft = zeros(window_length, num_windows, 'like', 1j);

for iter = 1:max_iter
    U_k_prev = U_k_stft;
    
    % 更新模式
    for k = 1:K
        % 计算其他模式之和 - 修复维度问题
        sum_U_j = zeros(window_length, num_windows, 'like', 1j);
        for j = 1:K
            if j ~= k
                % 正确处理维度，避免使用squeeze可能导致的维度问题
                for w_idx = 1:num_windows
                    % 明确指定维度，不使用squeeze
                    u_j_window = U_k_stft(j, :, w_idx);
                    sum_U_j(:, w_idx) = sum_U_j(:, w_idx) + u_j_window(:);
                end
            end
        end
        
        % 更新U_k
        for w_idx = 1:num_windows
            numerator = signal_stft(:, w_idx) - sum_U_j(:, w_idx) + lambda_stft(:, w_idx)/2;
            denominator = 1 + 2 * alpha * (freq_axis.' - omega_k(k)).^2;
            U_k_stft(k, :, w_idx) = numerator ./ denominator;
        end
    end
    
    % 更新中心频率
    for k = 1:K
        % 修改U_k_current的计算方式，确保维度正确
        freq_weighted = 0;
        total_weight = 0;
        
        for w_idx = 1:num_windows
            % 明确指定维度，不使用squeeze
            u_k_window = U_k_stft(k, :, w_idx);
            abs_U_k_sq = abs(u_k_window(:)).^2;
            
            freq_weighted = freq_weighted + sum(freq_axis.' .* abs_U_k_sq);
            total_weight = total_weight + sum(abs_U_k_sq);
        end
        
        if total_weight > 1e-12
            omega_k(k) = freq_weighted / total_weight;
        end
    end
    
    % 更新拉格朗日乘子 - 修复维度问题
    for w_idx = 1:num_windows
        sum_U_k_w = zeros(window_length, 1, 'like', 1j);
        for k = 1:K
            % 明确指定维度，不使用squeeze
            u_k_window = U_k_stft(k, :, w_idx);
            sum_U_k_w = sum_U_k_w + u_k_window(:);
        end
        lambda_stft(:, w_idx) = lambda_stft(:, w_idx) + tau * (signal_stft(:, w_idx) - sum_U_k_w);
    end
    
    % 检查收敛 - 确保维度正确
    diff_U = 0;
    norm_U_prev = 0;
    
    for k = 1:K
        for w_idx = 1:num_windows
            % 明确指定维度，不使用squeeze
            u_k_curr = U_k_stft(k, :, w_idx);
            u_k_prev = U_k_prev(k, :, w_idx);
            
            diff_U = diff_U + sum(abs(u_k_curr(:) - u_k_prev(:)).^2);
            norm_U_prev = norm_U_prev + sum(abs(u_k_prev(:)).^2);
        end
    end
    
    if norm_U_prev > 1e-12 && (diff_U / norm_U_prev) < tol && iter > 1
        break;
    end
end

% 重构时域模式
for k = 1:K
    mode_reconstructed = zeros(1, N, 'like', 1j);
    for w_idx = 1:num_windows
        start_idx = (w_idx - 1) * hop_size + 1;
        end_idx = start_idx + window_length - 1;
        if end_idx <= N
            % 明确指定维度，不使用squeeze
            u_k_window = U_k_stft(k, :, w_idx);
            mode_window = ifft(u_k_window(:));
            mode_reconstructed(start_idx:end_idx) = mode_reconstructed(start_idx:end_idx) + mode_window.' .* window.';
        end
    end
    modes(k, :) = mode_reconstructed;
    
    % 计算瞬时频率
    analytic_signal = hilbert(real(modes(k, :)));
    inst_phase = unwrap(angle(analytic_signal));
    IF_modes{k} = diff(inst_phase) / (2*pi);
    IF_modes{k} = [IF_modes{k}, IF_modes{k}(end)]; % 补齐长度
end

end

function phase_error = estimate_phase_error_cubic(mode, center_freq, inst_freq)
% 估计立方相位误差

N = length(mode);
t = 0:N-1;

% 使用多项式拟合瞬时频率
if ~isempty(inst_freq)
    % 拟合二次多项式 (对应立方相位)
    p = polyfit(t, inst_freq, 2);
    
    % 积分得到相位误差
    % phi_error = 2*pi * integral(f_fitted - f_center)
    phase_poly = [p(1)/3, p(2)/2, p(3) - center_freq, 0]; % 积分系数
    phase_error = 2*pi * polyval(phase_poly, t);
else
    % 直接从模式相位估计
    phase_error = angle(mode) - 2*pi*center_freq*t;
    phase_error = unwrap(phase_error);
end

% 移除线性趋势
phase_error = phase_error - mean(phase_error);

end

function generate_analysis_plots(s_r_tm, decomposed_modes, analysis_results, ISAR_image)
% 生成分析图表

[Nr, Ntm] = size(s_r_tm);

% 图1: 原始信号和STVMD分解结果
figure('Position', [100, 100, 1200, 800]);

% 选择一个代表性的距离单元
r_idx_demo = round(Nr/2);
signal_demo = s_r_tm(r_idx_demo, :);
modes_demo = decomposed_modes{r_idx_demo};
K = size(modes_demo, 1);

% 子图1: 原始信号
subplot(K+2, 2, 1:2);
plot(real(signal_demo));
title('原始ISAR信号 (距离单元 ' + string(r_idx_demo) + ')');
xlabel('慢时间采样点');
ylabel('幅度');
grid on;

% 子图2-K+1: 各个模式
for k = 1:K
    subplot(K+2, 2, 2*k+1);
    plot(real(modes_demo(k, :)));
    title(['模式 ' num2str(k) ' (中心频率: ' sprintf('%.3f', analysis_results.center_frequencies(r_idx_demo, k)) ')']);
    xlabel('慢时间采样点');
    ylabel('幅度');
    grid on;
    
    subplot(K+2, 2, 2*k+2);
    IF = analysis_results.instantaneous_freq{r_idx_demo}{k};
    plot(IF);
    title(['模式 ' num2str(k) ' 瞬时频率']);
    xlabel('慢时间采样点');
    ylabel('归一化频率');
    grid on;
end

% 子图K+2: 重构信号
subplot(K+2, 2, 2*(K+1)+1:2*(K+2));
reconstructed = sum(modes_demo, 1);
plot(real(reconstructed), 'r', 'LineWidth', 1.5);
hold on;
plot(real(signal_demo), 'b--', 'LineWidth', 1);
legend('重构信号', '原始信号');
title('信号重构对比');
xlabel('慢时间采样点');
ylabel('幅度');
grid on;

% 图2: 模式能量分布
figure('Position', [200, 150, 800, 600]);
imagesc(analysis_results.mode_energies');
colorbar;
xlabel('距离单元');
ylabel('模式编号');
title('各模式能量分布');
colormap('jet');

% 图3: 中心频率演化
figure('Position', [300, 200, 800, 600]);
for k = 1:K
    plot(analysis_results.center_frequencies(:, k), 'LineWidth', 2);
    hold on;
end
xlabel('距离单元');
ylabel('归一化频率');
title('各模式中心频率随距离的变化');
legend(arrayfun(@(x) ['模式 ' num2str(x)], 1:K, 'UniformOutput', false));
grid on;

% 图4: ISAR成像结果对比
figure('Position', [400, 250, 1000, 400]);

% 原始图像 (未补偿)
subplot(1, 2, 1);
ISAR_original = fftshift(fft(s_r_tm, Ntm, 2), 2);
imagesc(20*log10(abs(ISAR_original)/max(abs(ISAR_original(:)))));
caxis([-30, 0]);
colorbar;
xlabel('方位单元');
ylabel('距离单元');
title('原始ISAR图像 (未补偿)');
colormap('jet');
axis xy;

% STVMD补偿后的图像
subplot(1, 2, 2);
imagesc(20*log10(abs(ISAR_image)/max(abs(ISAR_image(:)))));
caxis([-30, 0]);
colorbar;
xlabel('方位单元');
ylabel('距离单元');
title('STVMD补偿后的ISAR图像');
colormap('jet');
axis xy;

end

function value = getfield_default(s, field, default)
% 获取结构体字段值，如果不存在则返回默认值
if isfield(s, field)
    value = s.(field);
else
    value = default;
end
end 