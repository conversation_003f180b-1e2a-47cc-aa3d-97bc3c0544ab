# Testing scripts used during development
This contains various MATLAB scripts used during the development of the QLP.

> All scripts require additional functions which are available in the [algorithms/](https://github.com/tristynferreiro/QP4ISAR/tree/main/src/algorithms) and [helper functions/](https://github.com/tristynferreiro/QP4ISAR/tree/main/src/helper%20functions) folders. These need to be copied into the same path as the QLP scripts for them to work.

> The measured datasets used are not freely available by the CSIR and so a MATLAB structure containing *high range resolution profiles* created using a **stepped-frequency waveform** will need to be added to the same directory inorder to use this script.

## Algorithm Runtime Optimisation/
Contians the MATLAB scripts used to test different versions of the algorithms to ensure that runtime optimisations were successful and that the algorithms still performed correctly.

## Measured Data Testing/
Contains the MATLAB scripts that were used to test each algorithm on a measured dataset. The script reads in a MATLAB structure containing *high range resolution profiles* and the user can select which of the range alignment and autofocus algorithms to perform on the data to produce focused ISAR images. 

## QLP Case Study/
Contains the testing scripts used to perform run time and image focus testing on the final QLP design. The final QLP design is also included as a MATLAB function for ease of testing.

> Any videos produced can be watched using this MATALB script: [videoViewer.m](https://github.com/tristynferreiro/QP4ISAR/blob/main/src/Quick-look%20Processor/videoViewer.m)

## QLP Design Timing/
Contains the MATLAB timing script used to time test each candidate QLP design considered. The final QLP design is also included as a MATLAB function for ease of testing.

## Testing Setup/
This folder contains all the necessary files to run the simulator and test the algorithms with ease. It includes the RA and AF algorithms as well as the function used to calculate image contrast values. 

> The ISAR_Simulator_with_RA_AF.m is an extension of the ISAR_simulator.m which includes the RA and AF algorithms to streamline testing. The CLI has been updated to allow users to select which algorithms, if any, to apply to the simulated profiles.

## Matlab2Tikz
The [matlab2tikz](http://www.mathworks.com/matlabcentral/fileexchange/22022-matlab2tikz-matlab2tikz?download=true) package was used to save the MATLAB plots in LaTeX-compatible file formats
> When dealing with imagesc() plots, follow these steps:
>1. Run matlab2tikz() and save the resulting .tex file.
2. Configure the MATLAB plot to remove all titles, axes, etc.
3. Save only the image as a PNG.
4. Update the .tex file to use the newly created PNG instead of the original one generated by MATLAB.
